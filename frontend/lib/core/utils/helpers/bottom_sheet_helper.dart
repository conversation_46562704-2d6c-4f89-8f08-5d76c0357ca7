import 'package:flutter/material.dart';

import '../../../features/shared/presentation/widgets/generic_bottom_sheet.dart';
import '../../enums/selection_type.dart';

class BottomSheetHelper {
  /// Shows a generic bottom sheet with single selection
  static Future<List<T>?> showSingleSelectionBottomSheet<T>({
    required BuildContext context,
    required String title,
    required List<T> items,
    required String Function(T) getTitle,
    String Function(T)? getSubtitle,
    T? initiallySelectedItem,
    bool useSafeArea = true,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
  }) {
    return showModalBottomSheet<List<T>>(
      context: context,
      isScrollControlled: true,
      useSafeArea: useSafeArea,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      constraints: constraints,
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: GenericBottomSheet<T>(
            title: title,
            items: items,
            selectionType: SelectionType.singleSelection,
            getTitle: getTitle,
            getSubtitle: getSubtitle,
            initiallySelectedItems: initiallySelectedItem != null 
                ? [initiallySelectedItem] 
                : null,
            onSelectionChanged: (selectedItems) {
              Navigator.pop(context, selectedItems);
            },
          ),
        );
      },
    ).then((value) => value ?? []);
  }

  /// Shows a generic bottom sheet with multi selection
  static Future<List<T>?> showMultiSelectionBottomSheet<T>({
    required BuildContext context,
    required String title,
    required List<T> items,
    required String Function(T) getTitle,
    String Function(T)? getSubtitle,
    String Function(T)? getAvatarUrl,
    Widget Function(T)? getLeadingWidget,
    List<T>? initiallySelectedItems,
    bool useSafeArea = true,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
  }) {
    return showModalBottomSheet<List<T>>(
      context: context,
      isScrollControlled: true,
      useSafeArea: useSafeArea,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      constraints: constraints,
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: GenericBottomSheet<T>(
            title: title,
            items: items,
            selectionType: SelectionType.multiSelection,
            getTitle: getTitle,
            getSubtitle: getSubtitle,
            getAvatarUrl: getAvatarUrl,
            getLeadingWidget: getLeadingWidget,
            initiallySelectedItems: initiallySelectedItems,
            onSelectionChanged: (selectedItems) {
              // Selection changes handled internally
            },
          ),
        );
      },
    ).then((value) => value ?? []);
  }

  /// Shows a custom bottom sheet with full configuration options
  static Future<List<T>?> showCustomBottomSheet<T>({
    required BuildContext context,
    required String title,
    required List<T> items,
    required SelectionType selectionType,
    required String Function(T) getTitle,
    String Function(T)? getSubtitle,
    String Function(T)? getAvatarUrl,
    Widget Function(T)? getLeadingWidget,
    List<T>? initiallySelectedItems,
    double heightFactor = 0.8,
    bool useSafeArea = true,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
  }) {
    return showModalBottomSheet<List<T>>(
      context: context,
      isScrollControlled: true,
      useSafeArea: useSafeArea,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      constraints: constraints,
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * heightFactor,
          child: GenericBottomSheet<T>(
            title: title,
            items: items,
            selectionType: selectionType,
            getTitle: getTitle,
            getSubtitle: getSubtitle,
            getAvatarUrl: getAvatarUrl,
            getLeadingWidget: getLeadingWidget,
            initiallySelectedItems: initiallySelectedItems,
            onSelectionChanged: (selectedItems) {
              if (selectionType == SelectionType.singleSelection) {
                Navigator.pop(context, selectedItems);
              }
            },
          ),
        );
      },
    ).then((value) => value ?? []);
  }
}