import 'package:flutter/material.dart';

enum SparePartCategory {
  valve('VALVE'),
  regulator('REGULATOR'),
  hose('HOSE'),
  connector('CONNECTOR'),
  gauge('GAUGE'),
  other('OTHER');

  const SparePartCategory(this.label);
  final String label;
}

extension SparePartCategoryExtension on SparePartCategory {
  String get name {
    switch (this) {
      case SparePartCategory.valve:
        return 'valve';
      case SparePartCategory.regulator:
        return 'regulator';
      case SparePartCategory.hose:
        return 'hose';
      case SparePartCategory.connector:
        return 'connector';
      case SparePartCategory.gauge:
        return 'gauge';
      case SparePartCategory.other:
        return 'other';
    }
  }

  String get displayName {
    switch (this) {
      case SparePartCategory.valve:
        return 'Valve';
      case SparePartCategory.regulator:
        return 'Regulator';
      case SparePartCategory.hose:
        return 'Hose';
      case SparePartCategory.connector:
        return 'Connector';
      case SparePartCategory.gauge:
        return 'Gauge';
      case SparePartCategory.other:
        return 'Other';
    }
  }

  String get description {
    switch (this) {
      case SparePartCategory.valve:
        return 'Gas cylinder valves and valve components';
      case SparePartCategory.regulator:
        return 'Gas pressure regulators and controls';
      case SparePartCategory.hose:
        return 'Gas hoses and flexible connections';
      case SparePartCategory.connector:
        return 'Connectors and fittings';
      case SparePartCategory.gauge:
        return 'Pressure gauges and meters';
      case SparePartCategory.other:
        return 'Other spare parts and accessories';
    }
  }

  IconData get icon {
    switch (this) {
      case SparePartCategory.valve:
        return Icons.settings;
      case SparePartCategory.regulator:
        return Icons.tune;
      case SparePartCategory.hose:
        return Icons.cable;
      case SparePartCategory.connector:
        return Icons.link;
      case SparePartCategory.gauge:
        return Icons.speed;
      case SparePartCategory.other:
        return Icons.category;
    }
  }
}

// Helper function to convert from string
SparePartCategory sparePartCategoryFromString(String category) {
  switch (category.toUpperCase()) {
    case 'VALVE':
      return SparePartCategory.valve;
    case 'REGULATOR':
      return SparePartCategory.regulator;
    case 'HOSE':
      return SparePartCategory.hose;
    case 'CONNECTOR':
      return SparePartCategory.connector;
    case 'GAUGE':
      return SparePartCategory.gauge;
    case 'OTHER':
      return SparePartCategory.other;
    default:
      throw ArgumentError('Unknown SparePartCategory: $category');
  }
}
