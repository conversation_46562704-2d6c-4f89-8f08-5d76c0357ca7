enum CylinderMaterial {
  iron('IRON'),
  plastic('PLASTIC');

  const CylinderMaterial(this.label);
  final String label;
}

extension CylinderMaterialExtension on CylinderMaterial {
  String get name {
    switch (this) {
      case CylinderMaterial.iron:
        return 'iron';
      case CylinderMaterial.plastic:
        return 'plastic';
    }
  }

  String get displayName {
    switch (this) {
      case CylinderMaterial.iron:
        return 'Iron';
      case CylinderMaterial.plastic:
        return 'Plastic';
    }
  }

  String get description {
    switch (this) {
      case CylinderMaterial.iron:
        return 'Durable iron cylinder';
      case CylinderMaterial.plastic:
        return 'Lightweight plastic cylinder';
    }
  }
}

// Helper function to convert from string
CylinderMaterial cylinderMaterialFromString(String material) {
  switch (material.toUpperCase()) {
    case 'IRON':
      return CylinderMaterial.iron;
    case 'PLASTIC':
      return CylinderMaterial.plastic;
    default:
      throw ArgumentError('Unknown CylinderMaterial: $material');
  }
}
