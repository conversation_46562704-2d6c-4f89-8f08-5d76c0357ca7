import 'package:injectable/injectable.dart';

import '../../../../features/user-management/data/repository/user_repository_impl.dart';
import '../../../../features/user-management/data/source/remote/user_remote_datasource.dart';
import '../../../../features/user-management/domain/repository/user_repository.dart';
import '../../../../features/user-management/domain/usecase/delete_user_usecase.dart';
import '../../../../features/user-management/domain/usecase/get_all_users_usecase.dart';
import '../../../../features/user-management/domain/usecase/get_current_user_usecase.dart';
import '../../../../features/user-management/domain/usecase/register_admin_or_agent_usecase.dart';
import '../../../../features/user-management/domain/usecase/subscribe_to_notifications_usecase.dart';
import '../../../../features/user-management/domain/usecase/unsubscribe_from_notifications_usecase.dart';
import '../../../../features/user-management/domain/usecase/update_user_usecase.dart';
import '../../../../features/user-management/presentation/bloc/user_bloc.dart';
import '../../../errors/http_error_handler.dart';
import '../../../network/api_client/dio_api_client.dart';

@module
abstract class UserModule {
  /// ✅ Remote Data Source
  @injectable
  UserRemoteDatasource provideUserRemoteDataSource(
    DioApiClient dioApiClient,
    HttpErrorHandler httpErrorHandler,
  ) {
    return UserRemoteDatasourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
    );
  }

  /// ✅ Local Data Source
  // @injectable
  // UserLocalDataSource provideUserLocalDataSource(
  //   FlutterSecureStorageServices storageServices,
  // ) {
  //   return UserLocalDataSourceImpl(
  //     flutterSecureStorageServices: storageServices,
  //   );
  // }

  /// ✅ Repository (Singleton)
  @lazySingleton
  UserRepository provideUserRepository(
    UserRemoteDatasource remoteDataSource,
    // UserLocalDataSource localDataSource,
  ) {
    return UserRepositoryImpl(
      userRemoteDatasource: remoteDataSource,
      // userLocalDataSource: localDataSource,
    );
  }

  /// ✅ Use Cases (Singleton)
  @lazySingleton
  RegisterAdminOrAgentUseCase provideRegisterAdminOrAgentUseCase(
    UserRepository repository,
  ) {
    return RegisterAdminOrAgentUseCase(repository: repository);
  }

  @lazySingleton
  UpdateUserUseCase provideUpdateUserUseCase(UserRepository repository) {
    return UpdateUserUseCase(repository: repository);
  }

  @lazySingleton
  DeleteUserUseCase provideDeleteUserUseCase(UserRepository repository) {
    return DeleteUserUseCase(repository: repository);
  }

  @lazySingleton
  SubscribeToNotificationsUseCase provideSubscribeToNotificationsUseCase(
    UserRepository repository,
  ) {
    return SubscribeToNotificationsUseCase(repository: repository);
  }

  @lazySingleton
  UnsubscribeFromNotificationsUseCase
  provideUnsubscribeFromNotificationsUseCase(UserRepository repository) {
    return UnsubscribeFromNotificationsUseCase(repository: repository);
  }

  @lazySingleton
  GetAllUsersUseCase provideGetAllUsersUseCase(UserRepository repository) {
    return GetAllUsersUseCase(repository: repository);
  }

  @lazySingleton
  GetCurrentUserUseCase provideGetCurrentUserUseCase(
    UserRepository repository,
  ) {
    return GetCurrentUserUseCase(repository: repository);
  }

  /// ✅ User Bloc (Singleton)
  @lazySingleton
  UserBloc provideUserBloc(
    RegisterAdminOrAgentUseCase registerAdminOrAgentUseCase,
    UpdateUserUseCase updateUserUseCase,
    DeleteUserUseCase deleteUserUseCase,
    SubscribeToNotificationsUseCase subscribeToNotificationsUseCase,
    UnsubscribeFromNotificationsUseCase unsubscribeFromNotificationsUseCase,
    GetAllUsersUseCase getAllUsersUseCase,
    GetCurrentUserUseCase getCurrentUserUseCase,
  ) {
    return UserBloc(
      registerAdminOrAgentUseCase: registerAdminOrAgentUseCase,
      updateUserUseCase: updateUserUseCase,
      deleteUserUseCase: deleteUserUseCase,
      subscribeToNotificationsUseCase: subscribeToNotificationsUseCase,
      unsubscribeFromNotificationsUseCase: unsubscribeFromNotificationsUseCase,
      getAllUsersUseCase: getAllUsersUseCase,
      getCurrentUserUseCase: getCurrentUserUseCase,
    );
  }
}
