import 'package:injectable/injectable.dart';

import '../../../../features/inventory/data/repository/inventory_repository_impl.dart';
import '../../../../features/inventory/data/source/remote/inventory_remote_datasource.dart';
import '../../../../features/inventory/domain/repository/inventory_repository.dart';
import '../../../../features/inventory/domain/usecases/adjust_cylinder_stock_usecase.dart';
import '../../../../features/inventory/domain/usecases/bulk_update_cylinder_status_usecase.dart';
import '../../../../features/inventory/domain/usecases/create_cylinder_usecase.dart';
import '../../../../features/inventory/domain/usecases/create_package_usecase.dart';
import '../../../../features/inventory/domain/usecases/create_spare_part_usecase.dart';
import '../../../../features/inventory/domain/usecases/delete_cylinder_usecase.dart';
import '../../../../features/inventory/domain/usecases/delete_package_usecase.dart';
import '../../../../features/inventory/domain/usecases/delete_spare_part_usecase.dart';
import '../../../../features/inventory/domain/usecases/get_cylinder_by_id_usecase.dart';
import '../../../../features/inventory/domain/usecases/get_cylinders_usecase.dart';
import '../../../../features/inventory/domain/usecases/get_package_by_id_usecase.dart';
import '../../../../features/inventory/domain/usecases/get_packages_usecase.dart';
import '../../../../features/inventory/domain/usecases/get_spare_part_by_id_usecase.dart';
import '../../../../features/inventory/domain/usecases/get_spare_parts_usecase.dart';
import '../../../../features/inventory/domain/usecases/restock_cylinder_usecase.dart';
import '../../../../features/inventory/domain/usecases/restock_package_usecase.dart';
import '../../../../features/inventory/domain/usecases/restock_spare_part_usecase.dart';
import '../../../../features/inventory/domain/usecases/update_cylinder_usecase.dart';
import '../../../../features/inventory/domain/usecases/update_package_usecase.dart';
import '../../../../features/inventory/domain/usecases/update_spare_part_usecase.dart';
import '../../../../features/inventory/domain/usecases/adjust_package_stock_usecase.dart';
import '../../../../features/inventory/domain/usecases/adjust_spare_part_stock_usecase.dart';
import '../../../../features/inventory/domain/usecases/bulk_update_package_status_usecase.dart';
import '../../../../features/inventory/domain/usecases/bulk_update_spare_part_status_usecase.dart';
import '../../../../features/inventory/presentation/bloc/inventory_bloc.dart';
import '../../../errors/http_error_handler.dart';
import '../../../network/api_client/dio_api_client.dart';

@module
abstract class InventoryModule {
  /// ✅ Remote Data Source
  @injectable
  InventoryRemoteDataSource provideInventoryRemoteDataSource(
    DioApiClient dioApiClient,
    HttpErrorHandler httpErrorHandler,
  ) {
    return InventoryRemoteDataSourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
    );
  }

  /// ✅ Repository (Singleton)
  @lazySingleton
  InventoryRepository provideInventoryRepository(
    InventoryRemoteDataSource remoteDataSource,
  ) {
    return InventoryRepositoryImpl(inventoryRemoteDataSource: remoteDataSource);
  }

  /// ✅ Use Cases (Singleton)
  @lazySingleton
  GetCylindersUseCase provideGetCylindersUseCase(
    InventoryRepository repository,
  ) {
    return GetCylindersUseCase(repository: repository);
  }

  @lazySingleton
  GetCylinderByIdUseCase provideGetCylinderByIdUseCase(
    InventoryRepository repository,
  ) {
    return GetCylinderByIdUseCase(repository: repository);
  }

  @lazySingleton
  CreateCylinderUseCase provideCreateCylinderUseCase(
    InventoryRepository repository,
  ) {
    return CreateCylinderUseCase(repository: repository);
  }

  @lazySingleton
  UpdateCylinderUseCase provideUpdateCylinderUseCase(
    InventoryRepository repository,
  ) {
    return UpdateCylinderUseCase(repository: repository);
  }

  @lazySingleton
  DeleteCylinderUseCase provideDeleteCylinderUseCase(
    InventoryRepository repository,
  ) {
    return DeleteCylinderUseCase(repository: repository);
  }

  @lazySingleton
  RestockCylinderUseCase provideRestockCylinderUseCase(
    InventoryRepository repository,
  ) {
    return RestockCylinderUseCase(repository: repository);
  }

  @lazySingleton
  AdjustCylinderStockUseCase provideAdjustCylinderStockUseCase(
    InventoryRepository repository,
  ) {
    return AdjustCylinderStockUseCase(repository: repository);
  }

  @lazySingleton
  BulkUpdateCylinderStatusUseCase provideBulkUpdateCylinderStatusUseCase(
    InventoryRepository repository,
  ) {
    return BulkUpdateCylinderStatusUseCase(repository: repository);
  }

  @lazySingleton
  GetPackagesUseCase provideGetPackagesUseCase(InventoryRepository repository) {
    return GetPackagesUseCase(repository: repository);
  }

  @lazySingleton
  GetPackageByIdUseCase provideGetPackageByIdUseCase(
    InventoryRepository repository,
  ) {
    return GetPackageByIdUseCase(repository: repository);
  }

  @lazySingleton
  CreatePackageUseCase provideCreatePackageUseCase(
    InventoryRepository repository,
  ) {
    return CreatePackageUseCase(repository: repository);
  }

  @lazySingleton
  UpdatePackageUseCase provideUpdatePackageUseCase(
    InventoryRepository repository,
  ) {
    return UpdatePackageUseCase(repository: repository);
  }

  @lazySingleton
  DeletePackageUseCase provideDeletePackageUseCase(
    InventoryRepository repository,
  ) {
    return DeletePackageUseCase(repository: repository);
  }

  @lazySingleton
  RestockPackageUseCase provideRestockPackageUseCase(
    InventoryRepository repository,
  ) {
    return RestockPackageUseCase(repository: repository);
  }

  @lazySingleton
  AdjustPackageStockUseCase provideAdjustPackageStockUseCase(
    InventoryRepository repository,
  ) {
    return AdjustPackageStockUseCase(repository: repository);
  }

  @lazySingleton
  BulkUpdatePackageStatusUseCase provideBulkUpdatePackageStatusUseCase(
    InventoryRepository repository,
  ) {
    return BulkUpdatePackageStatusUseCase(repository: repository);
  }

  @lazySingleton
  GetSparePartsUseCase provideGetSparePartsUseCase(
    InventoryRepository repository,
  ) {
    return GetSparePartsUseCase(repository: repository);
  }

  @lazySingleton
  GetSparePartByIdUseCase provideGetSparePartByIdUseCase(
    InventoryRepository repository,
  ) {
    return GetSparePartByIdUseCase(repository: repository);
  }

  @lazySingleton
  CreateSparePartUseCase provideCreateSparePartUseCase(
    InventoryRepository repository,
  ) {
    return CreateSparePartUseCase(repository: repository);
  }

  @lazySingleton
  UpdateSparePartUseCase provideUpdateSparePartUseCase(
    InventoryRepository repository,
  ) {
    return UpdateSparePartUseCase(repository: repository);
  }

  @lazySingleton
  DeleteSparePartUseCase provideDeleteSparePartUseCase(
    InventoryRepository repository,
  ) {
    return DeleteSparePartUseCase(repository: repository);
  }

  @lazySingleton
  RestockSparePartUseCase provideRestockSparePartUseCase(
    InventoryRepository repository,
  ) {
    return RestockSparePartUseCase(repository: repository);
  }

  @lazySingleton
  AdjustSparePartStockUseCase provideAdjustSparePartStockUseCase(
    InventoryRepository repository,
  ) {
    return AdjustSparePartStockUseCase(repository: repository);
  }

  @lazySingleton
  BulkUpdateSparePartStatusUseCase provideBulkUpdateSparePartStatusUseCase(
    InventoryRepository repository,
  ) {
    return BulkUpdateSparePartStatusUseCase(repository: repository);
  }

  @lazySingleton
  InventoryBloc provideInventoryBloc(
    // Cylinder use cases
    GetCylindersUseCase getCylindersUseCase,
    GetCylinderByIdUseCase getCylinderByIdUseCase,
    CreateCylinderUseCase createCylinderUseCase,
    UpdateCylinderUseCase updateCylinderUseCase,
    DeleteCylinderUseCase deleteCylinderUseCase,
    RestockCylinderUseCase restockCylinderUseCase,
    AdjustCylinderStockUseCase adjustCylinderStockUseCase,
    BulkUpdateCylinderStatusUseCase bulkUpdateCylinderStatusUseCase,
    // Package use cases
    GetPackagesUseCase getPackagesUseCase,
    GetPackageByIdUseCase getPackageByIdUseCase,
    CreatePackageUseCase createPackageUseCase,
    UpdatePackageUseCase updatePackageUseCase,
    DeletePackageUseCase deletePackageUseCase,
    RestockPackageUseCase restockPackageUseCase,
    AdjustPackageStockUseCase adjustPackageStockUseCase,
    BulkUpdatePackageStatusUseCase bulkUpdatePackageStatusUseCase,
    // Spare part use cases
    GetSparePartsUseCase getSparePartsUseCase,
    GetSparePartByIdUseCase getSparePartByIdUseCase,
    CreateSparePartUseCase createSparePartUseCase,
    UpdateSparePartUseCase updateSparePartUseCase,
    DeleteSparePartUseCase deleteSparePartUseCase,
    RestockSparePartUseCase restockSparePartUseCase,
    AdjustSparePartStockUseCase adjustSparePartStockUseCase,
    BulkUpdateSparePartStatusUseCase bulkUpdateSparePartStatusUseCase,
  ) {
    return InventoryBloc(
      // Cylinder use cases
      getCylindersUseCase: getCylindersUseCase,
      getCylinderByIdUseCase: getCylinderByIdUseCase,
      createCylinderUseCase: createCylinderUseCase,
      updateCylinderUseCase: updateCylinderUseCase,
      deleteCylinderUseCase: deleteCylinderUseCase,
      restockCylinderUseCase: restockCylinderUseCase,
      adjustCylinderStockUseCase: adjustCylinderStockUseCase,
      bulkUpdateCylinderStatusUseCase: bulkUpdateCylinderStatusUseCase,
      // Package use cases
      getPackagesUseCase: getPackagesUseCase,
      getPackageByIdUseCase: getPackageByIdUseCase,
      createPackageUseCase: createPackageUseCase,
      updatePackageUseCase: updatePackageUseCase,
      deletePackageUseCase: deletePackageUseCase,
      restockPackageUseCase: restockPackageUseCase,
      adjustPackageStockUseCase: adjustPackageStockUseCase,
      bulkUpdatePackageStatusUseCase: bulkUpdatePackageStatusUseCase,
      // Spare part use cases
      getSparePartsUseCase: getSparePartsUseCase,
      getSparePartByIdUseCase: getSparePartByIdUseCase,
      createSparePartUseCase: createSparePartUseCase,
      updateSparePartUseCase: updateSparePartUseCase,
      deleteSparePartUseCase: deleteSparePartUseCase,
      restockSparePartUseCase: restockSparePartUseCase,
      adjustSparePartStockUseCase: adjustSparePartStockUseCase,
      bulkUpdateSparePartStatusUseCase: bulkUpdateSparePartStatusUseCase,
    );
  }
}
