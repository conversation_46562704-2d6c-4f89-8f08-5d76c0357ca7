class ApiEndpoints {
  /// ipconfig getifaddr en0
  static const String baseUrl = 'http://localhost:3010/api/v1';
  // static const String baseUrl = 'http://*************:3010/api/v1'; // hodan hospital
  // static const String baseUrl = 'http://*************:3010/api/v1'; // Home
  // static const String baseUrl = 'http://**************:3010/api/v1'; // Home
  //   static const String baseUrl = 'http://************:3010/api/v1'; // Rasiin
  //   static const String baseUrl = 'http://**************:3010/api/v1'; // Rasiin

  // User endpoints
  static const String login = '/users/login';
  static const String register = '/users/register';
  static const String resendOtp = '/users/resend-otp';
  static const String verifyOtp = '/users/verify-otp';
  static const String checkUserExist = '/users/check-user-exist';
  static const String getCurrentUser = '/users/me';
  static String getUserById(String id) => '/users/$id';
  static String updateUserById(String id) => '/users/$id';
  static String deleteUserById(String id) => '/users/$id';
  static const String registerAdminOrAgent = '/users/admin';
  static const String allUsers = '/users';
  static const String subscribeNotifications = '/users/notifications/subscribe';
  static const String unsubscribeNotifications =
      '/users/notifications/unsubscribe';

  // Dashboard endpoints
  static const String adminDashboard = '/dashboard/admin';
  static const String agentDashboard = '/dashboard/agent';
  static const String customerDashboard = '/dashboard/customer';
  static const String supervisorDashboard = '/dashboard/supervisor';
  static const String adminDashboardReport = '/dashboard/reports';

  // Order endpoints
  static const String createOrder = '/orders';
  static const String getOrders = '/orders';
  static String assignAgentToOrder(String orderId) =>
      '/orders/$orderId/assign-agent';
  static String cancelOrder(String orderId) => '/orders/$orderId/cancel';
  static String completeOrder(String orderId) => '/orders/$orderId/complete';
  static const String validateOrderInQRCode = '/orders/validate-qr';
  static String updateOrder(String orderId) => '/orders/$orderId';
  static String deleteOrder(String orderId) => '/orders/$orderId';

  // Inventory endpoints
  // Cylinders
  static const String getCylinders = '/cylinders';
  static String getCylinderById(String cylinderId) => '/cylinders/$cylinderId';
  static const String createCylinder = '/cylinders';
  static String updateCylinder(String cylinderId) => '/cylinders/$cylinderId';
  static String deleteCylinder(String cylinderId) => '/cylinders/$cylinderId';
  static String restockCylinder(String cylinderId) =>
      '/cylinders/$cylinderId/restock';
  static String adjustCylinderStock(String cylinderId) =>
      '/cylinders/$cylinderId/adjust';
  static const String bulkUpdateCylinderStatus = '/cylinders/bulk-status';

  // Packages
  static const String getPackages = '/packages';
  static String getPackageById(String packageId) => '/packages/$packageId';
  static const String createPackage = '/packages';
  static String updatePackage(String packageId) => '/packages/$packageId';
  static String deletePackage(String packageId) => '/packages/$packageId';
  static String restockPackage(String packageId) =>
      '/packages/$packageId/restock';
  static String adjustPackageStock(String packageId) =>
      '/packages/$packageId/adjust';
  static const String bulkUpdatePackageStatus = '/packages/bulk-status';

  // Spare Parts
  static const String getSpareParts = '/spare-parts';
  static String getSparePartById(String sparePartId) =>
      '/spare-parts/$sparePartId';
  static const String createSparePart = '/spare-parts';
  static String updateSparePart(String sparePartId) =>
      '/spare-parts/$sparePartId';
  static String deleteSparePart(String sparePartId) =>
      '/spare-parts/$sparePartId';
  static String restockSparePart(String sparePartId) =>
      '/spare-parts/$sparePartId/restock';
  static String adjustSparePartStock(String sparePartId) =>
      '/spare-parts/$sparePartId/adjust';
  static const String bulkUpdateSparePartStatus = '/spare-parts/bulk-status';

  // Communication endpoints
  static const String sendSms = '/sms-management/send';
  static const String sendBulkSms = '/sms-management/send-bulk';
  static const String getSmsHistory = '/sms-management/history';
  static const String getSmsStats = '/sms-management/stats';
  static const String getSmsById = '/sms-management/';
  static const String cancelScheduledSms = '/sms-management/cancel';
  static const String getSmsTemplates = '/sms-management/templates';
  static const String getSmsDashboard = '/sms-management/dashboard';

  // Notification endpoints
  static const String sendNotificationToUser =
      '/notification-management/send-to-user';
  static const String sendNotificationToTopic =
      '/notification-management/send-to-topic';
  static const String getNotificationHistory =
      '/notification-management/history';
  static const String getNotificationStats = '/notification-management/stats';
  static const String updateFcmToken = '/notification-management/fcm-token';
  static const String subscribeToTopic = '/notification-management/subscribe';
  static const String toggleNotifications = '/notification-management/toggle';
  static const String markNotificationAsRead =
      '/notification-management/mark-as-read';
  static const String getUserNotifications =
      '/notification-management/user-notifications';
  static const String getNotificationTemplates =
      '/notification-management/templates';
  static const String getNotificationDashboard =
      '/notification-management/dashboard';
}
