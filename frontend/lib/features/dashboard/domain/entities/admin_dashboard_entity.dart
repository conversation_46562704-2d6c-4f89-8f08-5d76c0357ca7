// admin_dashboard_entity.dart
import 'customer_dashboard_entity.dart';

class AdminDashboardEntity {
  final OrderStatsEntity orderStats;
  final RevenueStatsEntity revenueStats;
  final InventoryStatsEntity inventoryStats;
  final UserStatsReportEntity userStats;
  final List<AdminRecentOrderEntity> recentOrders;
  final List<TopProductEntity> topProducts;
  final List<AgentPerformanceReportEntity> agentPerformance;
  final PaymentStatsEntity paymentStats;
  final DateTime lastUpdated;

  AdminDashboardEntity({
    required this.orderStats,
    required this.revenueStats,
    required this.inventoryStats,
    required this.userStats,
    required this.recentOrders,
    required this.topProducts,
    required this.agentPerformance,
    required this.paymentStats,
    required this.lastUpdated,
  });
}

class OrderStatsEntity {
  final int total;
  final int pending;
  final int confirmed;
  final int outForDelivery;
  final int delivered;
  final int cancelled;
  final int failed;
  final double totalRevenue;

  OrderStatsEntity({
    required this.total,
    required this.pending,
    required this.confirmed,
    required this.outForDelivery,
    required this.delivered,
    required this.cancelled,
    required this.failed,
    required this.totalRevenue,
  });
}

class RevenueStatsEntity {
  final double totalRevenue;
  final double averageDailyRevenue;
  final List<DailyStatEntity> dailyStats;
  final int totalOrders;

  RevenueStatsEntity({
    required this.totalRevenue,
    required this.averageDailyRevenue,
    required this.dailyStats,
    required this.totalOrders,
  });
}

class DailyStatEntity {
  final DailyStatIdEntity id;
  final double dailyRevenue;
  final int orderCount;

  DailyStatEntity({
    required this.id,
    required this.dailyRevenue,
    required this.orderCount,
  });
}

class DailyStatIdEntity {
  final int year;
  final int month;
  final int day;

  DailyStatIdEntity({
    required this.year,
    required this.month,
    required this.day,
  });
}

class InventoryStatsEntity {
  final CylindersEntity cylinders;
  final SparePartsEntity spareParts;
  final PackagesEntity packages;

  InventoryStatsEntity({
    required this.cylinders,
    required this.spareParts,
    required this.packages,
  });
}

class CylindersEntity {
  final List<ByStatusEntity> byStatus;
  final List<dynamic> lowStockItems;
  final int lowStockCount;

  CylindersEntity({
    required this.byStatus,
    required this.lowStockItems,
    required this.lowStockCount,
  });
}

class SparePartsEntity {
  final List<ByStatusEntity> byStatus;
  final List<dynamic> lowStockItems;
  final int lowStockCount;

  SparePartsEntity({
    required this.byStatus,
    required this.lowStockItems,
    required this.lowStockCount,
  });
}

class PackagesEntity {
  final List<dynamic> byStatus;

  PackagesEntity({required this.byStatus});
}

class ByStatusEntity {
  final String id;
  final int count;
  final int totalQuantity;
  final int? totalReserved;
  final int? totalSold;
  final double? totalValue;

  ByStatusEntity({
    required this.id,
    required this.count,
    required this.totalQuantity,
    this.totalReserved,
    this.totalSold,
    this.totalValue,
  });
}

class UserStatsReportEntity {
  final int total;
  final int customers;
  final int agents;
  final int admins;
  final int activeAgents;

  UserStatsReportEntity({
    required this.total,
    required this.customers,
    required this.agents,
    required this.admins,
    required this.activeAgents,
  });
}

class AdminRecentOrderEntity {
  final String id;
  final CustomerEntity customer;
  final double totalAmount;
  final String status;
  final String deliveryAddress;
  final DateTime createdAt;
  final DeliveryAgentEntity? deliveryAgent;

  AdminRecentOrderEntity({
    required this.id,
    required this.customer,
    required this.totalAmount,
    required this.status,
    required this.deliveryAddress,
    required this.createdAt,
    this.deliveryAgent,
  });
}

class CustomerEntity {
  final String id;
  final String phone;

  CustomerEntity({required this.id, required this.phone});
}

class TopProductEntity {
  final ItemIdEntity id;
  final int totalQuantity;
  final double totalRevenue;
  final int orderCount;
  final ProductDetailsEntity productDetails;

  TopProductEntity({
    required this.id,
    required this.totalQuantity,
    required this.totalRevenue,
    required this.orderCount,
    required this.productDetails,
  });
}

class AgentPerformanceReportEntity {
  final String id;
  final int totalOrders;
  final int completedOrders;
  final double totalRevenue;
  final int avgDeliveryTime;
  final String agentId;
  final String agentPhone;
  final double completionRate;

  AgentPerformanceReportEntity({
    required this.id,
    required this.totalOrders,
    required this.completedOrders,
    required this.totalRevenue,
    required this.avgDeliveryTime,
    required this.agentId,
    required this.agentPhone,
    required this.completionRate,
  });
}

class PaymentStatsEntity {
  final Map<String, PaymentMethodEntity> byMethod;
  final Map<String, PaymentStatusEntity> byStatus;
  final int totalProcessed;
  final double totalAmount;

  PaymentStatsEntity({
    required this.byMethod,
    required this.byStatus,
    required this.totalProcessed,
    required this.totalAmount,
  });
}

class PaymentMethodEntity {
  final int count;
  final double amount;

  PaymentMethodEntity({required this.count, required this.amount});
}

class PaymentStatusEntity {
  final int count;
  final double amount;

  PaymentStatusEntity({required this.count, required this.amount});
}
