import '../../../../core/enums/dashboard_report_type.dart';
import 'admin_dashboard_entity.dart';

class DateRangeEntity {
  final DateTime startDate;
  final DateTime endDate;

  DateRangeEntity({required this.startDate, required this.endDate});
}

class AdminDashboardReportEntity {
  final DashboardReportType reportType;
  final DateRangeEntity dateRange;
  final dynamic report; // Will be one of the specific report types below

  AdminDashboardReportEntity({
    required this.reportType,
    required this.dateRange,
    required this.report,
  });
}

// Specific report types
class OrdersReportEntity {
  final OrderStatsEntity orderStats;
  final List<AdminRecentOrderEntity> recentOrders;

  OrdersReportEntity({required this.orderStats, required this.recentOrders});
}

class RevenueReportEntity {
  final RevenueStatsEntity revenueStats;
  final PaymentStatsEntity paymentStats;

  RevenueReportEntity({required this.revenueStats, required this.paymentStats});
}

class InventoryReportEntity {
  final InventoryStatsEntity inventoryStats;
  final List<TopProductEntity> topProducts;

  InventoryReportEntity({
    required this.inventoryStats,
    required this.topProducts,
  });
}

class AgentsReportEntity {
  final List<AgentPerformanceReportEntity> agentPerformance;
  final UserStatsReportEntity userStats;

  AgentsReportEntity({required this.agentPerformance, required this.userStats});
}
