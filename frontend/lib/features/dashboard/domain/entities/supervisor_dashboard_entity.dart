// supervisor_dashboard_entity.dart
import 'admin_dashboard_entity.dart';

class SupervisorDashboardEntity {
  final SupervisorInfoEntity supervisorInfo;
  final TodayStatsEntity todayStats;
  final List<AvailableAgentEntity> availableAgents;
  final List<TodayOrderEntity> todayOrders;
  final List<TodayTopProductEntity> todayTopProducts;
  final SupervisorRestrictionsEntity restrictions;
  final DateTime lastUpdated;

  SupervisorDashboardEntity({
    required this.supervisorInfo,
    required this.todayStats,
    required this.availableAgents,
    required this.todayOrders,
    required this.todayTopProducts,
    required this.restrictions,
    required this.lastUpdated,
  });
}

class SupervisorInfoEntity {
  final String? id;
  final String? phone;
  final String? email;

  SupervisorInfoEntity({
    this.id,
    this.phone,
    this.email,
  });
}

class TodayStatsEntity {
  final SupervisorOrderStatsEntity orders;
  final SupervisorSalesStatsEntity sales;

  TodayStatsEntity({
    required this.orders,
    required this.sales,
  });
}

class SupervisorOrderStatsEntity {
  final int total;
  final int pending;
  final int confirmed;
  final int outForDelivery;
  final int delivered;
  final int cancelled;
  final int failed;
  final double totalRevenue;

  SupervisorOrderStatsEntity({
    required this.total,
    required this.pending,
    required this.confirmed,
    required this.outForDelivery,
    required this.delivered,
    required this.cancelled,
    required this.failed,
    required this.totalRevenue,
  });
}

class SupervisorSalesStatsEntity {
  final int totalQuantitySold;
  final double totalRevenue;
  final int itemsSold;
  final List<SalesBreakdownEntity> salesBreakdown;

  SupervisorSalesStatsEntity({
    required this.totalQuantitySold,
    required this.totalRevenue,
    required this.itemsSold,
    required this.salesBreakdown,
  });
}

class SalesBreakdownEntity {
  final ItemIdEntity id;
  final int totalQuantitySold;
  final double totalRevenue;
  final int orderCount;

  SalesBreakdownEntity({
    required this.id,
    required this.totalQuantitySold,
    required this.totalRevenue,
    required this.orderCount,
  });
}

class AvailableAgentEntity {
  final String id;
  final String phone;
  final String? email;
  final AgentMetadataEntity agentMetadata;

  AvailableAgentEntity({
    required this.id,
    required this.phone,
    this.email,
    required this.agentMetadata,
  });
}

class AgentMetadataEntity {
  final double rating;
  final VehicleEntity? vehicle;

  AgentMetadataEntity({
    required this.rating,
    this.vehicle,
  });
}

class VehicleEntity {
  final String type;
  final String? plateNumber;

  VehicleEntity({
    required this.type,
    this.plateNumber,
  });
}

class TodayOrderEntity {
  final String id;
  final CustomerEntity customer;
  final double totalAmount;
  final String status;
  final String deliveryAddress;
  final DateTime createdAt;
  final DeliveryAgentEntity? deliveryAgent;

  TodayOrderEntity({
    required this.id,
    required this.customer,
    required this.totalAmount,
    required this.status,
    required this.deliveryAddress,
    required this.createdAt,
    this.deliveryAgent,
  });
}

class TodayTopProductEntity {
  final ItemIdEntity id;
  final int totalQuantity;
  final double totalRevenue;
  final int orderCount;
  final SupervisorProductDetailsEntity productDetails;

  TodayTopProductEntity({
    required this.id,
    required this.totalQuantity,
    required this.totalRevenue,
    required this.orderCount,
    required this.productDetails,
  });
}

class SupervisorProductDetailsEntity {
  final String? type;
  final String? material;
  final String? name;
  final String? category;
  final String? description;
  final double price; // Only selling price, no cost data

  SupervisorProductDetailsEntity({
    this.type,
    this.material,
    this.name,
    this.category,
    this.description,
    required this.price,
  });
}

class SupervisorRestrictionsEntity {
  final String dataScope;
  final bool canAssignOrders;
  final bool canEditInventory;
  final bool canSeeCostData;

  SupervisorRestrictionsEntity({
    required this.dataScope,
    required this.canAssignOrders,
    required this.canEditInventory,
    required this.canSeeCostData,
  });
}

// Reusing entities from admin dashboard
class ItemIdEntity {
  final String itemType;
  final String itemId;

  ItemIdEntity({
    required this.itemType,
    required this.itemId,
  });
}

class DeliveryAgentEntity {
  final String id;
  final String phone;
  final String? email;

  DeliveryAgentEntity({
    required this.id,
    required this.phone,
    this.email,
  });
}
