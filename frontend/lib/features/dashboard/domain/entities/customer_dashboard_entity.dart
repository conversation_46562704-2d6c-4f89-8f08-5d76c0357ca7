// domain/entities/customer_dashboard_entity.dart
class CustomerDashboardEntity {
  final UserInfoEntity userInfo;
  final OrderStatsEntity orderStats;
  final TotalSpentEntity totalSpent;
  final List<RecentOrderEntity> recentOrders;
  final List<FavoriteProductEntity> favoriteProducts;
  final DeliveryStatsEntity deliveryStats;
  final DateTime lastUpdated;

  CustomerDashboardEntity({
    required this.userInfo,
    required this.orderStats,
    required this.totalSpent,
    required this.recentOrders,
    required this.favoriteProducts,
    required this.deliveryStats,
    required this.lastUpdated,
  });
}

class UserInfoEntity {
  final String id;
  final String phone;
  final List<dynamic> addresses;
  final DateTime memberSince;

  UserInfoEntity({
    required this.id,
    required this.phone,
    required this.addresses,
    required this.memberSince,
  });
}

class OrderStatsEntity {
  final int total;
  final int pending;
  final int confirmed;
  final int outForDelivery;
  final int delivered;
  final int cancelled;
  final int failed;

  OrderStatsEntity({
    required this.total,
    required this.pending,
    required this.confirmed,
    required this.outForDelivery,
    required this.delivered,
    required this.cancelled,
    required this.failed,
  });
}

class TotalSpentEntity {
  final String? id;
  final double totalSpent;
  final int orderCount;
  final double avgOrderValue;

  TotalSpentEntity({
    this.id,
    required this.totalSpent,
    required this.orderCount,
    required this.avgOrderValue,
  });
}

class RecentOrderEntity {
  final String id;
  final List<OrderItemEntity> items;
  final double totalAmount;
  final String status;
  final String deliveryAddress;
  final DateTime createdAt;
  final DeliveryAgentEntity? deliveryAgent;

  RecentOrderEntity({
    required this.id,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.deliveryAddress,
    required this.createdAt,
    this.deliveryAgent,
  });
}

class OrderItemEntity {
  final String itemType;
  final String itemId;
  final int quantity;
  final String id;

  OrderItemEntity({
    required this.itemType,
    required this.itemId,
    required this.quantity,
    required this.id,
  });

  Map<String, dynamic> toJson() {
    return {'itemType': itemType, 'itemId': itemId, 'quantity': quantity};
  }
}

class DeliveryAgentEntity {
  final String id;
  final String phone;
  final String email;

  DeliveryAgentEntity({
    required this.id,
    required this.phone,
    required this.email,
  });
}

class FavoriteProductEntity {
  final ItemIdEntity id;
  final int orderCount;
  final int totalQuantity;
  final ProductDetailsEntity productDetails;

  FavoriteProductEntity({
    required this.id,
    required this.orderCount,
    required this.totalQuantity,
    required this.productDetails,
  });
}

class ItemIdEntity {
  final String itemType;
  final String itemId;

  ItemIdEntity({required this.itemType, required this.itemId});
}

class ProductDetailsEntity {
  final String id;
  final String type;
  final String material;
  final double price;
  final dynamic availableQuantity;
  final String productId;

  ProductDetailsEntity({
    required this.id,
    required this.type,
    required this.material,
    required this.price,
    required this.availableQuantity,
    required this.productId,
  });
}

class DeliveryStatsEntity {
  final int totalDeliveries;
  final double avgDeliveryTimeHours;
  final double fastestDeliveryHours;
  final double slowestDeliveryHours;

  DeliveryStatsEntity({
    required this.totalDeliveries,
    required this.avgDeliveryTimeHours,
    required this.fastestDeliveryHours,
    required this.slowestDeliveryHours,
  });
}
