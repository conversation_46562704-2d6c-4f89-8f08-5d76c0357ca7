// admin_dashboard_mapper.dart
import '../models/admin_dashboard_model.dart';
import '../../domain/entities/admin_dashboard_entity.dart';

class AdminDashboardMapper {
  static AdminDashboardEntity toEntity(AdminDashboardModel model) {
    return AdminDashboardEntity(
      orderStats: model.orderStats,
      revenueStats: model.revenueStats,
      inventoryStats: model.inventoryStats,
      userStats: model.userStats,
      recentOrders: model.recentOrders,
      topProducts: model.topProducts,
      agentPerformance: model.agentPerformance,
      paymentStats: model.paymentStats,
      lastUpdated: model.lastUpdated,
    );
  }

  static AdminDashboardModel toModel(AdminDashboardEntity entity) {
    return AdminDashboardModel(
      orderStats: entity.orderStats,
      revenueStats: entity.revenueStats,
      inventoryStats: entity.inventoryStats,
      userStats: entity.userStats,
      recentOrders: entity.recentOrders,
      topProducts: entity.topProducts,
      agentPerformance: entity.agentPerformance,
      paymentStats: entity.paymentStats,
      lastUpdated: entity.lastUpdated,
    );
  }

  static List<AdminDashboardEntity> toEntityList(
    List<AdminDashboardModel> models,
  ) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<AdminDashboardModel> toModelList(
    List<AdminDashboardEntity> entities,
  ) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
