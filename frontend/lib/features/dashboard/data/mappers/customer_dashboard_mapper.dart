// data/mappers/customer_dashboard_mapper.dart
import 'package:frontend/features/dashboard/data/models/customer_dashboard_model.dart';
import 'package:frontend/features/dashboard/domain/entities/customer_dashboard_entity.dart';

class CustomerDashboardMapper {
  static CustomerDashboardEntity toEntity(CustomerDashboardModel model) {
    return CustomerDashboardEntity(
      userInfo: model.userInfo,
      orderStats: model.orderStats,
      totalSpent: model.totalSpent,
      recentOrders: model.recentOrders,
      favoriteProducts: model.favoriteProducts,
      deliveryStats: model.deliveryStats,
      lastUpdated: model.lastUpdated,
    );
  }

  static CustomerDashboardModel toModel(CustomerDashboardEntity entity) {
    return CustomerDashboardModel(
      userInfo: entity.userInfo,
      orderStats: entity.orderStats,
      totalSpent: entity.totalSpent,
      recentOrders: entity.recentOrders,
      favoriteProducts: entity.favoriteProducts,
      deliveryStats: entity.deliveryStats,
      lastUpdated: entity.lastUpdated,
    );
  }

  static List<CustomerDashboardEntity> toEntityList(
      List<CustomerDashboardModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<CustomerDashboardModel> toModelList(
      List<CustomerDashboardEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
