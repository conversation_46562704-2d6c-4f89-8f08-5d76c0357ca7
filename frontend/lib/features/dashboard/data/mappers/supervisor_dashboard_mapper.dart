// supervisor_dashboard_mapper.dart
import '../../domain/entities/supervisor_dashboard_entity.dart';
import '../models/supervisor_dashboard_model.dart';
import '../../domain/entities/admin_dashboard_entity.dart' show CustomerEntity;
import '../models/admin_dashboard_model.dart' show CustomerModel;

class SupervisorDashboardMapper {
  static SupervisorDashboardEntity toEntity(SupervisorDashboardModel model) {
    return SupervisorDashboardEntity(
      supervisorInfo: _mapSupervisorInfo(model.supervisorInfo),
      todayStats: _mapTodayStats(model.todayStats),
      availableAgents: model.availableAgents.map(_mapAvailableAgent).toList(),
      todayOrders: model.todayOrders.map(_mapTodayOrder).toList(),
      todayTopProducts: model.todayTopProducts
          .map(_mapTodayTopProduct)
          .toList(),
      restrictions: _mapRestrictions(model.restrictions),
      lastUpdated: model.lastUpdated,
    );
  }

  static SupervisorInfoEntity _mapSupervisorInfo(SupervisorInfoModel model) {
    return SupervisorInfoEntity(
      id: model.id,
      phone: model.phone,
      email: model.email,
    );
  }

  static TodayStatsEntity _mapTodayStats(TodayStatsModel model) {
    return TodayStatsEntity(
      orders: _mapOrderStats(model.orders),
      sales: _mapSalesStats(model.sales),
    );
  }

  static SupervisorOrderStatsEntity _mapOrderStats(
    SupervisorOrderStatsModel model,
  ) {
    return SupervisorOrderStatsEntity(
      total: model.total,
      pending: model.pending,
      confirmed: model.confirmed,
      outForDelivery: model.outForDelivery,
      delivered: model.delivered,
      cancelled: model.cancelled,
      failed: model.failed,
      totalRevenue: model.totalRevenue,
    );
  }

  static SupervisorSalesStatsEntity _mapSalesStats(
    SupervisorSalesStatsModel model,
  ) {
    return SupervisorSalesStatsEntity(
      totalQuantitySold: model.totalQuantitySold,
      totalRevenue: model.totalRevenue,
      itemsSold: model.itemsSold,
      salesBreakdown: model.salesBreakdown.map(_mapSalesBreakdown).toList(),
    );
  }

  static SalesBreakdownEntity _mapSalesBreakdown(SalesBreakdownModel model) {
    return SalesBreakdownEntity(
      id: _mapItemId(model.id),
      totalQuantitySold: model.totalQuantitySold,
      totalRevenue: model.totalRevenue,
      orderCount: model.orderCount,
    );
  }

  static AvailableAgentEntity _mapAvailableAgent(AvailableAgentModel model) {
    return AvailableAgentEntity(
      id: model.id,
      phone: model.phone,
      email: model.email,
      agentMetadata: _mapAgentMetadata(model.agentMetadata),
    );
  }

  static AgentMetadataEntity _mapAgentMetadata(AgentMetadataModel model) {
    return AgentMetadataEntity(
      rating: model.rating,
      vehicle: model.vehicle != null ? _mapVehicle(model.vehicle!) : null,
    );
  }

  static VehicleEntity _mapVehicle(VehicleModel model) {
    return VehicleEntity(type: model.type, plateNumber: model.plateNumber);
  }

  static TodayOrderEntity _mapTodayOrder(TodayOrderModel model) {
    return TodayOrderEntity(
      id: model.id,
      customer: _mapCustomer(model.customer),
      totalAmount: model.totalAmount,
      status: model.status,
      deliveryAddress: model.deliveryAddress,
      createdAt: model.createdAt,
      deliveryAgent: model.deliveryAgent != null
          ? _mapDeliveryAgent(model.deliveryAgent!)
          : null,
    );
  }

  static CustomerEntity _mapCustomer(CustomerModel model) {
    return CustomerEntity(id: model.id, phone: model.phone);
  }

  static DeliveryAgentEntity _mapDeliveryAgent(DeliveryAgentModel model) {
    return DeliveryAgentEntity(
      id: model.id,
      phone: model.phone,
      email: model.email,
    );
  }

  static TodayTopProductEntity _mapTodayTopProduct(TodayTopProductModel model) {
    return TodayTopProductEntity(
      id: _mapItemId(model.id),
      totalQuantity: model.totalQuantity,
      totalRevenue: model.totalRevenue,
      orderCount: model.orderCount,
      productDetails: _mapProductDetails(model.productDetails),
    );
  }

  static ItemIdEntity _mapItemId(ItemIdModel model) {
    return ItemIdEntity(itemType: model.itemType, itemId: model.itemId);
  }

  static SupervisorProductDetailsEntity _mapProductDetails(
    SupervisorProductDetailsModel model,
  ) {
    return SupervisorProductDetailsEntity(
      type: model.type,
      material: model.material,
      name: model.name,
      category: model.category,
      description: model.description,
      price: model.price,
    );
  }

  static SupervisorRestrictionsEntity _mapRestrictions(
    SupervisorRestrictionsModel model,
  ) {
    return SupervisorRestrictionsEntity(
      dataScope: model.dataScope,
      canAssignOrders: model.canAssignOrders,
      canEditInventory: model.canEditInventory,
      canSeeCostData: model.canSeeCostData,
    );
  }

  // Helper classes for reused entities
  // Import CustomerEntity and CustomerModel from their original source files.
}

class DeliveryAgentModel {
  final String id;
  final String phone;
  final String? email;

  DeliveryAgentModel({required this.id, required this.phone, this.email});

  factory DeliveryAgentModel.fromJson(Map<String, dynamic> json) {
    return DeliveryAgentModel(
      id: json['_id'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
    );
  }
}

class ItemIdModel {
  final String itemType;
  final String itemId;

  ItemIdModel({required this.itemType, required this.itemId});

  factory ItemIdModel.fromJson(Map<String, dynamic> json) {
    return ItemIdModel(
      itemType: json['itemType'] ?? '',
      itemId: json['itemId'] ?? '',
    );
  }
}
