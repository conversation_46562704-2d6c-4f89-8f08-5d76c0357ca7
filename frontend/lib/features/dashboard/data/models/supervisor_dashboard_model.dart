// supervisor_dashboard_model.dart
import '../mappers/supervisor_dashboard_mapper.dart';
import 'admin_dashboard_model.dart';

class SupervisorDashboardModel {
  final SupervisorInfoModel supervisorInfo;
  final TodayStatsModel todayStats;
  final List<AvailableAgentModel> availableAgents;
  final List<TodayOrderModel> todayOrders;
  final List<TodayTopProductModel> todayTopProducts;
  final SupervisorRestrictionsModel restrictions;
  final DateTime lastUpdated;

  SupervisorDashboardModel({
    required this.supervisorInfo,
    required this.todayStats,
    required this.availableAgents,
    required this.todayOrders,
    required this.todayTopProducts,
    required this.restrictions,
    required this.lastUpdated,
  });

  factory SupervisorDashboardModel.fromJson(Map<String, dynamic> json) {
    return SupervisorDashboardModel(
      supervisorInfo: SupervisorInfoModel.fromJson(json['supervisorInfo'] ?? {}),
      todayStats: TodayStatsModel.fromJson(json['todayStats'] ?? {}),
      availableAgents: (json['availableAgents'] as List<dynamic>?)
              ?.map((agent) => AvailableAgentModel.fromJson(agent))
              .toList() ??
          [],
      todayOrders: (json['todayOrders'] as List<dynamic>?)
              ?.map((order) => TodayOrderModel.fromJson(order))
              .toList() ??
          [],
      todayTopProducts: (json['todayTopProducts'] as List<dynamic>?)
              ?.map((product) => TodayTopProductModel.fromJson(product))
              .toList() ??
          [],
      restrictions: SupervisorRestrictionsModel.fromJson(json['restrictions'] ?? {}),
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class SupervisorInfoModel {
  final String? id;
  final String? phone;
  final String? email;

  SupervisorInfoModel({
    this.id,
    this.phone,
    this.email,
  });

  factory SupervisorInfoModel.fromJson(Map<String, dynamic> json) {
    return SupervisorInfoModel(
      id: json['id'],
      phone: json['phone'],
      email: json['email'],
    );
  }
}

class TodayStatsModel {
  final SupervisorOrderStatsModel orders;
  final SupervisorSalesStatsModel sales;

  TodayStatsModel({
    required this.orders,
    required this.sales,
  });

  factory TodayStatsModel.fromJson(Map<String, dynamic> json) {
    return TodayStatsModel(
      orders: SupervisorOrderStatsModel.fromJson(json['orders'] ?? {}),
      sales: SupervisorSalesStatsModel.fromJson(json['sales'] ?? {}),
    );
  }
}

class SupervisorOrderStatsModel {
  final int total;
  final int pending;
  final int confirmed;
  final int outForDelivery;
  final int delivered;
  final int cancelled;
  final int failed;
  final double totalRevenue;

  SupervisorOrderStatsModel({
    required this.total,
    required this.pending,
    required this.confirmed,
    required this.outForDelivery,
    required this.delivered,
    required this.cancelled,
    required this.failed,
    required this.totalRevenue,
  });

  factory SupervisorOrderStatsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorOrderStatsModel(
      total: json['total'] ?? 0,
      pending: json['pending'] ?? 0,
      confirmed: json['confirmed'] ?? 0,
      outForDelivery: json['outForDelivery'] ?? 0,
      delivered: json['delivered'] ?? 0,
      cancelled: json['cancelled'] ?? 0,
      failed: json['failed'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
    );
  }
}

class SupervisorSalesStatsModel {
  final int totalQuantitySold;
  final double totalRevenue;
  final int itemsSold;
  final List<SalesBreakdownModel> salesBreakdown;

  SupervisorSalesStatsModel({
    required this.totalQuantitySold,
    required this.totalRevenue,
    required this.itemsSold,
    required this.salesBreakdown,
  });

  factory SupervisorSalesStatsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorSalesStatsModel(
      totalQuantitySold: json['totalQuantitySold'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      itemsSold: json['itemsSold'] ?? 0,
      salesBreakdown: (json['salesBreakdown'] as List<dynamic>?)
              ?.map((breakdown) => SalesBreakdownModel.fromJson(breakdown))
              .toList() ??
          [],
    );
  }
}

class SalesBreakdownModel {
  final ItemIdModel id;
  final int totalQuantitySold;
  final double totalRevenue;
  final int orderCount;

  SalesBreakdownModel({
    required this.id,
    required this.totalQuantitySold,
    required this.totalRevenue,
    required this.orderCount,
  });

  factory SalesBreakdownModel.fromJson(Map<String, dynamic> json) {
    return SalesBreakdownModel(
      id: ItemIdModel.fromJson(json['_id'] ?? {}),
      totalQuantitySold: json['totalQuantitySold'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      orderCount: json['orderCount'] ?? 0,
    );
  }
}

class AvailableAgentModel {
  final String id;
  final String phone;
  final String? email;
  final AgentMetadataModel agentMetadata;

  AvailableAgentModel({
    required this.id,
    required this.phone,
    this.email,
    required this.agentMetadata,
  });

  factory AvailableAgentModel.fromJson(Map<String, dynamic> json) {
    return AvailableAgentModel(
      id: json['_id'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      agentMetadata: AgentMetadataModel.fromJson(json['agentMetadata'] ?? {}),
    );
  }
}

class AgentMetadataModel {
  final double rating;
  final VehicleModel? vehicle;

  AgentMetadataModel({
    required this.rating,
    this.vehicle,
  });

  factory AgentMetadataModel.fromJson(Map<String, dynamic> json) {
    return AgentMetadataModel(
      rating: (json['rating'] ?? 0).toDouble(),
      vehicle: json['vehicle'] != null ? VehicleModel.fromJson(json['vehicle']) : null,
    );
  }
}

class VehicleModel {
  final String type;
  final String? plateNumber;

  VehicleModel({
    required this.type,
    this.plateNumber,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      type: json['type'] ?? '',
      plateNumber: json['plateNumber'],
    );
  }
}

class TodayOrderModel {
  final String id;
  final CustomerModel customer;
  final double totalAmount;
  final String status;
  final String deliveryAddress;
  final DateTime createdAt;
  final DeliveryAgentModel? deliveryAgent;

  TodayOrderModel({
    required this.id,
    required this.customer,
    required this.totalAmount,
    required this.status,
    required this.deliveryAddress,
    required this.createdAt,
    this.deliveryAgent,
  });

  factory TodayOrderModel.fromJson(Map<String, dynamic> json) {
    return TodayOrderModel(
      id: json['_id'] ?? '',
      customer: CustomerModel.fromJson(json['customer'] ?? {}),
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      status: json['status'] ?? '',
      deliveryAddress: json['deliveryAddress'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      deliveryAgent: json['deliveryAgent'] != null
          ? DeliveryAgentModel.fromJson(json['deliveryAgent'])
          : null,
    );
  }
}

class TodayTopProductModel {
  final ItemIdModel id;
  final int totalQuantity;
  final double totalRevenue;
  final int orderCount;
  final SupervisorProductDetailsModel productDetails;

  TodayTopProductModel({
    required this.id,
    required this.totalQuantity,
    required this.totalRevenue,
    required this.orderCount,
    required this.productDetails,
  });

  factory TodayTopProductModel.fromJson(Map<String, dynamic> json) {
    return TodayTopProductModel(
      id: ItemIdModel.fromJson(json['_id'] ?? {}),
      totalQuantity: json['totalQuantity'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      orderCount: json['orderCount'] ?? 0,
      productDetails: SupervisorProductDetailsModel.fromJson(json['productDetails'] ?? {}),
    );
  }
}

class SupervisorProductDetailsModel {
  final String? type;
  final String? material;
  final String? name;
  final String? category;
  final String? description;
  final double price;

  SupervisorProductDetailsModel({
    this.type,
    this.material,
    this.name,
    this.category,
    this.description,
    required this.price,
  });

  factory SupervisorProductDetailsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorProductDetailsModel(
      type: json['type'],
      material: json['material'],
      name: json['name'],
      category: json['category'],
      description: json['description'],
      price: (json['price'] ?? 0).toDouble(),
    );
  }
}

class SupervisorRestrictionsModel {
  final String dataScope;
  final bool canAssignOrders;
  final bool canEditInventory;
  final bool canSeeCostData;

  SupervisorRestrictionsModel({
    required this.dataScope,
    required this.canAssignOrders,
    required this.canEditInventory,
    required this.canSeeCostData,
  });

  factory SupervisorRestrictionsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorRestrictionsModel(
      dataScope: json['dataScope'] ?? 'TODAY_ONLY',
      canAssignOrders: json['canAssignOrders'] ?? true,
      canEditInventory: json['canEditInventory'] ?? false,
      canSeeCostData: json['canSeeCostData'] ?? false,
    );
  }
}
