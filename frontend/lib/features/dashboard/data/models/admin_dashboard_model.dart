// admin_dashboard_model.dart
import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/admin_dashboard_entity.dart';
import 'customer_dashboard_model.dart';

class AdminDashboardModel extends AdminDashboardEntity {
  AdminDashboardModel({
    required super.orderStats,
    required super.revenueStats,
    required super.inventoryStats,
    required super.userStats,
    required super.recentOrders,
    required super.topProducts,
    required super.agentPerformance,
    required super.paymentStats,
    required super.lastUpdated,
  });

  factory AdminDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return AdminDashboardModel(
        orderStats: OrderStatsModel.fromJson(json['orderStats']),
        revenueStats: RevenueStatsModel.fromJson(json['revenueStats']),
        inventoryStats: InventoryStatsModel.from<PERSON>son(json['inventoryStats']),
        userStats: UserStatsReportModel.fromJson(json['userStats']),
        recentOrders: AdminRecentOrderModel.fromJsonList(json['recentOrders']),
        topProducts: TopProductModel.fromJsonList(json['topProducts']),
        agentPerformance: AgentPerformanceReportModel.fromJsonList(
          json['agentPerformance'],
        ),
        paymentStats: PaymentStatsModel.fromJson(json['paymentStats']),
        lastUpdated: DateTime.parse(json['lastUpdated']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AdminDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AdminDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class OrderStatsModel extends OrderStatsEntity {
  OrderStatsModel({
    required super.total,
    required super.pending,
    required super.confirmed,
    required super.outForDelivery,
    required super.delivered,
    required super.cancelled,
    required super.failed,
    required super.totalRevenue,
  });

  factory OrderStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderStatsModel(
        total: json['total'] ?? 0,
        pending: json['pending'] ?? 0,
        confirmed: json['confirmed'] ?? 0,
        outForDelivery: json['outForDelivery'] ?? 0,
        delivered: json['delivered'] ?? 0,
        cancelled: json['cancelled'] ?? 0,
        failed: json['failed'] ?? 0,
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class RevenueStatsModel extends RevenueStatsEntity {
  RevenueStatsModel({
    required super.totalRevenue,
    required super.averageDailyRevenue,
    required super.dailyStats,
    required super.totalOrders,
  });

  factory RevenueStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return RevenueStatsModel(
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        averageDailyRevenue: (json['averageDailyRevenue'] as num).toDouble(),
        dailyStats: DailyStatModel.fromJsonList(json['dailyStats']),
        totalOrders: json['totalOrders'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse RevenueStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: RevenueStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class DailyStatModel extends DailyStatEntity {
  DailyStatModel({
    required super.id,
    required super.dailyRevenue,
    required super.orderCount,
  });

  factory DailyStatModel.fromJson(Map<String, dynamic> json) {
    try {
      return DailyStatModel(
        id: DailyStatIdModel.fromJson(json['_id']),
        dailyRevenue: (json['dailyRevenue'] as num).toDouble(),
        orderCount: json['orderCount'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DailyStatModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DailyStatModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<DailyStatModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => DailyStatModel.fromJson(json)).toList();
  }
}

class DailyStatIdModel extends DailyStatIdEntity {
  DailyStatIdModel({
    required super.year,
    required super.month,
    required super.day,
  });

  factory DailyStatIdModel.fromJson(Map<String, dynamic> json) {
    try {
      return DailyStatIdModel(
        year: json['year'] ?? 0,
        month: json['month'] ?? 0,
        day: json['day'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DailyStatIdModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DailyStatIdModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class InventoryStatsModel extends InventoryStatsEntity {
  InventoryStatsModel({
    required super.cylinders,
    required super.spareParts,
    required super.packages,
  });

  factory InventoryStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return InventoryStatsModel(
        cylinders: CylindersModel.fromJson(json['cylinders']),
        spareParts: SparePartsModel.fromJson(json['spareParts']),
        packages: PackagesModel.fromJson(json['packages']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse InventoryStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: InventoryStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class CylindersModel extends CylindersEntity {
  CylindersModel({
    required super.byStatus,
    required super.lowStockItems,
    required super.lowStockCount,
  });

  factory CylindersModel.fromJson(Map<String, dynamic> json) {
    try {
      return CylindersModel(
        byStatus: ByStatusModel.fromJsonList(json['byStatus']),
        lowStockItems: json['lowStockItems'] ?? [],
        lowStockCount: json['lowStockCount'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse CylindersModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CylindersModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class SparePartsModel extends SparePartsEntity {
  SparePartsModel({
    required super.byStatus,
    required super.lowStockItems,
    required super.lowStockCount,
  });

  factory SparePartsModel.fromJson(Map<String, dynamic> json) {
    try {
      return SparePartsModel(
        byStatus: ByStatusModel.fromJsonList(json['byStatus']),
        lowStockItems: json['lowStockItems'] ?? [],
        lowStockCount: json['lowStockCount'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse SparePartsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SparePartsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class PackagesModel extends PackagesEntity {
  PackagesModel({required super.byStatus});

  factory PackagesModel.fromJson(Map<String, dynamic> json) {
    try {
      return PackagesModel(byStatus: json['byStatus'] ?? []);
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PackagesModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PackagesModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class ByStatusModel extends ByStatusEntity {
  ByStatusModel({
    required super.id,
    required super.count,
    required super.totalQuantity,
    super.totalReserved,
    super.totalSold,
    super.totalValue,
  });

  factory ByStatusModel.fromJson(Map<String, dynamic> json) {
    try {
      return ByStatusModel(
        id: json['_id'] ?? '',
        count: json['count'] ?? 0,
        totalQuantity: json['totalQuantity'] ?? 0,
        totalReserved: json['totalReserved'],
        totalSold: json['totalSold'],
        totalValue: json['totalValue']?.toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse ByStatusModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: ByStatusModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<ByStatusModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => ByStatusModel.fromJson(json)).toList();
  }
}

class UserStatsReportModel extends UserStatsReportEntity {
  UserStatsReportModel({
    required super.total,
    required super.customers,
    required super.agents,
    required super.admins,
    required super.activeAgents,
  });

  factory UserStatsReportModel.fromJson(Map<String, dynamic> json) {
    try {
      return UserStatsReportModel(
        total: json['total'] ?? 0,
        customers: json['customers'] ?? 0,
        agents: json['agents'] ?? 0,
        admins: json['admins'] ?? 0,
        activeAgents: json['activeAgents'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse UserStatsReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: UserStatsReportModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class AdminRecentOrderModel extends AdminRecentOrderEntity {
  AdminRecentOrderModel({
    required super.id,
    required super.customer,
    required super.totalAmount,
    required super.status,
    required super.deliveryAddress,
    required super.createdAt,
    super.deliveryAgent,
  });

  factory AdminRecentOrderModel.fromJson(Map<String, dynamic> json) {
    try {
      return AdminRecentOrderModel(
        id: json['_id'] ?? '',
        customer: CustomerModel.fromJson(json['customer']),
        totalAmount: (json['totalAmount'] as num).toDouble(),
        status: json['status'] ?? '',
        deliveryAddress: json['deliveryAddress'] ?? '',
        createdAt: DateTime.parse(json['createdAt']),
        deliveryAgent: json['deliveryAgent'] != null
            ? DeliveryAgentModel.fromJson(json['deliveryAgent'])
            : null,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AdminRecentOrderModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AdminRecentOrderModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<AdminRecentOrderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => AdminRecentOrderModel.fromJson(json))
        .toList();
  }
}

class CustomerModel extends CustomerEntity {
  CustomerModel({required super.id, required super.phone});

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    try {
      return CustomerModel(id: json['_id'] ?? '', phone: json['phone'] ?? '');
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse CustomerModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CustomerModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class TopProductModel extends TopProductEntity {
  TopProductModel({
    required super.id,
    required super.totalQuantity,
    required super.totalRevenue,
    required super.orderCount,
    required super.productDetails,
  });

  factory TopProductModel.fromJson(Map<String, dynamic> json) {
    try {
      return TopProductModel(
        id: ItemIdModel.fromJson(json['_id']),
        totalQuantity: json['totalQuantity'] ?? 0,
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        orderCount: json['orderCount'] ?? 0,
        productDetails: ProductDetailsModel.fromJson(json['productDetails']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse TopProductModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: TopProductModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<TopProductModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => TopProductModel.fromJson(json)).toList();
  }
}

class AgentPerformanceReportModel extends AgentPerformanceReportEntity {
  AgentPerformanceReportModel({
    required super.id,
    required super.totalOrders,
    required super.completedOrders,
    required super.totalRevenue,
    required super.avgDeliveryTime,
    required super.agentId,
    required super.agentPhone,
    required super.completionRate,
  });

  factory AgentPerformanceReportModel.fromJson(Map<String, dynamic> json) {
    try {
      return AgentPerformanceReportModel(
        id: json['_id'] ?? '',
        totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
        completedOrders: json['completedOrders'] ?? 0,
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        avgDeliveryTime: (json['avgDeliveryTime'] as num?)?.toInt() ?? 0,
        agentId: json['agentId'] ?? '',
        agentPhone: json['agentPhone'] ?? '',
        completionRate: (json['completionRate'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message:
            'Failed to parse AgentPerformanceReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AgentPerformanceReportModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<AgentPerformanceReportModel> fromJsonList(
    List<dynamic> jsonList,
  ) {
    return jsonList
        .map((json) => AgentPerformanceReportModel.fromJson(json))
        .toList();
  }
}

class PaymentStatsModel extends PaymentStatsEntity {
  PaymentStatsModel({
    required super.byMethod,
    required super.byStatus,
    required super.totalProcessed,
    required super.totalAmount,
  });

  factory PaymentStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return PaymentStatsModel(
        byMethod: (json['byMethod'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, PaymentMethodModel.fromJson(value)),
        ),
        byStatus: (json['byStatus'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, PaymentStatusModel.fromJson(value)),
        ),
        totalProcessed: json['totalProcessed'] ?? 0,
        totalAmount: (json['totalAmount'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PaymentStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PaymentStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class PaymentMethodModel extends PaymentMethodEntity {
  PaymentMethodModel({required super.count, required super.amount});

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    try {
      return PaymentMethodModel(
        count: json['count'] ?? 0,
        amount: (json['amount'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PaymentMethodModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PaymentMethodModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class PaymentStatusModel extends PaymentStatusEntity {
  PaymentStatusModel({required super.count, required super.amount});

  factory PaymentStatusModel.fromJson(Map<String, dynamic> json) {
    try {
      return PaymentStatusModel(
        count: json['count'] ?? 0,
        amount: (json['amount'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PaymentStatusModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PaymentStatusModel,
        stackTrace: stackTrace,
      );
    }
  }
}
