// data/models/customer_dashboard_model.dart

import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/customer_dashboard_entity.dart';

class CustomerDashboardModel extends CustomerDashboardEntity {
  CustomerDashboardModel({
    required super.userInfo,
    required super.orderStats,
    required super.totalSpent,
    required super.recentOrders,
    required super.favoriteProducts,
    required super.deliveryStats,
    required super.lastUpdated,
  });

  factory CustomerDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return CustomerDashboardModel(
        userInfo: UserInfoModel.fromJson(json['userInfo']),
        orderStats: OrderStatsModel.fromJson(json['orderStats']),
        totalSpent: TotalSpentModel.fromJson(json['totalSpent']),
        recentOrders: RecentOrderModel.fromJsonList(json['recentOrders']),
        favoriteProducts: FavoriteProductModel.fromJsonList(
          json['favoriteProducts'],
        ),
        deliveryStats: DeliveryStatsModel.fromJson(json['deliveryStats']),
        lastUpdated: DateTime.parse(json['lastUpdated']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse CustomerDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CustomerDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class UserInfoModel extends UserInfoEntity {
  UserInfoModel({
    required super.id,
    required super.phone,
    required super.addresses,
    required super.memberSince,
  });

  factory UserInfoModel.fromJson(Map<String, dynamic> json) {
    try {
      return UserInfoModel(
        id: json['id'] ?? '',
        phone: json['phone'] ?? '',
        addresses: json['addresses'] ?? [],
        memberSince: DateTime.parse(json['memberSince']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse UserInfoModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: UserInfoModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class OrderStatsModel extends OrderStatsEntity {
  OrderStatsModel({
    required super.total,
    required super.pending,
    required super.confirmed,
    required super.outForDelivery,
    required super.delivered,
    required super.cancelled,
    required super.failed,
  });

  factory OrderStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderStatsModel(
        total: json['total'] ?? 0,
        pending: json['pending'] ?? 0,
        confirmed: json['confirmed'] ?? 0,
        outForDelivery: json['outForDelivery'] ?? 0,
        delivered: json['delivered'] ?? 0,
        cancelled: json['cancelled'] ?? 0,
        failed: json['failed'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class TotalSpentModel extends TotalSpentEntity {
  TotalSpentModel({
    super.id,
    required super.totalSpent,
    required super.orderCount,
    required super.avgOrderValue,
  });

  factory TotalSpentModel.fromJson(Map<String, dynamic> json) {
    try {
      return TotalSpentModel(
        id: json['_id'],
        totalSpent: (json['totalSpent'] as num).toDouble(),
        orderCount: json['orderCount'] ?? 0,
        avgOrderValue: (json['avgOrderValue'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse TotalSpentModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: TotalSpentModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class RecentOrderModel extends RecentOrderEntity {
  RecentOrderModel({
    required super.id,
    required super.items,
    required super.totalAmount,
    required super.status,
    required super.deliveryAddress,
    required super.createdAt,
    super.deliveryAgent,
  });

  factory RecentOrderModel.fromJson(Map<String, dynamic> json) {
    try {
      return RecentOrderModel(
        id: json['_id'] ?? '',
        items: OrderItemModel.fromJsonList(json['items']),
        totalAmount: (json['totalAmount'] as num).toDouble(),
        status: json['status'] ?? '',
        deliveryAddress: json['deliveryAddress'] ?? '',
        createdAt: DateTime.parse(json['createdAt']),
        deliveryAgent: json['deliveryAgent'] != null
            ? DeliveryAgentModel.fromJson(json['deliveryAgent'])
            : null,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse RecentOrderModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: RecentOrderModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<RecentOrderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => RecentOrderModel.fromJson(json)).toList();
  }
}

class OrderItemModel extends OrderItemEntity {
  OrderItemModel({
    required super.itemType,
    required super.itemId,
    required super.quantity,
    required super.id,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderItemModel(
        itemType: json['itemType'] ?? '',
        itemId: json['itemId'] ?? '',
        quantity: json['quantity'] ?? 0,
        id: json['_id'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderItemModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderItemModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<OrderItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => OrderItemModel.fromJson(json)).toList();
  }
}

class DeliveryAgentModel extends DeliveryAgentEntity {
  DeliveryAgentModel({
    required super.id,
    required super.phone,
    required super.email,
  });

  factory DeliveryAgentModel.fromJson(Map<String, dynamic> json) {
    try {
      return DeliveryAgentModel(
        id: json['_id'] ?? '',
        phone: json['phone'] ?? '',
        email: json['email'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DeliveryAgentModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DeliveryAgentModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class FavoriteProductModel extends FavoriteProductEntity {
  FavoriteProductModel({
    required super.id,
    required super.orderCount,
    required super.totalQuantity,
    required super.productDetails,
  });

  factory FavoriteProductModel.fromJson(Map<String, dynamic> json) {
    try {
      return FavoriteProductModel(
        id: ItemIdModel.fromJson(json['_id']),
        orderCount: json['orderCount'] ?? 0,
        totalQuantity: json['totalQuantity'] ?? 0,
        productDetails: ProductDetailsModel.fromJson(json['productDetails']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse FavoriteProductModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: FavoriteProductModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<FavoriteProductModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => FavoriteProductModel.fromJson(json)).toList();
  }
}

class ItemIdModel extends ItemIdEntity {
  ItemIdModel({required super.itemType, required super.itemId});

  factory ItemIdModel.fromJson(Map<String, dynamic> json) {
    try {
      return ItemIdModel(
        itemType: json['itemType'] ?? '',
        itemId: json['itemId'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse ItemIdModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: ItemIdModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class ProductDetailsModel extends ProductDetailsEntity {
  ProductDetailsModel({
    required super.id,
    required super.type,
    required super.material,
    required super.price,
    required super.availableQuantity,
    required super.productId,
  });

  factory ProductDetailsModel.fromJson(Map<String, dynamic> json) {
    try {
      return ProductDetailsModel(
        id: json['_id'] ?? '',
        type: json['type'] ?? '',
        material: json['material'] ?? '',
        price: (json['price'] as num).toDouble(),
        availableQuantity: json['availableQuantity'],
        productId: json['id'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse ProductDetailsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: ProductDetailsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class DeliveryStatsModel extends DeliveryStatsEntity {
  DeliveryStatsModel({
    required super.totalDeliveries,
    required super.avgDeliveryTimeHours,
    required super.fastestDeliveryHours,
    required super.slowestDeliveryHours,
  });

  factory DeliveryStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return DeliveryStatsModel(
        totalDeliveries: json['totalDeliveries'] ?? 0,
        avgDeliveryTimeHours: (json['avgDeliveryTimeHours'] as num).toDouble(),
        fastestDeliveryHours: (json['fastestDeliveryHours'] as num).toDouble(),
        slowestDeliveryHours: (json['slowestDeliveryHours'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DeliveryStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DeliveryStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}
