import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/agent_dashboard_entity.dart';

class AgentDashboardModel extends AgentDashboardEntity {
  AgentDashboardModel({
    required super.agentInfo,
    required super.orderStats,
    required super.earnings,
    required super.recentDeliveries,
    required super.performanceMetrics,
    required super.lastUpdated,
  });

  factory AgentDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return AgentDashboardModel(
        agentInfo: AgentInfoModel.fromJson(json['agentInfo']),
        orderStats: OrderStatsModel.fromJson(json['orderStats']),
        earnings: EarningsModel.fromJson(json['earnings']),
        recentDeliveries: RecentDeliveryModel.fromJsonList(
          json['recentDeliveries'],
        ),
        performanceMetrics: PerformanceMetricsModel.from<PERSON>son(
          json['performanceMetrics'],
        ),
        lastUpdated: DateTime.parse(json['lastUpdated']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AgentDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AgentDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class AgentInfoModel extends AgentInfoEntity {
  AgentInfoModel({
    required super.id,
    required super.phone,
    required super.email,
    required super.isOnDuty,
    required super.rating,
  });

  factory AgentInfoModel.fromJson(Map<String, dynamic> json) {
    try {
      return AgentInfoModel(
        id: json['id'] ?? '',
        phone: json['phone'] ?? '',
        email: json['email'] ?? '',
        isOnDuty: json['isOnDuty'] ?? false,
        rating: (json['rating'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AgentInfoModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AgentInfoModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class OrderStatsModel extends OrderStatsEntity {
  OrderStatsModel({
    required super.total,
    required super.completed,
    required super.pending,
    required super.completionRate,
  });

  factory OrderStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderStatsModel(
        total: json['total'] ?? 0,
        completed: json['completed'] ?? 0,
        pending: json['pending'] ?? 0,
        completionRate: (json['completionRate'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class EarningsModel extends EarningsEntity {
  EarningsModel({
    required super.id,
    required super.totalEarnings,
    required super.totalOrders,
    required super.avgOrderValue,
  });

  factory EarningsModel.fromJson(Map<String, dynamic> json) {
    try {
      return EarningsModel(
        id: json['_id'] ?? '',
        totalEarnings: (json['totalEarnings'] as num).toDouble(),
        totalOrders: json['totalOrders'] ?? 0,
        avgOrderValue: (json['avgOrderValue'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse EarningsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: EarningsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class RecentDeliveryModel extends RecentDeliveryEntity {
  RecentDeliveryModel({
    required super.id,
    required super.customer,
    required super.totalAmount,
    required super.deliveryAddress,
    required super.deliveredAt,
  });

  factory RecentDeliveryModel.fromJson(Map<String, dynamic> json) {
    try {
      return RecentDeliveryModel(
        id: json['_id'] ?? '',
        customer: CustomerModel.fromJson(json['customer']),
        totalAmount: (json['totalAmount'] as num).toDouble(),
        deliveryAddress: json['deliveryAddress'] ?? '',
        deliveredAt: DateTime.parse(json['deliveredAt']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse RecentDeliveryModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: RecentDeliveryModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<RecentDeliveryModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => RecentDeliveryModel.fromJson(json)).toList();
  }
}

class CustomerModel extends CustomerEntity {
  CustomerModel({required super.id, required super.phone});

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    try {
      return CustomerModel(id: json['_id'] ?? '', phone: json['phone'] ?? '');
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse CustomerModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CustomerModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class PerformanceMetricsModel extends PerformanceMetricsEntity {
  PerformanceMetricsModel({
    required super.monthlyStats,
    required super.overallMetrics,
  });

  factory PerformanceMetricsModel.fromJson(Map<String, dynamic> json) {
    try {
      return PerformanceMetricsModel(
        monthlyStats: MonthlyStatsModel.fromJsonList(json['monthlyStats']),
        overallMetrics: OverallMetricsModel.fromJson(json['overallMetrics']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PerformanceMetricsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PerformanceMetricsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class MonthlyStatsModel extends MonthlyStatsEntity {
  MonthlyStatsModel({
    required super.year,
    required super.month,
    required super.totalOrders,
    required super.completedOrders,
    required super.totalRevenue,
  });

  factory MonthlyStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return MonthlyStatsModel(
        year: json['_id']['year'] ?? 0,
        month: json['_id']['month'] ?? 0,
        totalOrders: json['totalOrders'] ?? 0,
        completedOrders: json['completedOrders'] ?? 0,
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse MonthlyStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: MonthlyStatsModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<MonthlyStatsModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => MonthlyStatsModel.fromJson(json)).toList();
  }
}

class OverallMetricsModel extends OverallMetricsEntity {
  OverallMetricsModel({
    required super.totalOrders,
    required super.totalCompleted,
    required super.completionRate,
    required super.totalRevenue,
    required super.avgOrderValue,
  });

  factory OverallMetricsModel.fromJson(Map<String, dynamic> json) {
    try {
      return OverallMetricsModel(
        totalOrders: json['totalOrders'] ?? 0,
        totalCompleted: json['totalCompleted'] ?? 0,
        completionRate: (json['completionRate'] as num).toDouble(),
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        avgOrderValue: (json['avgOrderValue'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OverallMetricsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OverallMetricsModel,
        stackTrace: stackTrace,
      );
    }
  }
}
