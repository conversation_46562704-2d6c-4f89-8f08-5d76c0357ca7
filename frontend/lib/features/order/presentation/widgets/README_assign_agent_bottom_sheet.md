# Assign Agent Bottom Sheet

A comprehensive Flutter widget for assigning delivery agents to orders using the `CustomSelectFieldDisplayer` component.

## Features

- **Agent Selection**: Uses `CustomSelectFieldDisplayer` for a consistent selection experience
- **Real-time Agent Filtering**: Only shows active agents who are currently on duty
- **Agent Information Display**: Shows agent contact info, vehicle details, and ratings
- **Order Details**: Displays relevant order information for context
- **Loading States**: Proper loading indicators during agent fetching and assignment
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Adapts to different screen sizes using ScreenUtil
- **Theme Integration**: Follows the app's theme and color scheme

## Usage

### Basic Usage

```dart
// Show the bottom sheet
AssignAgentBottomSheet.show(
  context: context,
  order: orderEntity,
);
```

### Usage with Callback

```dart
// Show with completion callback
AssignAgentBottomSheet.show(
  context: context,
  order: orderEntity,
  onAssignmentComplete: () {
    // Handle assignment completion
    print('Agent assigned successfully!');
    // Refresh order list, navigate, etc.
  },
);
```

### Integration in Order List

```dart
// In your order list item
ElevatedButton(
  onPressed: () => AssignAgentBottomSheet.show(
    context: context,
    order: order,
    onAssignmentComplete: () {
      // Refresh the order list
      context.orderBloc.add(GetAllOrdersEvent());
    },
  ),
  child: const Text('Assign Agent'),
)
```

## Required Dependencies

### BLoC Providers

Make sure your widget tree includes the required BLoC providers:

```dart
MultiBlocProvider(
  providers: [
    BlocProvider<UserBloc>(
      create: (context) => UserBloc(
        // Inject required use cases
        getAllUsersUseCase: context.read(),
        // ... other use cases
      ),
    ),
    BlocProvider<OrderBloc>(
      create: (context) => OrderBloc(
        // Inject required use cases including AssignAgentToOrderUseCase
        assignAgentToOrderUseCase: context.read(),
        // ... other use cases
      ),
    ),
  ],
  child: YourWidget(),
)
```

### Required Use Cases

The bottom sheet depends on these use cases:

1. **GetAllUsersUseCase**: For fetching available agents
2. **AssignAgentToOrderUseCase**: For assigning the selected agent to the order

### Required Entities

- `OrderEntity`: Must include `id`, `totalAmount`, `deliveryAddress`, and `deliveryAgent` fields
- `UserEntity`: Must include agent metadata with vehicle info and duty status

## Component Structure

### Main Components

1. **Header Section**: 
   - Title and close button
   - Order information display

2. **Agent Selection**:
   - `CustomSelectFieldDisplayer` for agent selection
   - Displays agent name, contact info, vehicle details, and rating

3. **Selected Agent Info**:
   - Shows detailed information about the selected agent
   - Includes avatar, contact details, and rating

4. **Action Button**:
   - Assign button with loading state
   - Disabled when no agent is selected

5. **Empty State**:
   - Shown when no agents are available
   - Includes refresh functionality

### Agent Filtering

The bottom sheet automatically filters agents based on:
- Role: Must be `UserRole.agent`
- Status: Must be active (`isActive: true`)
- Duty Status: Must be on duty (`isOnDuty: true`)

### Agent Display Format

- **Primary Text**: Email (if available) or phone number
- **Secondary Text**: Phone + Vehicle info + Duty status
- **Rating**: Star rating if available

## Customization

### Styling

The component uses the app's theme system:
- Primary colors from `AppColorsExtension`
- Text styles from `Theme.of(context).textTheme`
- Responsive sizing with `ScreenUtil`

### Behavior Customization

You can customize the behavior by:
- Modifying the agent filtering logic in `_handleUserBlocState`
- Changing the display format in `_getAgentDisplayName` and `_getAgentSubtitle`
- Adjusting the bottom sheet height (currently 85% of screen height)

## Error Handling

The component handles various error scenarios:

1. **Network Errors**: When fetching agents fails
2. **Assignment Errors**: When agent assignment fails
3. **Empty States**: When no agents are available
4. **Validation**: Ensures an agent is selected before assignment

## State Management

### UserBloc States

- `GetAllUsersLoading`: Shows loading indicator
- `GetAllUsersSuccess`: Populates agent list
- `GetAllUsersFailure`: Shows error message

### OrderBloc States

- `AssignAgentToOrderLoading`: Shows loading on assign button
- `AssignAgentToOrderSuccess`: Shows success message and closes sheet
- `AssignAgentToOrderFailure`: Shows error message

## Backend Integration

The component integrates with these backend endpoints:

1. **GET /api/v1/users**: Fetches agents with role filtering
2. **PUT /api/v1/orders/:id/assign-agent**: Assigns agent to order

## Testing

For testing, you can:

1. Mock the required BLoCs
2. Provide test data for orders and agents
3. Test different states (loading, success, error, empty)
4. Verify the assignment flow

## Dependencies

- `flutter_bloc`: State management
- `flutter_screenutil`: Responsive design
- `CustomSelectFieldDisplayer`: Agent selection UI
- `CustomButton`: Action buttons
- App theme system and extensions

## Notes

- The bottom sheet is modal and covers 85% of the screen height
- Agent selection is single-selection only (one agent per order)
- The component automatically refreshes agent data when opened
- Assignment success triggers both callback and bottom sheet dismissal
