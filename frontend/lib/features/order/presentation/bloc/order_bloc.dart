import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/enums/order_status.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/utils/helpers/bloc_helper.dart';
import '../../../dashboard/domain/entities/customer_dashboard_entity.dart';
import '../../domain/entities/order_entity.dart';
import '../../domain/params/assign_agent_to_order_params.dart';
import '../../domain/params/cancel_order_params.dart';
import '../../domain/params/complete_order_params.dart';
import '../../domain/params/create_order_params.dart';
import '../../domain/params/delete_order_params.dart';
import '../../domain/params/get_orders_params.dart';
import '../../domain/params/update_order_params.dart';
import '../../domain/params/validate_order_qrcode_params.dart';
import '../../domain/usecases/assign_agent_to_order_usecase.dart';
import '../../domain/usecases/cancel_order_usecase.dart';
import '../../domain/usecases/complete_order_usecase.dart';
import '../../domain/usecases/create_order_usecase.dart';
import '../../domain/usecases/delete_order_usecase.dart';
import '../../domain/usecases/get_orders_usecase.dart';
import '../../domain/usecases/update_order_usecase.dart';
import '../../domain/usecases/validate_order_qr_usecase.dart';

part 'order_event.dart';
part 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final CreateOrderUseCase createOrderUseCase;
  final GetOrdersUseCase getOrdersUseCase;
  final AssignAgentToOrderUseCase assignAgentToOrderUseCase;
  final CancelOrderUseCase cancelOrderUseCase;
  final CompleteOrderUseCase completeOrderUseCase;
  final ValidateOrderQrCodeUseCase validateOrderQrCodeUseCase;
  final UpdateOrderUseCase updateOrderUseCase;
  final DeleteOrderUseCase deleteOrderUseCase;

  OrderBloc({
    required this.createOrderUseCase,
    required this.getOrdersUseCase,
    required this.assignAgentToOrderUseCase,
    required this.cancelOrderUseCase,
    required this.completeOrderUseCase,
    required this.validateOrderQrCodeUseCase,
    required this.updateOrderUseCase,
    required this.deleteOrderUseCase,
  }) : super(OrderInitial()) {
    on<CreateOrderEvent>(_onCreateOrder);
    on<GetOrdersEvent>(_onGetOrders);
    on<AssignAgentToOrderEvent>(_onAssignAgentToOrder);
    on<CancelOrderEvent>(_onCancelOrder);
    on<CompleteOrderEvent>(_onCompleteOrder);
    on<ValidateOrderQrCodeEvent>(_onValidateOrderQrCode);
    on<UpdateOrderEvent>(_onUpdateOrder);
    on<DeleteOrderEvent>(_onDeleteOrder);
  }

  List<OrderEntity> _orders = [];
  List<OrderEntity> get orders => _orders;

  // event handlers
  Future<void> _onCreateOrder(
    CreateOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<OrderEntity, OrderState>(
      emit: emit,
      loadingState: CreateOrderLoading(),
      callUseCase: createOrderUseCase(
        params: CreateOrderParams(
          customerId: event.customerId,
          items: event.items,
          deliveryAddress: event.deliveryAddress,
          paymentMethod: event.paymentMethod,
        ),
      ),
      onSuccess: (order) =>
          const CreateOrderSuccess(message: 'Order created successfully'),
      onFailure: (failure) => CreateOrderFailure(appFailure: failure),
    );
  }

  Future<void> _onGetOrders(
    GetOrdersEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<OrderEntity>, OrderState>(
      emit: emit,
      loadingState: GetOrdersLoading(),
      callUseCase: getOrdersUseCase(
        params: GetOrdersParams(
          customerId: event.customerId,
          status: event.status,
          paymentMethod: event.paymentMethod,
          startDate: event.startDate,
          endDate: event.endDate,
        ),
      ),
      onSuccess: (orders) {
        _orders = orders;
        return GetOrdersSuccess(orders: orders);
      },
      onFailure: (failure) => GetOrdersFailure(appFailure: failure),
    );
  }

  Future<void> _onAssignAgentToOrder(
    AssignAgentToOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, OrderState>(
      emit: emit,
      loadingState: AssignAgentToOrderLoading(),
      callUseCase: assignAgentToOrderUseCase(
        params: AssignAgentToOrderParams(
          orderId: event.orderId,
          agentId: event.agentId,
        ),
      ),
      onSuccess: (_) => const AssignAgentToOrderSuccess(
        message: 'Agent assigned successfully',
      ),
      onFailure: (failure) => AssignAgentToOrderFailure(appFailure: failure),
    );
  }

  Future<void> _onCancelOrder(
    CancelOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, OrderState>(
      emit: emit,
      loadingState: CancelOrderLoading(),
      callUseCase: cancelOrderUseCase(
        params: CancelOrderParams(orderId: event.orderId),
      ),
      onSuccess: (_) =>
          const CancelOrderSuccess(message: 'Order cancelled successfully'),
      onFailure: (failure) => CancelOrderFailure(appFailure: failure),
    );
  }

  Future<void> _onCompleteOrder(
    CompleteOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, OrderState>(
      emit: emit,
      loadingState: CompleteOrderLoading(),
      callUseCase: completeOrderUseCase(
        params: CompleteOrderParams(orderId: event.orderId),
      ),
      onSuccess: (_) =>
          const CompleteOrderSuccess(message: 'Order completed successfully'),
      onFailure: (failure) => CompleteOrderFailure(appFailure: failure),
    );
  }

  Future<void> _onValidateOrderQrCode(
    ValidateOrderQrCodeEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<OrderEntity, OrderState>(
      emit: emit,
      loadingState: ValidateOrderQrCodeLoading(),
      callUseCase: validateOrderQrCodeUseCase(
        params: ValidateOrderQrCodeParams(qrCode: event.qrCode),
      ),
      onSuccess: (order) => ValidateOrderQrCodeSuccess(order: order),
      onFailure: (failure) => ValidateOrderQrCodeFailure(appFailure: failure),
    );
  }

  Future<void> _onUpdateOrder(
    UpdateOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, OrderState>(
      emit: emit,
      loadingState: UpdateOrderLoading(),
      callUseCase: updateOrderUseCase(
        params: UpdateOrderParams(
          orderId: event.orderId,
          customerId: event.customerId,
          items: event.items,
          deliveryAddress: event.deliveryAddress,
          paymentMethod: event.paymentMethod,
        ),
      ),
      onSuccess: (_) =>
          const UpdateOrderSuccess(message: 'Order updated successfully'),
      onFailure: (failure) => UpdateOrderFailure(appFailure: failure),
    );
  }

  Future<void> _onDeleteOrder(
    DeleteOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, OrderState>(
      emit: emit,
      loadingState: DeleteOrderLoading(),
      callUseCase: deleteOrderUseCase(
        params: DeleteOrderParams(orderId: event.orderId),
      ),
      onSuccess: (_) =>
          const DeleteOrderSuccess(message: 'Order deleted successfully'),
      onFailure: (failure) => DeleteOrderFailure(appFailure: failure),
    );
  }
}
