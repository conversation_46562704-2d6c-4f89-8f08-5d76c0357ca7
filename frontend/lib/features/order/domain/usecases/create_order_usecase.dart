import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../entities/order_entity.dart';
import '../params/create_order_params.dart';
import '../repositories/order_repository.dart';

class CreateOrderUseCase implements UseCase<OrderEntity, CreateOrderParams> {
  final OrderRepository repository;

  const CreateOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<OrderEntity> call({
    required CreateOrderParams params,
  }) async {
    final response = await repository.createOrder(
      customerId: params.customerId,
      items: params.items,
      deliveryAddress: params.deliveryAddress,
      paymentMethod: params.paymentMethod,
    );
    return response;
  }
}
