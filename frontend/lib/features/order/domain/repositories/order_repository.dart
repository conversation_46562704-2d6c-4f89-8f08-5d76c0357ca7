import '../../../../core/enums/order_status.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../dashboard/domain/entities/customer_dashboard_entity.dart';
import '../entities/order_entity.dart';

abstract class OrderRepository {
  FutureEitherFailOr<String> createOrder({
    required String? customerId,
    required List<OrderItemEntity> items,
    required String deliveryAddress,
    String? paymentMethod,
  });

  FutureEitherFailOr<List<OrderEntity>> getOrders({
    String? customerId,
    OrderStatus? status,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  });

  FutureEitherFailOr<void> assignAgentToOrder({
    required String orderId,
    required String agentId,
  });

  FutureEitherFailOr<void> cancelOrder({required String orderId});

  FutureEitherFailOr<void> completeOrder({required String orderId});

  FutureEitherFailOr<OrderEntity> validateOrderInQRCode({
    required String qrCode,
  });

  FutureEitherFailOr<void> updateOrder({
    required String orderId,
    String? customerId,
    List<OrderItemEntity>? items,
    String? deliveryAddress,
    String? paymentMethod,
  });

  FutureEitherFailOr<void> deleteOrder({required String orderId});
}
