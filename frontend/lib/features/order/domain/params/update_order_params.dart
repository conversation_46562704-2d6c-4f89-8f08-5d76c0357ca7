import 'package:equatable/equatable.dart';

import '../../../dashboard/domain/entities/customer_dashboard_entity.dart';

class UpdateOrderParams extends Equatable {
  final String orderId;
  final String? customerId;
  final List<OrderItemEntity>? items;
  final String? deliveryAddress;
  final String? paymentMethod;

  const UpdateOrderParams({
    required this.orderId,
    this.customerId,
    this.items,
    this.deliveryAddress,
    this.paymentMethod,
  });

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      if (customerId != null && customerId!.trim().isNotEmpty)
        'customerId': customerId,
      if (items != null) 'items': items!.map((item) => item.toJson()).toList(),
      if (deliveryAddress != null) 'deliveryAddress': deliveryAddress,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
    };
  }

  @override
  List<Object?> get props => [
    orderId,
    customerId,
    items,
    deliveryAddress,
    paymentMethod,
  ];
}
