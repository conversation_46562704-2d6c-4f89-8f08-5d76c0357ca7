/*

{
            "_id": "6852cd462893bded98587f51",
            "customer": {
                "_id": "684f671dabea9ca7a8c7f3e9",
                "phone": "+252613656021",
                "role": "customer",
                "addresses": [],
                "isActive": true,
                "createdAt": "2025-06-16T00:36:45.705Z",
                "updatedAt": "2025-06-18T11:49:31.867Z",
                "__v": 0,
                "otp": null
            },
            "payment": "6852cd462893bded98587f4f",
            "items": [
                {
                    "itemType": "CYLINDER",
                    "itemId": "6852cbf0b75ba863a91736b2",
                    "quantity": 2,
                    "_id": "6852cd462893bded98587f52"
                }
            ],
            "totalAmount": 91.98,
            "status": "DELIVERED",
            "deliveryAddress": "123 Main Street, Hargeisa, Somaliland",
            "paymentMethod": "waafi_preauth",
            "createdAt": "2025-06-18T14:29:26.781Z",
            "updatedAt": "2025-06-18T14:36:01.263Z",
            "__v": 0,
            "qrCode": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcmRlcklkIjoiNjg1MmNkNDYyODkzYmRlZDk4NTg3ZjUxIiwiaWF0IjoxNzUwMjU2OTY3LCJleHAiOjE3NTAzNDMzNjd9.0u41_62cj6kx1qXFEkwjDx2XDSI8eR6x-YRyY6xPXjk",
            "qrCodeExpiresAt": "2025-06-19T14:29:27.417Z",
            "deliveryAgent": {
                "_id": "6852ce18cc36bca49308b922",
                "phone": "+252619124489",
                "email": "<EMAIL>",
                "role": "agent",
                "isActive": true,
                "addresses": [],
                "createdAt": "2025-06-18T14:32:56.421Z",
                "updatedAt": "2025-06-18T17:38:34.817Z",
                "__v": 0,
                "otp": null
            },
            "deliveredAt": "2025-06-18T14:36:01.262Z"
        }

*/

import '../../../../core/enums/order_status.dart';
import '../../../authentication/domain/entities/user_entity.dart';
import '../../../dashboard/domain/entities/customer_dashboard_entity.dart';

class OrderEntity {
  final String id;
  final UserEntity customer;
  final String payment;
  final List<OrderItemEntity> items;
  final double totalAmount;
  final OrderStatus status;
  final String deliveryAddress;
  final String paymentMethod;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deliveredAt;
  final DateTime? cancelledAt;
  final DateTime? failedAt;
  final String? failureReason;
  final int? verificationAttempts;
  final DateTime? assignedAt;
  final DateTime? verifiedAt;
  final String? qrCode;
  final DateTime? qrCodeExpiresAt;
  final UserEntity? deliveryAgent;

  OrderEntity({
    required this.id,
    required this.customer,
    required this.payment,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.deliveryAddress,
    required this.paymentMethod,
    required this.createdAt,
    required this.updatedAt,
    this.verificationAttempts,
    this.assignedAt,
    this.verifiedAt,
    this.deliveredAt,
    this.cancelledAt,
    this.failedAt,
    this.failureReason,
    this.qrCode,
    this.qrCodeExpiresAt,
    this.deliveryAgent,
  });

  @override
  String toString() {
    return 'OrderEntity(id: $id, customer: $customer, payment: $payment, items: $items, totalAmount: $totalAmount, status: $status, deliveryAddress: $deliveryAddress, paymentMethod: $paymentMethod, createdAt: $createdAt, updatedAt: $updatedAt, deliveredAt: $deliveredAt, cancelledAt: $cancelledAt, failedAt: $failedAt, failureReason: $failureReason, verificationAttempts: $verificationAttempts, assignedAt: $assignedAt, verifiedAt: $verifiedAt, qrCode: $qrCode, qrCodeExpiresAt: $qrCodeExpiresAt, deliveryAgent: $deliveryAgent)';
  }
}
