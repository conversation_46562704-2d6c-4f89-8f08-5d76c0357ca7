import '../../../../core/enums/order_status.dart';
import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../authentication/data/models/user_model.dart';
import '../../../dashboard/domain/entities/customer_dashboard_entity.dart';
import '../../domain/entities/order_entity.dart';

class OrderModel extends OrderEntity {
  OrderModel({
    required super.id,
    required super.customer,
    required super.payment,
    required super.items,
    required super.totalAmount,
    required super.status,
    required super.deliveryAddress,
    required super.paymentMethod,
    required super.createdAt,
    required super.updatedAt,
    super.deliveredAt,
    super.cancelledAt,
    super.failedAt,
    super.failureReason,
    super.qrCode,
    super.qrCodeExpiresAt,
    super.deliveryAgent,
    super.verificationAttempts,
    super.assignedAt,
    super.verifiedAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderModel(
        id: json['_id'] ?? '',
        customer: UserModel.fromJson(json['customer']),
        payment: json['payment'] ?? '',
        items: OrderItemModel.fromJsonList(json['items']),
        status: fromString(json['status'] ?? ''),
        totalAmount: (json['totalAmount'] as num).toDouble(),
        deliveryAddress: json['deliveryAddress'] ?? '',
        paymentMethod: json['paymentMethod'] ?? '',
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        deliveredAt: json['deliveredAt'] != null
            ? DateTime.parse(json['deliveredAt'])
            : null,
        cancelledAt: json['cancelledAt'] != null
            ? DateTime.parse(json['cancelledAt'])
            : null,
        failedAt: json['failedAt'] != null
            ? DateTime.parse(json['failedAt'])
            : null,
        failureReason: json['failureReason'],
        qrCode: json['qrCode'],
        qrCodeExpiresAt: json['qrCodeExpiresAt'] != null
            ? DateTime.parse(json['qrCodeExpiresAt'])
            : null,
        deliveryAgent: json['deliveryAgent'] != null
            ? UserModel.fromJson(json['deliveryAgent'])
            : null,

        verificationAttempts: json['verificationAttempts'] is int
            ? json['verificationAttempts']
            : int.tryParse(json['verificationAttempts']?.toString() ?? '0') ??
                  0,
        assignedAt: json['AssignedAt'] != null
            ? DateTime.parse(json['assignedAt'])
            : null,
        verifiedAt: json['verifiedAt'] != null
            ? DateTime.parse(json['verifiedAt'])
            : null,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<OrderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => OrderModel.fromJson(json)).toList();
  }
}

class OrderItemModel extends OrderItemEntity {
  OrderItemModel({
    required super.itemType,
    required super.itemId,
    required super.quantity,
    required super.id,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderItemModel(
        itemType: json['itemType'] ?? '',
        itemId: json['itemId'] ?? '',
        quantity: json['quantity'] ?? 0,
        id: json['_id'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderItemModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderItemModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<OrderItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => OrderItemModel.fromJson(json)).toList();
  }
}
