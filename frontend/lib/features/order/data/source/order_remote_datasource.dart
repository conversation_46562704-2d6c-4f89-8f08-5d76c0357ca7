import 'package:fpdart/fpdart.dart';
import 'package:frontend/features/order/data/models/order_model.dart';

import '../../../../core/constants/api_end_points.dart';
import '../../../../core/enums/http_method.dart';
import '../../../../core/enums/order_status.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/errors/http_error_handler.dart';
import '../../../../core/network/api_client/dio_api_client.dart';
import '../../../../core/utils/helpers/request_data.dart';
import '../../../dashboard/domain/entities/customer_dashboard_entity.dart';

abstract class OrderRemoteDataSource {
  FutureEitherFailOr<String> createOrder({
    String? customerId,
    required List<OrderItemEntity> items,
    required String deliveryAddress,
    String? paymentMethod,
  });

  FutureEitherFailOr<List<OrderModel>> getOrders({
    String? customerId,
    OrderStatus? status,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  });

  FutureEitherFailOr<void> assignAgentToOrder({
    required String orderId,
    required String agentId,
  });

  FutureEitherFailOr<void> cancelOrder({required String orderId});

  FutureEitherFailOr<void> completeOrder({required String orderId});

  FutureEitherFailOr<OrderModel> validateOrderInQRCode({
    required String qrCode,
  });

  FutureEitherFailOr<void> updateOrder({
    required String orderId,
    String? customerId,
    List<OrderItemEntity>? items,
    String? deliveryAddress,
    String? paymentMethod,
  });

  FutureEitherFailOr<void> deleteOrder({required String orderId});
}

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const OrderRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<String> createOrder({
    String? customerId,
    required List<OrderItemEntity> items,
    required String deliveryAddress,
    String? paymentMethod,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.createOrder,
        data: RequestData.json({
          if (customerId != null && customerId.trim().isNotEmpty)
            'customerId': customerId,
          'items': items.map((item) => item.toJson()).toList(),
          'deliveryAddress': deliveryAddress,
          if (paymentMethod != null) 'paymentMethod': paymentMethod,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final message = apiResponse.apiMessage;
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<List<OrderModel>> getOrders({
    String? customerId,
    OrderStatus? status,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => OrderModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getOrders,
        queryParameters: {
          if (customerId != null) 'customerId': customerId,
          if (status != null) 'status': status.name,
          if (paymentMethod != null) 'paymentMethod': paymentMethod,
          if (startDate != null) 'startDate': startDate.toIso8601String(),
          if (endDate != null) 'endDate': endDate.toIso8601String(),
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final orders = apiResponse.getNonNullableData();
      return right(orders);
    });
  }

  @override
  FutureEitherFailOr<void> assignAgentToOrder({
    required String orderId,
    required String agentId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.assignAgentToOrder(orderId),
        data: RequestData.json({'agentId': agentId}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> cancelOrder({required String orderId}) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.cancelOrder(orderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> completeOrder({required String orderId}) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.completeOrder(orderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<OrderModel> validateOrderInQRCode({
    required String qrCode,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => OrderModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.validateOrderInQRCode,
        data: RequestData.json({'qrCode': qrCode}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final order = apiResponse.getNonNullableData();
      return right(order);
    });
  }

  @override
  FutureEitherFailOr<void> updateOrder({
    required String orderId,
    String? customerId,
    List<OrderItemEntity>? items,
    String? deliveryAddress,
    String? paymentMethod,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.updateOrder(orderId),
        data: RequestData.json({
          if (customerId != null && customerId.trim().isNotEmpty)
            'customerId': customerId,
          if (items != null)
            'items': items.map((item) => item.toJson()).toList(),
          if (deliveryAddress != null) 'deliveryAddress': deliveryAddress,
          if (paymentMethod != null) 'paymentMethod': paymentMethod,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> deleteOrder({required String orderId}) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.deleteOrder(orderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }
}
