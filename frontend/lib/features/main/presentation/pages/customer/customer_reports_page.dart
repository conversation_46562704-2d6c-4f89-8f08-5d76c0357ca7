import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/app_date_extensions.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/customer_dashboard_entity.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';
import 'package:intl/intl.dart';

class CustomerReportsPage extends StatefulWidget {
  const CustomerReportsPage({super.key});

  @override
  State<CustomerReportsPage> createState() => _CustomerReportsPageState();
}

class _CustomerReportsPageState extends State<CustomerReportsPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  void _loadDashboardData() {
    // Load customer dashboard data
    context.read<DashboardBloc>().add(GetCustomerDashboardEvent());

    // Load current user data
    context.read<UserBloc>().add(const GetCurrentUserEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        title: const Text('My Reports'),
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        foregroundColor: context.appColors.textColor,
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            _loadDashboardData();
          },
          child: MultiBlocListener(
            listeners: [
              BlocListener<DashboardBloc, DashboardState>(
                listener: (context, state) {
                  if (state is GetCustomerDashboardFailure) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.appFailure.getErrorMessage()),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
              ),
            ],
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(16.w),
              child: BlocBuilder<DashboardBloc, DashboardState>(
                builder: (context, dashboardState) {
                  return BlocBuilder<AuthenticationBloc, AuthenticationState>(
                    builder: (context, authState) {
                      UserEntity? user;
                      if (authState is Authenticated &&
                          authState.user != null) {
                        user = authState.user!;
                      }

                      if (dashboardState is GetCustomerDashboardLoading) {
                        return _buildLoadingState();
                      }

                      if (dashboardState is GetCustomerDashboardSuccess) {
                        return _buildReportsContent(
                          context,
                          dashboardState.dashboard,
                          user,
                        );
                      }

                      if (dashboardState is GetCustomerDashboardFailure) {
                        return _buildErrorState(
                          context,
                          dashboardState.appFailure.getErrorMessage(),
                        );
                      }

                      // Initial state - show loading
                      return _buildLoadingState();
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        SizedBox(height: 100.h),
        const Center(child: CircularProgressIndicator()),
        SizedBox(height: 16.h),
        Text(
          'Loading your reports...',
          style: TextStyle(color: Colors.grey[600], fontSize: 16.sp),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Column(
      children: [
        SizedBox(height: 100.h),
        Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
        SizedBox(height: 16.h),
        Text(
          'Failed to load reports',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          message,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 24.h),
        ElevatedButton(
          onPressed: _loadDashboardData,
          child: const Text('Retry'),
        ),
      ],
    );
  }

  Widget _buildReportsContent(
    BuildContext context,
    CustomerDashboardEntity dashboard,
    UserEntity? user,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatsCards(context, dashboard),
        SizedBox(height: 24.h),
        _buildRecentOrders(context, dashboard.recentOrders),
        SizedBox(height: 24.h),
        _buildFavoriteProducts(context, dashboard.favoriteProducts),
        SizedBox(height: 24.h),
        _buildDeliveryStats(context, dashboard.deliveryStats),
      ],
    );
  }

  Widget _buildStatsCards(
    BuildContext context,
    CustomerDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Statistics',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Total Orders',
                dashboard.orderStats.total.toString(),
                Icons.shopping_bag,
                context.appColors.primaryColor,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                context,
                'Total Spent',
                '\$${dashboard.totalSpent.totalSpent.toStringAsFixed(2)}',
                Icons.attach_money,
                Colors.green,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Pending Orders',
                dashboard.orderStats.pending.toString(),
                Icons.pending,
                Colors.orange,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                context,
                'Avg Order Value',
                '\$${dashboard.totalSpent.avgOrderValue.toStringAsFixed(2)}',
                Icons.trending_up,
                Colors.blue,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, color: color, size: 20.sp),
              ),
              const Spacer(),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentOrders(
    BuildContext context,
    List<RecentOrderEntity> orders,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Orders',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        if (orders.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 48.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(height: 12.h),
                Text(
                  'No recent orders',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Your order history will appear here',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else
          Column(
            children: orders
                .map((order) => _buildOrderCard(context, order))
                .toList(),
          ),
      ],
    );
  }

  Widget _buildOrderCard(BuildContext context, RecentOrderEntity order) {
    final statusColor = _getStatusColor(order.status);
    final formattedDate = DateFormat('MMM dd, yyyy').format(order.createdAt);

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order #${order.id.substring(0, 8)}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  order.status.toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '\$${order.totalAmount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            formattedDate,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          if (order.items.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              '${order.items.length} item${order.items.length != 1 ? 's' : ''}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'out_for_delivery':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildFavoriteProducts(
    BuildContext context,
    List<FavoriteProductEntity> products,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Favorite Products',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        if (products.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.favorite_outline,
                  size: 48.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(height: 12.h),
                Text(
                  'No favorite products yet',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Products you order frequently will appear here',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else
          SizedBox(
            height: 120.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];
                return _buildProductCard(context, product);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildProductCard(
    BuildContext context,
    FavoriteProductEntity product,
  ) {
    return Container(
      width: 150.w,
      margin: EdgeInsets.only(right: 12.w),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${product.productDetails.type} - ${product.productDetails.material}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 8.h),
          Text(
            '\$${product.productDetails.price.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'Ordered ${product.orderCount} times',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryStats(BuildContext context, DeliveryStatsEntity stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Performance',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildDeliveryStatItem(
                      context,
                      'Total Deliveries',
                      stats.totalDeliveries.toString(),
                      Icons.local_shipping,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: _buildDeliveryStatItem(
                      context,
                      'Avg Delivery Time',
                      '${stats.avgDeliveryTimeHours.toStringAsFixed(1)}h',
                      Icons.schedule,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Row(
                children: [
                  Expanded(
                    child: _buildDeliveryStatItem(
                      context,
                      'Fastest Delivery',
                      '${stats.fastestDeliveryHours.toStringAsFixed(1)}h',
                      Icons.speed,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: _buildDeliveryStatItem(
                      context,
                      'Slowest Delivery',
                      '${stats.slowestDeliveryHours.toStringAsFixed(1)}h',
                      Icons.schedule_outlined,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDeliveryStatItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, size: 24.sp, color: context.appColors.primaryColor),
        SizedBox(height: 8.h),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.subtextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
