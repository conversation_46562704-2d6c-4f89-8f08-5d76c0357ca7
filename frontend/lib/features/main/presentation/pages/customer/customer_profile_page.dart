import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';

import '../../../../authentication/presentation/pages/login_page.dart';

class CustomerProfilePage extends StatefulWidget {
  const CustomerProfilePage({super.key});

  @override
  State<CustomerProfilePage> createState() => _CustomerProfilePageState();
}

class _CustomerProfilePageState extends State<CustomerProfilePage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        UserEntity? user;
        if (state is Authenticated && state.user != null) {
          user = state.user!;
        }

        return Scaffold(
          backgroundColor: context.appColors.backgroundColor,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileHeader(context, user),
                  SizedBox(height: 24.h),
                  _buildAccountSection(context, user),
                  SizedBox(height: 24.h),
                  _buildPreferencesSection(context),
                  SizedBox(height: 24.h),
                  _buildSupportSection(context),
                  SizedBox(height: 24.h),
                  _buildLogoutSection(context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(BuildContext context, UserEntity? user) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            appColors.primaryColor,
            appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32.r,
            backgroundColor: appColors.whiteColor.withValues(alpha: 0.2),
            child: Icon(Icons.person, color: appColors.whiteColor, size: 32.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user?.phone ?? 'Customer',
                  style: textTheme.headlineSmall?.copyWith(
                    color: appColors.whiteColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                if (user?.email != null)
                  Text(
                    user!.email!,
                    style: textTheme.bodyMedium?.copyWith(
                      color: appColors.whiteColor.withValues(alpha: 0.9),
                    ),
                  ),
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: appColors.whiteColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    'Customer',
                    style: TextStyle(
                      color: appColors.whiteColor,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _editProfile(context, user),
            icon: Icon(Icons.edit, color: appColors.whiteColor, size: 24.sp),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context, UserEntity? user) {
    return _buildSection(context, 'Account Information', Icons.account_circle, [
      _buildInfoTile(
        context,
        'Phone Number',
        user?.phone ?? 'Not provided',
        Icons.phone,
        () => _editPhone(context),
      ),
      _buildInfoTile(
        context,
        'Email Address',
        user?.email ?? 'Not provided',
        Icons.email,
        () => _editEmail(context),
      ),
    ]);
  }

  Widget _buildPreferencesSection(BuildContext context) {
    return _buildSection(context, 'Preferences', Icons.settings, [
      _buildActionTile(
        context,
        'Notifications',
        'Manage notification preferences',
        Icons.notifications,
        Colors.orange,
        () => _manageNotifications(context),
      ),
      _buildActionTile(
        context,
        'Language',
        'Change app language',
        Icons.language,
        Colors.purple,
        () => _changeLanguage(context),
      ),
      _buildActionTile(
        context,
        'Theme',
        'Switch between light and dark mode',
        Icons.palette,
        Colors.indigo,
        () => _changeTheme(context),
      ),
    ]);
  }

  Widget _buildSupportSection(BuildContext context) {
    return _buildSection(context, 'Support & Help', Icons.help, [
      _buildActionTile(
        context,
        'Help Center',
        'Find answers to common questions',
        Icons.help_center,
        Colors.blue,
        () => _openHelpCenter(context),
      ),
      _buildActionTile(
        context,
        'Contact Support',
        'Get help from our support team',
        Icons.support_agent,
        Colors.green,
        () => _contactSupport(context),
      ),
      _buildActionTile(
        context,
        'Rate App',
        'Rate and review our app',
        Icons.star,
        Colors.orange,
        () => _rateApp(context),
      ),
      _buildActionTile(
        context,
        'Terms & Privacy',
        'View terms of service and privacy policy',
        Icons.policy,
        Colors.purple,
        () => _viewTermsAndPrivacy(context),
      ),
    ]);
  }

  Widget _buildLogoutSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.logout, color: Colors.red, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'Account Actions',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          BlocListener<AuthenticationBloc, AuthenticationState>(
            listener: (context, state) {
              if (state is Unauthenticated) {
                context.pushAndRemoveUntilRoute(const LoginPage());
              }
            },
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _logout(context),
                icon: const Icon(Icons.logout, color: Colors.red),
                label: const Text(
                  'Logout',
                  style: TextStyle(color: Colors.red),
                ),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Colors.red),
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: context.appColors.primaryColor, size: 20.sp),
            SizedBox(width: 8.w),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Container(
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildInfoTile(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: context.appColors.subtextColor,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(icon, color: color, size: 20.sp),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: context.appColors.subtextColor,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  void _editProfile(BuildContext context, UserEntity? user) {
    // TODO: Navigate to edit profile page
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Edit profile functionality')));
  }

  void _editPhone(BuildContext context) {
    // TODO: Show edit phone dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Edit phone number')));
  }

  void _editEmail(BuildContext context) {
    // TODO: Show edit email dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Edit email address')));
  }

  void _manageAddresses(BuildContext context) {
    // TODO: Navigate to addresses management page
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Manage addresses')));
  }

  void _setDefaultAddress(BuildContext context) {
    // TODO: Show default address selection
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Set default address')));
  }

  void _manageNotifications(BuildContext context) {
    // TODO: Navigate to notification settings
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Manage notifications')));
  }

  void _changeLanguage(BuildContext context) {
    // TODO: Show language selection dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Change language')));
  }

  void _changeTheme(BuildContext context) {
    // TODO: Show theme selection dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Change theme')));
  }

  void _openHelpCenter(BuildContext context) {
    // TODO: Navigate to help center
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Open help center')));
  }

  void _contactSupport(BuildContext context) {
    // TODO: Navigate to support contact
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Contact support')));
  }

  void _rateApp(BuildContext context) {
    // TODO: Open app store for rating
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Rate app')));
  }

  void _viewTermsAndPrivacy(BuildContext context) {
    // TODO: Navigate to terms and privacy page
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('View terms and privacy')));
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logou?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthenticationBloc>().add(LogoutEvent());
            },
            child: const Text('Logout', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
