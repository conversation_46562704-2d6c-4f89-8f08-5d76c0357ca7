import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/logger/app_logger.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/cylinder_status.dart';
import 'package:frontend/core/enums/cylinder_type.dart';
import 'package:frontend/core/enums/package_status.dart';
import 'package:frontend/core/enums/spare_part_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_list_grid_view.dart';

import '../../../../../core/enums/layout_type.dart';
import 'product_detail_page.dart';

class ProductItem {
  final String id;
  final String name;
  final double price;
  final String imageUrl;
  final String description;
  final bool isAvailable;
  final VoidCallback onTap;

  ProductItem({
    required this.id,
    required this.name,
    required this.price,
    required this.imageUrl,
    required this.description,
    required this.isAvailable,
    required this.onTap,
  });
}

class CustomerHomePage extends StatefulWidget {
  const CustomerHomePage({super.key});

  @override
  State<CustomerHomePage> createState() => _CustomerHomePageState();
}

class _CustomerHomePageState extends State<CustomerHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadInventoryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadInventoryData({bool forceRefresh = false}) {
    final inventoryBloc = context.inventoryBloc;
    final canRefetchCylinders = forceRefresh || inventoryBloc.cylinders.isEmpty;
    final canRefetchPackages = forceRefresh || inventoryBloc.packages.isEmpty;
    final canRefetchSpareParts =
        forceRefresh || inventoryBloc.spareParts.isEmpty;

    // Load all inventory data for the tabs
    if (canRefetchCylinders) {
      context.read<InventoryBloc>().add(
        const GetCylindersEvent(status: CylinderStatus.active, limit: 20),
      );
    }
    if (canRefetchPackages) {
      // print(' Loading packages ');
      AppLogger().info('Loading packages');
      context.read<InventoryBloc>().add(
        const GetPackagesEvent(status: PackageStatus.active, limit: 20),
      );
    }
    if (canRefetchSpareParts) {
      // print(' Loading spare parts ');
      AppLogger().info('Loading spare parts');
      context.read<InventoryBloc>().add(
        const GetSparePartsEvent(status: SparePartStatus.available, limit: 20),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: SafeArea(
        child: RefreshIndicator.adaptive(
          onRefresh: () async {
            _loadInventoryData(forceRefresh: true);
          },
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              SliverToBoxAdapter(child: _buildHeader(context)),
              SliverToBoxAdapter(child: _buildTabBar(context)),
              SliverFillRemaining(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildCylindersTab(context),
                    _buildPackagesTab(context),
                    _buildSparePartsTab(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, authState) {
        UserEntity? user;
        if (authState is Authenticated && authState.user != null) {
          user = authState.user!;
        }

        return Container(
          padding: EdgeInsets.all(20.w),
          margin: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                context.appColors.primaryColor,
                context.appColors.primaryColor.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back!',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      user?.phone ?? 'Guest',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Discover our products',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.local_gas_station,
                  color: Colors.white,
                  size: 32.sp,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: context.appColors.primaryColor,
        unselectedLabelColor: context.appColors.subtextColor,
        indicatorColor: context.appColors.primaryColor,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: const [
          Tab(text: 'Gas Cylinders'),
          Tab(text: 'Full Package'),
          Tab(text: 'Spare Parts'),
        ],
      ),
    );
  }

  Widget _buildCylindersTab(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetCylindersLoading ||
            current is GetCylindersSuccess ||
            current is GetCylindersFailure;
      },
      builder: (context, state) {
        if (state is GetCylindersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetCylindersFailure) {
          return _buildErrorWidget(context, 'Failed to load cylinders', () {
            context.read<InventoryBloc>().add(
              const GetCylindersEvent(status: CylinderStatus.active, limit: 20),
            );
          });
        }

        // if (state is GetCylindersSuccess) {
        // final cylinders = state.cylinders;
        final cylinders = context.inventoryBloc.cylinders;
        return _buildProducList(
          context,
          // state.
          cylinders
              .map(
                (cylinder) => ProductItem(
                  id: cylinder.id,
                  name: '${cylinder.type.name} - ${cylinder.material.name}',
                  price: cylinder.price,
                  imageUrl: cylinder.formattedImageUrl,
                  description: cylinder.description,
                  isAvailable: cylinder.status == CylinderStatus.active,
                  onTap: () => _onProductTap(context, cylinder.id, 'cylinder'),
                ),
              )
              .toList(),
          emptyWidgetMessage: 'No cylinders available',
        );
        // }

        // return _buildEmptyWidget(context, 'No cylinders available');
      },
    );
  }

  Widget _buildPackagesTab(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetPackagesLoading ||
            current is GetPackagesSuccess ||
            current is GetPackagesFailure;
      },
      builder: (context, state) {
        if (state is GetPackagesLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetPackagesFailure) {
          return _buildErrorWidget(context, 'Failed to load packages', () {
            context.read<InventoryBloc>().add(
              const GetPackagesEvent(status: PackageStatus.active, limit: 20),
            );
          });
        }

        // if (state is GetPackagesSuccess) {
        final packages = context.inventoryBloc.packages;
        return _buildProducList(
          context,
          // state.
          packages
              .map(
                (package) => ProductItem(
                  id: package.id,
                  name: package.name,
                  price: package.totalPrice,
                  imageUrl: package.imageUrl,
                  description: package.description,
                  isAvailable: package.status == PackageStatus.active,
                  onTap: () => _onProductTap(context, package.id, 'package'),
                ),
              )
              .toList(),
          emptyWidgetMessage: 'No packages available',
        );

        // return _buildEmptyWidget(context, 'No packages available');
      },
    );
  }

  Widget _buildSparePartsTab(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetSparePartsLoading ||
            current is GetSparePartsSuccess ||
            current is GetSparePartsFailure;
      },
      builder: (context, state) {
        if (state is GetSparePartsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetSparePartsFailure) {
          return _buildErrorWidget(context, 'Failed to load spare parts', () {
            context.read<InventoryBloc>().add(
              const GetSparePartsEvent(
                status: SparePartStatus.available,
                limit: 20,
              ),
            );
          });
        }

        // if (state is GetSparePartsSuccess) {
        final spareParts = context.inventoryBloc.spareParts;
        return _buildProducList(
          context,
          // state.
          spareParts
              .map(
                (sparePart) => ProductItem(
                  id: sparePart.id,
                  name: sparePart.name,
                  price: sparePart.price,
                  imageUrl: '',
                  description: sparePart.description,
                  isAvailable: sparePart.status == SparePartStatus.available,
                  onTap: () =>
                      _onProductTap(context, sparePart.id, 'spare_part'),
                ),
              )
              .toList(),
          emptyWidgetMessage: 'No spare parts available',
        );

        // return _buildEmptyWidget(context, 'No spare parts available');
      },
    );
  }

  Widget _buildProducList(
    BuildContext context,
    List<ProductItem> products, {
    String emptyWidgetMessage = 'No products available',
  }) {
    return CustomListGridView(
      isEmpty: products.isEmpty,
      isLoading: false,
      showFooter: false,
      items: products,
      itemCount: products.length,
      layoutType: LayoutType.listView,
      padding: EdgeInsets.all(16.w),
      itemBuilder: (context, product) =>
          _buildProductListItem(context, product),
      emptyDataBuilder: () => _buildEmptyWidget(context, emptyWidgetMessage),
      onRefresh: () {
        _loadInventoryData();
      },
    );
  }

  Widget _buildProductListItem(BuildContext context, ProductItem product) {
    return GestureDetector(
      onTap: product.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.appColors.surfaceColor,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: context.appColors.dividerColor),
        ),
        child: Row(
          children: [
            // Product Image
            Container(
              width: 80.w,
              height: 80.h,
              margin: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: product.imageUrl.trim().isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: Image.network(
                        product.imageUrl,
                        // fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.local_gas_station,
                          size: 32.sp,
                          color: context.appColors.primaryColor,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.local_gas_station,
                      size: 32.sp,
                      color: context.appColors.primaryColor,
                    ),
            ),
            // Product Details
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    if (product.description.isNotEmpty) ...[
                      Text(
                        product.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 8.h),
                    ],
                    Row(
                      children: [
                        Text(
                          '\$${product.price.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: context.appColors.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const Spacer(),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: product.isAvailable
                                ? Colors.green.withValues(alpha: 0.1)
                                : Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          child: Text(
                            product.isAvailable ? 'Available' : 'Out of Stock',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: product.isAvailable
                                      ? Colors.green
                                      : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            // Arrow Icon
            Padding(
              padding: EdgeInsets.only(right: 12.w),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 16.sp,
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(
    BuildContext context,
    String message,
    VoidCallback onRetry,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
        ],
      ),
    );
  }

  void _onProductTap(
    BuildContext context,
    String productId,
    String productType,
  ) {
    // Navigate to full screen product detail page
    context.pushRoute(
      ProductDetailPage(productId: productId, productType: productType),
    );
  }
}
