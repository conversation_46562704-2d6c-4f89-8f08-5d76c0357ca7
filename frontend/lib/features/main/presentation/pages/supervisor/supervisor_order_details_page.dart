import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/dashboard/domain/entities/customer_dashboard_entity.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:intl/intl.dart';

import '../../../../../core/utils/helpers/bottom_sheet_helper.dart';

class SupervisorOrderDetailsPage extends StatefulWidget {
  final OrderEntity order;

  const SupervisorOrderDetailsPage({super.key, required this.order});

  @override
  State<SupervisorOrderDetailsPage> createState() =>
      _SupervisorOrderDetailsPageState();
}

class _SupervisorOrderDetailsPageState
    extends State<SupervisorOrderDetailsPage> {
  @override
  Widget build(BuildContext context) {
    print(widget.order.assignedAt);
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Order Details',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (_canAssignAgent(widget.order))
            IconButton(
              onPressed: () => _showAssignAgentDialog(context, widget.order),
              icon: Icon(
                Icons.assignment,
                color: context.appColors.primaryColor,
              ),
              tooltip: 'Assign Agent',
            ),
        ],
      ),
      body: BlocListener<OrderBloc, OrderState>(
        listener: (context, state) {
          if (state is AssignAgentToOrderSuccess) {
            SnackBarHelper.showSuccessSnackBar(context, message: state.message);
            Navigator.pop(context); // Go back to refresh the list
          } else if (state is AssignAgentToOrderFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOrderHeader(context),
              SizedBox(height: 20.h),
              _buildCustomerInfo(context),
              SizedBox(height: 20.h),
              _buildOrderItems(context),
              SizedBox(height: 20.h),
              _buildDeliveryInfo(context),
              SizedBox(height: 20.h),
              _buildPaymentInfo(context),
              SizedBox(height: 20.h),
              _buildOrderTracking(context),
              if (_canAssignAgent(widget.order)) ...[
                SizedBox(height: 20.h),
                _buildAssignAgentSection(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeader(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Order #${widget.order.id.substring(widget.order.id.length - 6)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(widget.order.status.name),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    widget.order.status.label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Created: ${DateFormat('MMM dd, yyyy - hh:mm a').format(widget.order.createdAt)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Icons.attach_money,
                  size: 16.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Total Amount: \$${widget.order.totalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoRow(
              context,
              Icons.person,
              'Phone',
              widget.order.customer.phone,
            ),
            if (widget.order.customer.email != null) ...[
              SizedBox(height: 8.h),
              _buildInfoRow(
                context,
                Icons.email,
                'Email',
                widget.order.customer.email!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItems(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Items',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            ...widget.order.items.map((item) => _buildOrderItem(context, item)),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, OrderItemEntity item) {
    // Calculate unit price from total amount and total quantity
    final totalQuantity = widget.order.items.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );
    final unitPrice = totalQuantity > 0
        ? widget.order.totalAmount / totalQuantity
        : 0.0;
    final itemTotal = item.quantity * unitPrice;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.subtextColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.itemId,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Qty: ${item.quantity} × \$${unitPrice.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${itemTotal.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Delivery Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoRow(
              context,
              Icons.location_on,
              'Address',
              widget.order.deliveryAddress,
            ),
            if (widget.order.deliveryAgent != null) ...[
              SizedBox(height: 8.h),
              _buildInfoRow(
                context,
                Icons.delivery_dining,
                'Assigned Agent',
                widget.order.deliveryAgent!.phone,
              ),
            ] else ...[
              SizedBox(height: 8.h),
              Row(
                children: [
                  Icon(
                    Icons.delivery_dining,
                    size: 16.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'No agent assigned yet',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.subtextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoRow(
              context,
              Icons.payment,
              'Payment Method',
              widget.order.paymentMethod,
            ),
            // SizedBox(height: 8.h),
            // _buildInfoRow(
            //   context,
            //   Icons.receipt,
            //   'Payment Status',
            //   widget.order.paymentMethod,
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderTracking(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Tracking',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildTrackingStep(
              context: context,
              title: 'Order Placed',
              timestamp: widget.order.createdAt,
              isCompleted: true,
              icon: Icons.shopping_cart,
            ),
            _buildTrackingStep(
              context: context,
              title: 'Order Confirmed',
              timestamp: widget.order.status == OrderStatus.pending
                  ? null
                  : widget.order.createdAt,
              isCompleted: widget.order.status != OrderStatus.pending,
              icon: Icons.check_circle,
            ),
            _buildTrackingStep(
              context: context,
              title: 'Out for Delivery',
              timestamp:
                  widget.order.status == OrderStatus.outForDelivery ||
                      widget.order.status == OrderStatus.delivered
                  ? widget.order.assignedAt
                  : null,
              isCompleted:
                  widget.order.status == OrderStatus.outForDelivery ||
                  widget.order.status == OrderStatus.delivered,
              icon: Icons.local_shipping,
            ),
            _buildTrackingStep(
              context: context,
              title: 'Delivered',
              timestamp: widget.order.status == OrderStatus.delivered
                  ? widget.order.deliveredAt
                  : null,
              isCompleted: widget.order.status == OrderStatus.delivered,
              icon: Icons.done_all,
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingStep({
    required BuildContext context,
    required String title,
    DateTime? timestamp,
    required bool isCompleted,
    required IconData icon,
    bool isLast = false,
  }) {
    print(widget.order.assignedAt);
    return Row(
      children: [
        Column(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: isCompleted
                    ? context.appColors.primaryColor
                    : context.appColors.subtextColor.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: Colors.white, size: 20.sp),
            ),
            if (!isLast)
              Container(
                width: 2.w,
                height: 30.h,
                color: isCompleted
                    ? context.appColors.primaryColor
                    : context.appColors.subtextColor.withValues(alpha: 0.3),
              ),
          ],
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: isCompleted ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              if (timestamp != null) ...[
                SizedBox(height: 2.h),
                Text(
                  DateFormat('MMM dd, yyyy - hh:mm a').format(timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAssignAgentSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Agent Assignment',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'This order is ready for agent assignment. Click the button below to assign an available agent.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showAssignAgentDialog(context, widget.order),
                icon: Icon(Icons.assignment, size: 20.sp),
                label: const Text('Assign Agent'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  backgroundColor: context.appColors.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16.sp, color: context.appColors.subtextColor),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.subtextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper Methods
  Color _getStatusColor(String status) {
    return widget.order.status.color;
    // switch (status.toUpperCase()) {
    //   case 'PENDING':
    //     return Colors.orange;
    //   case 'CONFIRMED':
    //     return Colors.blue;
    //   case 'OUT_FOR_DELIVERY':
    //     return Colors.purple;
    //   case 'DELIVERED':
    //     return Colors.green;
    //   case 'CANCELLED':
    //     return Colors.red;
    //   default:
    //     return Colors.grey;
    // }
  }

  bool _canAssignAgent(OrderEntity order) {
    return order.status == OrderStatus.pending ||
        order.status == OrderStatus.confirmed;
  }

  void _showAssignAgentDialog(BuildContext context, OrderEntity order) async {
    await BottomSheetHelper.showAssignAgentBottomSheet(
      context: context,
      order: order,
      onAssignmentComplete: () => _loadOrdersData(forceRefresh: true),
    );
  }

  void _assignAgent(String agentId) {
    context.read<OrderBloc>().add(
      AssignAgentToOrderEvent(orderId: widget.order.id, agentId: agentId),
    );
  }

  void _loadOrdersData({bool forceRefresh = false}) {
    final orderBloc = context.orderBloc;
    final canRefetch = forceRefresh || orderBloc.orders.isEmpty;
    if (!canRefetch) return;

    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(
      today.year,
      today.month,
      today.day,
      23,
      59,
      59,
      999,
    );

    orderBloc.add(GetOrdersEvent(startDate: startOfDay, endDate: endOfDay));
  }
}
