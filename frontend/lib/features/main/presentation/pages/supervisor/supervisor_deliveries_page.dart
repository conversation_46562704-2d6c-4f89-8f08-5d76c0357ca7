import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';

import '../../../../../core/enums/layout_type.dart';
import '../../../../../core/utils/helpers/bottom_sheet_helper.dart';
import '../../../../order/presentation/bloc/order_bloc.dart';
import '../../../../shared/presentation/widgets/custom_list_grid_view.dart';
import 'supervisor_order_details_page.dart';

class SupervisorDeliveriesPage extends StatefulWidget {
  const SupervisorDeliveriesPage({super.key});

  @override
  State<SupervisorDeliveriesPage> createState() =>
      _SupervisorDeliveriesPageState();
}

class _SupervisorDeliveriesPageState extends State<SupervisorDeliveriesPage> {
  void _loadOrdersData({bool forceRefresh = false}) {
    final orderBloc = context.orderBloc;
    final canRefetch = forceRefresh || orderBloc.orders.isEmpty;
    if (!canRefetch) return;

    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(
      today.year,
      today.month,
      today.day,
      23,
      59,
      59,
      999,
    );

    orderBloc.add(GetOrdersEvent(startDate: startOfDay, endDate: endOfDay));
  }

  @override
  void initState() {
    super.initState();
    _loadOrdersData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Today\'s Deliveries',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () async {
              _loadOrdersData(forceRefresh: true);
            },
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocListener<OrderBloc, OrderState>(
          listener: (context, state) {
            if (state is AssignAgentToOrderSuccess) {
              SnackBarHelper.showSuccessSnackBar(
                context,
                message: state.message,
              );
              // Refresh dashboard data after successful assignment
              _loadOrdersData(forceRefresh: true);
            } else if (state is AssignAgentToOrderFailure) {
              SnackBarHelper.showErrorSnackBar(
                context,
                message: state.appFailure.getErrorMessage(),
              );
            }
          },
          child: BlocBuilder<OrderBloc, OrderState>(
            buildWhen: (previous, current) {
              return current is GetOrdersLoading ||
                  current is GetOrdersSuccess ||
                  current is GetOrdersFailure;
            },
            builder: (context, state) {
              if (state is GetOrdersLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state is GetOrdersFailure) {
                return _buildErrorWidget(
                  context,
                  state.appFailure.getErrorMessage(),
                );
              }

              // if (state is GetSupervisorDashboardSuccess) {
              final orders = context.orderBloc.orders;
              // final deliveredOrders = orders
              //     .where((order) => order.status == OrderStatus.delivered)
              //     .toList();

              // return _buildOrdersList(context);
              return CustomListGridView<OrderEntity>(
                isEmpty: orders.isEmpty,
                isLoading: state is GetOrdersLoading,
                items: orders,
                layoutType: LayoutType.listView,
                padding: EdgeInsets.all(16.w),
                itemBuilder: (context, order) =>
                    _buildOrderCard(context, order),
                emptyDataBuilder: () =>
                    _buildEmptyWidget(context, 'No orders found'),
                onRefresh: () => _loadOrdersData(forceRefresh: true),
              );
              // }

              // return _buildEmptyWidget(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, OrderEntity order) {
    return Card(
      elevation: 2,
      color: context.appColors.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () => _navigateToOrderDetails(context, order),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with Order ID and Status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      'Order #${order.id.substring(order.id.length - 6)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(order.status.name),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      order.status.label,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),

              // Customer Info
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      order.customer.phone,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),

              // Delivery Address
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      order.deliveryAddress,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),

              // Amount and Agent Info
              Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    size: 16.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '\$${order.totalAmount.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  if (order.deliveryAgent != null) ...[
                    Icon(
                      Icons.delivery_dining,
                      size: 16.sp,
                      color: context.appColors.primaryColor,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      order.deliveryAgent!.phone,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ] else ...[
                    Text(
                      'No agent assigned',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
              SizedBox(height: 16.h),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _navigateToOrderDetails(context, order),
                      icon: Icon(Icons.visibility, size: 16.sp),
                      label: const Text('View Details'),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  if (_canAssignAgent(order)) ...[
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _showAssignAgentDialog(context, order),
                        icon: Icon(Icons.assignment, size: 16.sp),
                        label: const Text('Assign Agent'),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          backgroundColor: context.appColors.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ] else ...[
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () =>
                            _navigateToOrderDetails(context, order),
                        icon: Icon(Icons.info, size: 16.sp),
                        label: const Text('Track Order'),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          backgroundColor: context.appColors.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper Methods
  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'CONFIRMED':
        return Colors.blue;
      case 'OUT_FOR_DELIVERY':
        return Colors.purple;
      case 'DELIVERED':
        return Colors.green;
      case 'CANCELLED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  bool _canAssignAgent(OrderEntity order) {
    return order.status == OrderStatus.pending ||
        order.status == OrderStatus.confirmed;
  }

  void _navigateToOrderDetails(BuildContext context, OrderEntity order) {
    context.pushRoute(SupervisorOrderDetailsPage(order: order));
  }

  void _showAssignAgentDialog(BuildContext context, OrderEntity order) async {
    await BottomSheetHelper.showAssignAgentBottomSheet(
      context: context,
      order: order,
      onAssignmentComplete: () => _loadOrdersData(forceRefresh: true),
    );
  }

  Widget _buildEmptyWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delivery_dining,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadOrdersData,
            child: const Text('Load Data'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'Error Loading Deliveries',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadOrdersData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
