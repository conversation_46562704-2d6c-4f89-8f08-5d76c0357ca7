import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/supervisor_dashboard_entity.dart';

import 'package:frontend/features/main/presentation/widgets/dashboard_widgets.dart';
import 'package:frontend/features/main/presentation/widgets/supervisor_drawer.dart';

class SupervisorDashboardPage extends StatefulWidget {
  const SupervisorDashboardPage({super.key});

  @override
  State<SupervisorDashboardPage> createState() =>
      _SupervisorDashboardPageState();
}

class _SupervisorDashboardPageState extends State<SupervisorDashboardPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboard();
  }

  Future<void> _loadDashboard({bool forceRefresh = false}) async {
    final dashboardBloc = context.dashboardBloc;
    final canRefetch =
        forceRefresh || dashboardBloc.supervisorDashboard == null;
    if (!canRefetch) return;

    context.read<DashboardBloc>().add(GetSupervisorDashboardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      drawer: const SupervisorDrawer(),
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu, color: context.appColors.textColor),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          'Supervisor Dashboard',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _loadDashboard(forceRefresh: true),
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await _loadDashboard(forceRefresh: true);
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: _buildDashboardContent(context),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        if (state is GetSupervisorDashboardLoading) {
          return _buildLoadingWidget(context);
        }

        if (state is GetSupervisorDashboardFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        if (state is GetSupervisorDashboardSuccess) {
          return _buildDashboardData(context, state.dashboard);
        }

        return _buildEmptyWidget(context);
      },
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return SizedBox(
      height: 400.h,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return SizedBox(
      height: 400.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
            SizedBox(height: 16.h),
            Text(
              message,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => _loadDashboard(forceRefresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return SizedBox(
      height: 400.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dashboard,
              size: 64.sp,
              color: context.appColors.subtextColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'No dashboard data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => _loadDashboard(forceRefresh: true),
              child: const Text('Load Dashboard'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardData(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRestrictionsNotice(context, dashboard.restrictions),
        SizedBox(height: 16.h),
        _buildTodayStatsOverview(context, dashboard),
        SizedBox(height: 24.h),
        _buildTodayChartsSection(context, dashboard),
        SizedBox(height: 24.h),
        _buildAvailableAgents(context, dashboard.availableAgents),
        SizedBox(height: 24.h),
        _buildTodayOrders(context, dashboard.todayOrders),
      ],
    );
  }

  Widget _buildRestrictionsNotice(
    BuildContext context,
    SupervisorRestrictionsEntity restrictions,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: context.appColors.primaryColor,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'Supervisor View: ${restrictions.dataScope.replaceAll('_', ' ').toLowerCase()} data only',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayStatsOverview(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: DashboardStatCard(
                title: 'Today\'s Orders',
                value: dashboard.todayStats.orders.total.toString(),
                icon: Icons.shopping_cart,
                color: Colors.blue,
                subtitle: 'Total orders',
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: DashboardStatCard(
                title: 'Today\'s Revenue',
                value:
                    '\$${dashboard.todayStats.orders.totalRevenue.toStringAsFixed(0)}',
                icon: Icons.attach_money,
                color: Colors.green,
                subtitle: 'Selling price only',
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: DashboardStatCard(
                title: 'Items Sold',
                value: dashboard.todayStats.sales.itemsSold.toString(),
                icon: Icons.inventory_2,
                color: Colors.orange,
                subtitle: 'Today',
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: DashboardStatCard(
                title: 'Delivered',
                value: dashboard.todayStats.orders.delivered.toString(),
                icon: Icons.check_circle,
                color: Colors.purple,
                subtitle: 'Completed',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTodayChartsSection(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Analytics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: DashboardChartCard(
                title: 'Order Status',
                child: _buildTodayOrderStatusChart(
                  context,
                  dashboard.todayStats.orders,
                ),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: DashboardChartCard(
                title: 'Top Products',
                child: _buildTodayTopProductsChart(
                  context,
                  dashboard.todayTopProducts,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTodayOrderStatusChart(
    BuildContext context,
    SupervisorOrderStatsEntity orderStats,
  ) {
    final data = [
      ChartData('Pending', orderStats.pending, Colors.orange),
      ChartData('Confirmed', orderStats.confirmed, Colors.blue),
      ChartData('Out for Delivery', orderStats.outForDelivery, Colors.purple),
      ChartData('Delivered', orderStats.delivered, Colors.green),
      ChartData('Cancelled', orderStats.cancelled, Colors.red),
    ].where((data) => data.value > 0).toList();

    if (data.isEmpty) {
      return Center(
        child: Text(
          'No orders today',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      );
    }

    return SfCircularChart(
      series: <CircularSeries>[
        DoughnutSeries<ChartData, String>(
          dataSource: data,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
          ),
          innerRadius: '60%',
        ),
      ],
    );
  }

  Widget _buildTodayTopProductsChart(
    BuildContext context,
    List<TodayTopProductEntity> topProducts,
  ) {
    if (topProducts.isEmpty) {
      return Center(
        child: Text(
          'No products sold today',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      );
    }

    final data = topProducts.take(5).map((product) {
      final name =
          product.productDetails.name ??
          product.productDetails.type ??
          'Product ${product.id.itemId}';
      return ChartData(name, product.totalQuantity, Colors.blue);
    }).toList();

    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(),
      series: <CartesianSeries>[
        ColumnSeries<ChartData, String>(
          dataSource: data,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          color: context.appColors.primaryColor,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
        ),
      ],
    );
  }

  Widget _buildAvailableAgents(
    BuildContext context,
    List<AvailableAgentEntity> agents,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Agents',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        if (agents.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Center(
              child: Text(
                'No agents available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: agents.length,
              separatorBuilder: (context, index) =>
                  Divider(color: context.appColors.dividerColor, height: 1),
              itemBuilder: (context, index) {
                final agent = agents[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: context.appColors.primaryColor,
                    child: const Icon(Icons.person, color: Colors.white),
                  ),
                  title: Text(
                    agent.phone,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (agent.email != null)
                        Text(
                          agent.email!,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: context.appColors.subtextColor),
                        ),
                      Row(
                        children: [
                          Icon(Icons.star, size: 16.sp, color: Colors.amber),
                          SizedBox(width: 4.w),
                          Text(
                            agent.agentMetadata.rating.toStringAsFixed(1),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: context.appColors.subtextColor,
                                ),
                          ),
                          if (agent.agentMetadata.vehicle != null) ...[
                            SizedBox(width: 16.w),
                            Icon(
                              Icons.local_shipping,
                              size: 16.sp,
                              color: context.appColors.subtextColor,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              agent.agentMetadata.vehicle!.type,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: context.appColors.subtextColor,
                                  ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                  trailing: ElevatedButton(
                    onPressed: () {
                      // TODO: Implement assign order functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Assign order to ${agent.phone}'),
                          backgroundColor: context.appColors.primaryColor,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.appColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 8.h,
                      ),
                    ),
                    child: const Text('Assign'),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildTodayOrders(
    BuildContext context,
    List<TodayOrderEntity> orders,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Orders',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        if (orders.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Center(
              child: Text(
                'No orders today',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: orders.take(10).length, // Show only first 10 orders
              separatorBuilder: (context, index) =>
                  Divider(color: context.appColors.dividerColor, height: 1),
              itemBuilder: (context, index) {
                final order = orders[index];
                return ListTile(
                  leading: Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        order.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getStatusIcon(order.status),
                      color: _getStatusColor(order.status),
                      size: 20.sp,
                    ),
                  ),
                  title: Text(
                    'Order #${order.id.substring(order.id.length - 6)}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.customer.phone,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                      Text(
                        order.deliveryAddress,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        DateFormat('HH:mm').format(order.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                  ),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${order.totalAmount.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: context.appColors.textColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                            order.status,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          order.status.toUpperCase(),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: _getStatusColor(order.status),
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'out_for_delivery':
      case 'outfordelivery':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'failed':
        return Colors.red.shade700;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.pending;
      case 'confirmed':
        return Icons.check_circle_outline;
      case 'out_for_delivery':
      case 'outfordelivery':
        return Icons.local_shipping;
      case 'delivered':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help_outline;
    }
  }
}

// Helper class for chart data
class ChartData {
  final String category;
  final int value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}
