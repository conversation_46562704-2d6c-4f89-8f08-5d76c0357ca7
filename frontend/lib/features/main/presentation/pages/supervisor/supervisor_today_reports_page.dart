import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart' as gauges;
import 'package:intl/intl.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/supervisor_dashboard_entity.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class SupervisorTodayReportsPage extends StatefulWidget {
  const SupervisorTodayReportsPage({super.key});

  @override
  State<SupervisorTodayReportsPage> createState() =>
      _SupervisorTodayReportsPageState();
}

class _SupervisorTodayReportsPageState
    extends State<SupervisorTodayReportsPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  void _loadDashboardData() {
    context.read<DashboardBloc>().add(GetSupervisorDashboardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Today\'s Reports',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocBuilder<DashboardBloc, DashboardState>(
          builder: (context, state) {
            if (state is GetSupervisorDashboardLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is GetSupervisorDashboardFailure) {
              return _buildErrorWidget(
                context,
                state.appFailure.getErrorMessage(),
              );
            }

            if (state is GetSupervisorDashboardSuccess) {
              return _buildContent(context, state.dashboard);
            }

            return _buildEmptyWidget(context);
          },
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        _loadDashboardData();
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildReportHeader(context),
            SizedBox(height: 24.h),
            _buildSalesOverview(context, dashboard.todayStats),
            SizedBox(height: 24.h),
            _buildOrderStatusChart(context, dashboard.todayStats.orders),
            SizedBox(height: 24.h),
            _buildSalesBreakdownChart(context, dashboard.todayStats.sales),
            SizedBox(height: 24.h),
            _buildPerformanceMetrics(context, dashboard),
            SizedBox(height: 24.h),
            _buildExportSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildReportHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.appColors.primaryColor,
            context.appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Colors.white, size: 32.sp),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Today\'s Performance Report',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      DateFormat('EEEE, MMMM dd, yyyy').format(DateTime.now()),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.white, size: 16.sp),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    'Supervisor view: Selling prices only (cost and profit data restricted)',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesOverview(BuildContext context, TodayStatsEntity stats) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sales Overview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Total Revenue',
                    '\$${stats.sales.totalRevenue.toStringAsFixed(2)}',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Items Sold',
                    stats.sales.itemsSold.toString(),
                    Icons.shopping_cart,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Total Orders',
                    stats.orders.total.toString(),
                    Icons.receipt_long,
                    context.appColors.primaryColor,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Quantity Sold',
                    stats.sales.totalQuantitySold.toString(),
                    Icons.inventory,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatusChart(
    BuildContext context,
    SupervisorOrderStatsEntity orderStats,
  ) {
    final chartData = [
      ChartData('Pending', orderStats.pending, Colors.orange),
      ChartData('Confirmed', orderStats.confirmed, Colors.blue),
      ChartData('Out for Delivery', orderStats.outForDelivery, Colors.purple),
      ChartData('Delivered', orderStats.delivered, Colors.green),
      ChartData('Cancelled', orderStats.cancelled, Colors.red),
      ChartData('Failed', orderStats.failed, Colors.grey),
    ].where((data) => data.value > 0).toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Status Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              height: 250.h,
              child: SfCircularChart(
                legend: const Legend(
                  isVisible: true,
                  position: LegendPosition.bottom,
                ),
                tooltipBehavior: TooltipBehavior(enable: true),
                series: <CircularSeries>[
                  DoughnutSeries<ChartData, String>(
                    dataSource: chartData,
                    xValueMapper: (ChartData data, _) => data.category,
                    yValueMapper: (ChartData data, _) => data.value,
                    pointColorMapper: (ChartData data, _) => data.color,
                    dataLabelSettings: const DataLabelSettings(
                      isVisible: true,
                      labelPosition: ChartDataLabelPosition.outside,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesBreakdownChart(
    BuildContext context,
    SupervisorSalesStatsEntity salesStats,
  ) {
    final chartData = salesStats.salesBreakdown
        .map(
          (breakdown) => ChartData(
            '${breakdown.id.itemType} (${breakdown.id.itemId.substring(breakdown.id.itemId.length - 6)})',
            breakdown.totalQuantitySold,
            _getRandomColor(),
          ),
        )
        .toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sales Breakdown by Product',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            if (chartData.isNotEmpty) ...[
              SizedBox(
                height: 250.h,
                child: SfCartesianChart(
                  primaryXAxis: const CategoryAxis(),
                  tooltipBehavior: TooltipBehavior(enable: true),
                  series: <CartesianSeries>[
                    ColumnSeries<ChartData, String>(
                      dataSource: chartData,
                      xValueMapper: (ChartData data, _) => data.category,
                      yValueMapper: (ChartData data, _) => data.value,
                      pointColorMapper: (ChartData data, _) => data.color,
                      dataLabelSettings: const DataLabelSettings(
                        isVisible: true,
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              Container(
                height: 150.h,
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 48.sp,
                      color: context.appColors.subtextColor,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'No sales data available',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    final deliveryRate = dashboard.todayStats.orders.total > 0
        ? (dashboard.todayStats.orders.delivered /
              dashboard.todayStats.orders.total *
              100)
        : 0.0;

    final averageOrderValue = dashboard.todayStats.orders.total > 0
        ? (dashboard.todayStats.sales.totalRevenue /
              dashboard.todayStats.orders.total)
        : 0.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Metrics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildGaugeCard(
                    context,
                    'Delivery Rate',
                    deliveryRate,
                    '%',
                    Colors.green,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Avg Order Value',
                    '\$${averageOrderValue.toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Available Agents',
                    dashboard.availableAgents.length.toString(),
                    Icons.people,
                    context.appColors.primaryColor,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Pending Orders',
                    dashboard.todayStats.orders.pending.toString(),
                    Icons.pending,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGaugeCard(
    BuildContext context,
    String title,
    double value,
    String unit,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          SizedBox(
            height: 100.h,
            child: gauges.SfRadialGauge(
              axes: <gauges.RadialAxis>[
                gauges.RadialAxis(
                  showLabels: false,
                  showTicks: false,
                  axisLineStyle: gauges.AxisLineStyle(
                    thickness: 0.2,
                    cornerStyle: gauges.CornerStyle.bothCurve,
                    color: color.withValues(alpha: 0.3),
                    thicknessUnit: gauges.GaugeSizeUnit.factor,
                  ),
                  pointers: <gauges.GaugePointer>[
                    gauges.RangePointer(
                      value: value,
                      cornerStyle: gauges.CornerStyle.bothCurve,
                      width: 0.2,
                      sizeUnit: gauges.GaugeSizeUnit.factor,
                      color: color,
                    ),
                  ],
                  annotations: <gauges.GaugeAnnotation>[
                    gauges.GaugeAnnotation(
                      positionFactor: 0.1,
                      angle: 90,
                      widget: Text(
                        '${value.toStringAsFixed(1)}$unit',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: color,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExportSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Export Reports',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Download today\'s performance report in various formats',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _exportReport('PDF'),
                    icon: const Icon(Icons.picture_as_pdf),
                    label: const Text('Export PDF'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _exportReport('Excel'),
                    icon: const Icon(Icons.table_chart),
                    label: const Text('Export Excel'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _exportReport(String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$format export feature coming soon!'),
        backgroundColor: context.appColors.primaryColor,
      ),
    );
  }

  Color _getRandomColor() {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[DateTime.now().millisecondsSinceEpoch % colors.length];
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'Error Loading Reports',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'No Data Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Load Data'),
          ),
        ],
      ),
    );
  }
}

class ChartData {
  final String category;
  final int value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}
