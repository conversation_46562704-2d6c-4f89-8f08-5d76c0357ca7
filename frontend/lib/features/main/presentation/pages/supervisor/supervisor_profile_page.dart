import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

class SupervisorProfilePage extends StatefulWidget {
  const SupervisorProfilePage({super.key});

  @override
  State<SupervisorProfilePage> createState() => _SupervisorProfilePageState();
}

class _SupervisorProfilePageState extends State<SupervisorProfilePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Supervisor Profile',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement edit profile functionality
            },
            icon: Icon(Icons.edit, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Center(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 60.r,
                    backgroundColor: context.appColors.primaryColor,
                    child: Icon(
                      Icons.supervisor_account,
                      size: 60.sp,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  Text(
                    'Supervisor Profile',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'This page will show supervisor profile information and settings.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 32.h),
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: context.appColors.primaryColor.withValues(
                        alpha: 0.1,
                      ),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: context.appColors.primaryColor.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.construction,
                          color: context.appColors.primaryColor,
                          size: 32.sp,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'Coming Soon',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                color: context.appColors.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'Profile management features will be available soon.',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: context.appColors.primaryColor),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 32.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
