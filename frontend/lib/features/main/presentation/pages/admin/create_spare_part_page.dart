import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/spare_part_category.dart';
import 'package:frontend/core/enums/spare_part_status.dart';
import 'package:frontend/core/enums/cylinder_type.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';

class CreateSparePartPage extends StatefulWidget {
  const CreateSparePartPage({super.key});

  @override
  State<CreateSparePartPage> createState() => _CreateSparePartPageState();
}

class _CreateSparePartPageState extends State<CreateSparePartPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _costController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minimumStockController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _imageUrlController = TextEditingController();

  SparePartCategory? _selectedCategory;
  SparePartStatus _selectedStatus = SparePartStatus.available;
  final List<CylinderType> _compatibleCylinderTypes = [];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _quantityController.dispose();
    _minimumStockController.dispose();
    _barcodeController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        title: Text(
          'Create New Spare Part',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _resetForm,
            child: Text(
              'Reset',
              style: TextStyle(
                color: context.appColors.subtextColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: BlocListener<InventoryBloc, InventoryState>(
        listener: (context, state) {
          if (state is CreateSparePartSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Spare part created successfully!',
            );
            Navigator.of(context).pop();
          }
          if (state is CreateSparePartFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to create spare part: ${state.appFailure.getErrorMessage()}',
            );
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Basic Information'),
                SizedBox(height: 16.h),
                _buildNameField(),
                SizedBox(height: 16.h),
                _buildDescriptionField(),
                SizedBox(height: 16.h),
                _buildCategoryAndStatusRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Pricing'),
                SizedBox(height: 16.h),
                _buildPricingRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Stock Management'),
                SizedBox(height: 16.h),
                _buildStockRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Compatibility'),
                SizedBox(height: 16.h),
                _buildCompatibilitySection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Additional Information'),
                SizedBox(height: 16.h),
                _buildImageUrlField(),
                SizedBox(height: 32.h),
                _buildCreateButton(),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: context.appColors.textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'Spare Part Name',
        hintText: 'Enter spare part name',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a spare part name';
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Description',
        hintText: 'Enter spare part description',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a description';
        }
        return null;
      },
    );
  }

  Widget _buildCategoryAndStatusRow() {
    return Row(
      children: [
        Expanded(
          child: _buildDropdownField<SparePartCategory>(
            label: 'Category',
            value: _selectedCategory,
            items: SparePartCategory.values,
            onChanged: (value) => setState(() => _selectedCategory = value),
            itemBuilder: (category) => category.displayName,
            validator: (value) =>
                value == null ? 'Please select a category' : null,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildDropdownField<SparePartStatus>(
            label: 'Status',
            value: _selectedStatus,
            items: SparePartStatus.values,
            onChanged: (value) => setState(() => _selectedStatus = value!),
            itemBuilder: (status) => status.displayName,
          ),
        ),
      ],
    );
  }

  Widget _buildPricingRow() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _priceController,
            label: 'Selling Price',
            hint: '0.00',
            prefix: '\$',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final price = double.tryParse(value);
              if (price == null || price <= 0) return 'Invalid price';
              final cost = double.tryParse(_costController.text);
              if (cost != null && price < cost) return 'Price must be ≥ cost';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _costController,
            label: 'Cost Price',
            hint: '0.00',
            prefix: '\$',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final cost = double.tryParse(value);
              if (cost == null || cost < 0) return 'Invalid cost';
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStockRow() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _quantityController,
            label: 'Initial Quantity',
            hint: '0',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final quantity = int.tryParse(value);
              if (quantity == null || quantity < 0) return 'Invalid quantity';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _minimumStockController,
            label: 'Minimum Stock Level',
            hint: '10',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final minStock = int.tryParse(value);
              if (minStock == null || minStock < 0) return 'Invalid value';
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCompatibilitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Compatible Cylinder Types (${_compatibleCylinderTypes.length})',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _showAddCompatibilityDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Type'),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        if (_compatibleCylinderTypes.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Center(
              child: Text(
                'No compatible cylinder types added yet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ),
          )
        else
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: _compatibleCylinderTypes.map((type) {
              return Chip(
                label: Text(type.displayName),
                deleteIcon: Icon(
                  Icons.close,
                  size: 16.sp,
                  color: context.appColors.errorColor,
                ),
                onDeleted: () => _removeCompatibleType(type),
                backgroundColor: context.appColors.primaryColor.withValues(
                  alpha: 0.1,
                ),
                side: BorderSide(color: context.appColors.primaryColor),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildImageUrlField() {
    return TextFormField(
      controller: _imageUrlController,
      decoration: InputDecoration(
        labelText: 'Image URL (Optional)',
        hintText: 'https://example.com/image.jpg',
        prefixIcon: Icon(Icons.image, color: context.appColors.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
    );
  }

  Widget _buildDropdownField<T>({
    required String label,
    required T? value,
    required List<T> items,
    required ValueChanged<T?> onChanged,
    required String Function(T) itemBuilder,
    String? Function(T?)? validator,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      items: items.map((item) {
        return DropdownMenuItem<T>(value: item, child: Text(itemBuilder(item)));
      }).toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? prefix,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefix,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildCreateButton() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        final isLoading = state is CreateSparePartLoading;

        return SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : _createSparePart,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Create Spare Part',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  void _showAddCompatibilityDialog() {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        CylinderType? selectedType;

        return StatefulBuilder(
          builder: (context, setState) {
            final availableTypes = CylinderType.values
                .where((type) => !_compatibleCylinderTypes.contains(type))
                .toList();

            return AlertDialog(
              title: const Text('Add Compatible Cylinder Type'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (availableTypes.isEmpty)
                    const Text('All cylinder types have been added')
                  else
                    DropdownButtonFormField<CylinderType>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        labelText: 'Select Cylinder Type',
                      ),
                      items: availableTypes.map((type) {
                        return DropdownMenuItem<CylinderType>(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (value) =>
                          setState(() => selectedType = value),
                    ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: const Text('Cancel'),
                ),
                if (availableTypes.isNotEmpty)
                  ElevatedButton(
                    onPressed: () {
                      if (selectedType != null) {
                        _addCompatibleType(selectedType!);
                        Navigator.of(dialogContext).pop();
                      }
                    },
                    child: const Text('Add'),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _addCompatibleType(CylinderType type) {
    setState(() {
      _compatibleCylinderTypes.add(type);
    });
  }

  void _removeCompatibleType(CylinderType type) {
    setState(() {
      _compatibleCylinderTypes.remove(type);
    });
  }

  void _createSparePart() {
    if (_formKey.currentState!.validate()) {
      final event = CreateSparePartEvent(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory!,
        price: double.parse(_priceController.text),
        cost: double.parse(_costController.text),
        barcode: _barcodeController.text.trim(),
        minimumStockLevel: int.parse(_minimumStockController.text),
        compatibleCylinderTypes: _compatibleCylinderTypes,
      );

      context.inventoryBloc.add(event);
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _costController.clear();
    _quantityController.clear();
    _minimumStockController.clear();
    _barcodeController.clear();
    _imageUrlController.clear();
    setState(() {
      _selectedCategory = null;
      _selectedStatus = SparePartStatus.available;
      _compatibleCylinderTypes.clear();
    });

    // unfocus
    FocusScope.of(context).unfocus();
  }
}
