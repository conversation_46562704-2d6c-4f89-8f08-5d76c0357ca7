import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';

class AdminSettingsPage extends StatefulWidget {
  const AdminSettingsPage({super.key});

  @override
  State<AdminSettingsPage> createState() => _AdminSettingsPageState();
}

class _AdminSettingsPageState extends State<AdminSettingsPage> {
  bool _notificationsEnabled = true;
  bool _maintenanceMode = false;
  bool _autoBackup = true;
  double _deliveryRadius = 10.0;
  double _commissionRate = 15.0;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        UserEntity? user;
        if (state is Authenticated && state.user != null) {
          user = state.user!;
        }

        return Scaffold(
          backgroundColor: context.appColors.backgroundColor,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(context, user),
                  SizedBox(height: 24.h),
                  _buildSystemSettings(context),
                  SizedBox(height: 24.h),
                  _buildBusinessSettings(context),
                  SizedBox(height: 24.h),
                  _buildNotificationSettings(context),
                  SizedBox(height: 24.h),
                  _buildSecuritySettings(context),
                  SizedBox(height: 24.h),
                  _buildDataManagement(context),
                  SizedBox(height: 24.h),
                  _buildDangerZone(context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, UserEntity? user) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.indigo,
            Colors.indigo.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'System Settings',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Configure system preferences and policies',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.settings,
              color: Colors.white,
              size: 32.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemSettings(BuildContext context) {
    return _buildSettingsSection(
      context,
      'System Settings',
      Icons.computer,
      [
        _buildSwitchTile(
          context,
          'Maintenance Mode',
          'Temporarily disable the app for maintenance',
          _maintenanceMode,
          (value) {
            setState(() {
              _maintenanceMode = value;
            });
          },
          Colors.orange,
        ),
        _buildSwitchTile(
          context,
          'Auto Backup',
          'Automatically backup data daily',
          _autoBackup,
          (value) {
            setState(() {
              _autoBackup = value;
            });
          },
          Colors.green,
        ),
        _buildActionTile(
          context,
          'System Logs',
          'View and download system logs',
          Icons.description,
          Colors.blue,
          () {
            // TODO: Navigate to system logs
          },
        ),
        _buildActionTile(
          context,
          'Database Status',
          'Check database health and performance',
          Icons.storage,
          Colors.purple,
          () {
            // TODO: Show database status
          },
        ),
      ],
    );
  }

  Widget _buildBusinessSettings(BuildContext context) {
    return _buildSettingsSection(
      context,
      'Business Settings',
      Icons.business,
      [
        _buildSliderTile(
          context,
          'Delivery Radius',
          'Maximum delivery distance in kilometers',
          _deliveryRadius,
          1.0,
          50.0,
          (value) {
            setState(() {
              _deliveryRadius = value;
            });
          },
          '${_deliveryRadius.toInt()} km',
          Colors.blue,
        ),
        _buildSliderTile(
          context,
          'Commission Rate',
          'Platform commission percentage',
          _commissionRate,
          5.0,
          30.0,
          (value) {
            setState(() {
              _commissionRate = value;
            });
          },
          '${_commissionRate.toInt()}%',
          Colors.green,
        ),
        _buildActionTile(
          context,
          'Pricing Rules',
          'Configure delivery pricing and fees',
          Icons.attach_money,
          Colors.orange,
          () {
            // TODO: Navigate to pricing settings
          },
        ),
        _buildActionTile(
          context,
          'Service Areas',
          'Manage delivery service areas',
          Icons.map,
          Colors.red,
          () {
            // TODO: Navigate to service areas
          },
        ),
      ],
    );
  }

  Widget _buildNotificationSettings(BuildContext context) {
    return _buildSettingsSection(
      context,
      'Notification Settings',
      Icons.notifications,
      [
        _buildSwitchTile(
          context,
          'Push Notifications',
          'Enable system-wide push notifications',
          _notificationsEnabled,
          (value) {
            setState(() {
              _notificationsEnabled = value;
            });
          },
          Colors.blue,
        ),
        _buildActionTile(
          context,
          'Email Templates',
          'Customize email notification templates',
          Icons.email,
          Colors.purple,
          () {
            // TODO: Navigate to email templates
          },
        ),
        _buildActionTile(
          context,
          'SMS Settings',
          'Configure SMS notifications and providers',
          Icons.sms,
          Colors.green,
          () {
            // TODO: Navigate to SMS settings
          },
        ),
      ],
    );
  }

  Widget _buildSecuritySettings(BuildContext context) {
    return _buildSettingsSection(
      context,
      'Security & Privacy',
      Icons.security,
      [
        _buildActionTile(
          context,
          'User Permissions',
          'Manage role-based access control',
          Icons.admin_panel_settings,
          Colors.red,
          () {
            // TODO: Navigate to permissions
          },
        ),
        _buildActionTile(
          context,
          'API Keys',
          'Manage third-party API keys and tokens',
          Icons.key,
          Colors.orange,
          () {
            // TODO: Navigate to API keys
          },
        ),
        _buildActionTile(
          context,
          'Audit Logs',
          'View security and access logs',
          Icons.history,
          Colors.blue,
          () {
            // TODO: Navigate to audit logs
          },
        ),
        _buildActionTile(
          context,
          'Privacy Policy',
          'Update privacy policy and terms',
          Icons.policy,
          Colors.purple,
          () {
            // TODO: Navigate to privacy policy
          },
        ),
      ],
    );
  }

  Widget _buildDataManagement(BuildContext context) {
    return _buildSettingsSection(
      context,
      'Data Management',
      Icons.folder,
      [
        _buildActionTile(
          context,
          'Export Data',
          'Export user and order data',
          Icons.download,
          Colors.green,
          () {
            _showExportDialog(context);
          },
        ),
        _buildActionTile(
          context,
          'Import Data',
          'Import data from external sources',
          Icons.upload,
          Colors.blue,
          () {
            _showImportDialog(context);
          },
        ),
        _buildActionTile(
          context,
          'Data Cleanup',
          'Clean up old and unused data',
          Icons.cleaning_services,
          Colors.orange,
          () {
            _showCleanupDialog(context);
          },
        ),
      ],
    );
  }

  Widget _buildDangerZone(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.red,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Danger Zone',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildDangerAction(
            context,
            'Reset System',
            'Reset all settings to default values',
            () {
              _showResetConfirmation(context);
            },
          ),
          SizedBox(height: 12.h),
          _buildDangerAction(
            context,
            'Clear All Data',
            'Permanently delete all user and order data',
            () {
              _showClearDataConfirmation(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: context.appColors.primaryColor,
              size: 20.sp,
            ),
            SizedBox(width: 8.w),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Container(
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: context.appColors.dividerColor,
              width: 1,
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              value ? Icons.check_circle : Icons.radio_button_unchecked,
              color: color,
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: color,
          ),
        ],
      ),
    );
  }

  Widget _buildSliderTile(
    BuildContext context,
    String title,
    String subtitle,
    double value,
    double min,
    double max,
    Function(double) onChanged,
    String displayValue,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.tune,
                  color: color,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: context.appColors.textColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          displayValue,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: color,
              thumbColor: color,
              overlayColor: color.withValues(alpha: 0.2),
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: context.appColors.subtextColor,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDangerAction(
    BuildContext context,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: Colors.red.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.red,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Choose data to export:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement data export
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data export started')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text('Select file to import:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement data import
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data import started')),
              );
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _showCleanupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data Cleanup'),
        content: const Text('This will remove old and unused data. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement data cleanup
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data cleanup completed')),
              );
            },
            child: const Text('Cleanup'),
          ),
        ],
      ),
    );
  }

  void _showResetConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset System'),
        content: const Text(
          'This will reset all settings to default values. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement system reset
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('System reset completed')),
              );
            },
            child: const Text('Reset', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showClearDataConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete ALL user and order data. This action cannot be undone!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement data clearing
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('All data cleared')),
              );
            },
            child: const Text('Clear All', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
