import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:intl/intl.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/admin_dashboard_entity.dart';

import 'package:frontend/features/main/presentation/widgets/dashboard_widgets.dart';
import 'package:frontend/features/main/presentation/widgets/admin_drawer.dart';

class AdminDashboardPage extends StatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboard();
  }

  void _loadDashboard({bool forceRefresh = false}) {
    final dashboardBloc = context.dashboardBloc;
    final canRefetch = forceRefresh || dashboardBloc.adminDashboard == null;
    if (!canRefetch) return;

    context.read<DashboardBloc>().add(GetAdminDashboardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      drawer: const AdminDrawer(),
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu, color: context.appColors.textColor),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          'Admin Dashboard',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _loadDashboard(forceRefresh: true),
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async => _loadDashboard(forceRefresh: true),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [_buildDashboardContent(context)],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        if (state is GetAdminDashboardLoading) {
          return _buildLoadingWidget(context);
        }

        if (state is GetAdminDashboardFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        if (state is GetAdminDashboardSuccess) {
          return _buildDashboardData(context, state.dashboard);
        }

        return _buildEmptyWidget(context);
      },
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return SizedBox(
      height: 400.h,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Container(
      height: 400.h,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48.sp,
            color: context.appColors.errorColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'Failed to load dashboard',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboard,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Container(
      height: 400.h,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard_outlined,
            size: 48.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'No dashboard data available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Pull to refresh or check back later',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardData(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatsOverview(context, dashboard),
        SizedBox(height: 24.h),
        _buildChartsSection(context, dashboard),
        SizedBox(height: 24.h),
        _buildRecentActivity(context, dashboard),
      ],
    );
  }

  Widget _buildStatsOverview(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'System Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: DashboardStatCard(
                title: 'Total Orders',
                value: dashboard.orderStats.total.toString(),
                icon: Icons.shopping_cart,
                color: Colors.blue,
                subtitle: 'All time',
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: DashboardStatCard(
                title: 'Revenue',
                value:
                    '\$${dashboard.revenueStats.totalRevenue.toStringAsFixed(0)}',
                icon: Icons.attach_money,
                color: Colors.green,
                subtitle: 'Total revenue',
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: DashboardStatCard(
                title: 'Active Users',
                value: dashboard.userStats.total.toString(),
                icon: Icons.people,
                color: Colors.purple,
                subtitle: 'Registered',
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: DashboardStatCard(
                title: 'Inventory Items',
                value: _getTotalInventoryCount(
                  dashboard.inventoryStats,
                ).toString(),
                icon: Icons.inventory,
                color: Colors.orange,
                subtitle: 'In stock',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartsSection(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Analytics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: DashboardChartCard(
                title: 'Order Status Distribution',
                child: _buildOrderStatusChart(context, dashboard.orderStats),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: DashboardChartCard(
                title: 'Revenue Trend',
                child: _buildRevenueChart(context, dashboard.revenueStats),
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: DashboardChartCard(
                title: 'Top Products',
                child: _buildTopProductsChart(context, dashboard.topProducts),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: DashboardChartCard(
                title: 'Order Completion Rate',
                child: _buildCompletionGauge(context, dashboard.orderStats),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivity(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Orders',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to full orders page
              },
              child: Text(
                'View All',
                style: TextStyle(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Container(
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: dashboard.recentOrders.length.clamp(0, 5),
            separatorBuilder: (context, index) =>
                Divider(color: context.appColors.dividerColor, height: 1),
            itemBuilder: (context, index) {
              final order = dashboard.recentOrders[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: context.appColors.primaryColor.withValues(
                    alpha: 0.1,
                  ),
                  child: Icon(
                    Icons.receipt,
                    color: context.appColors.primaryColor,
                    size: 20.sp,
                  ),
                ),
                title: Text(
                  'Order #${order.id}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  order.customer.phone,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${order.totalAmount.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _getOrderStatusText(order.status),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getOrderStatusColor(order.status),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOrderStatusChart(
    BuildContext context,
    OrderStatsEntity orderStats,
  ) {
    final data = [
      ChartData('Pending', orderStats.pending.toDouble(), Colors.orange),
      ChartData('Confirmed', orderStats.confirmed.toDouble(), Colors.blue),
      ChartData(
        'Out for Delivery',
        orderStats.outForDelivery.toDouble(),
        Colors.indigo,
      ),
      ChartData('Delivered', orderStats.delivered.toDouble(), Colors.green),
      ChartData('Cancelled', orderStats.cancelled.toDouble(), Colors.red),
    ];

    return SfCircularChart(
      legend: Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: TextStyle(
          color: context.appColors.textColor,
          fontSize: 10.sp,
        ),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CircularSeries>[
        DoughnutSeries<ChartData, String>(
          dataSource: data,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,
          innerRadius: '60%',
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
          ),
        ),
      ],
    );
  }

  Widget _buildRevenueChart(
    BuildContext context,
    RevenueStatsEntity revenueStats,
  ) {
    // Mock data for revenue trend - in real app, this would come from the entity
    final data = [
      ChartData('Jan', 15000, Colors.green),
      ChartData('Feb', 18000, Colors.green),
      ChartData('Mar', 22000, Colors.green),
      ChartData('Apr', 19000, Colors.green),
      ChartData('May', 25000, Colors.green),
      ChartData('Jun', revenueStats.totalRevenue, Colors.green),
    ];

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        labelStyle: TextStyle(
          color: context.appColors.subtextColor,
          fontSize: 10.sp,
        ),
      ),
      primaryYAxis: NumericAxis(
        labelStyle: TextStyle(
          color: context.appColors.subtextColor,
          fontSize: 10.sp,
        ),
        numberFormat: NumberFormat.compact(),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries>[
        AreaSeries<ChartData, String>(
          dataSource: data,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          color: context.appColors.primaryColor.withValues(alpha: 0.3),
          borderColor: context.appColors.primaryColor,
        ),
      ],
    );
  }

  Widget _buildTopProductsChart(
    BuildContext context,
    List<TopProductEntity> topProducts,
  ) {
    final data = topProducts
        .take(5)
        .map(
          (product) => ChartData(
            '${product.productDetails.type} - ${product.productDetails.material}',
            product.totalQuantity.toDouble(),
            _getRandomColor(),
          ),
        )
        .toList();

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        labelStyle: TextStyle(
          color: context.appColors.subtextColor,
          fontSize: 10.sp,
        ),
        labelRotation: -45,
      ),
      primaryYAxis: NumericAxis(
        labelStyle: TextStyle(
          color: context.appColors.subtextColor,
          fontSize: 10.sp,
        ),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries>[
        ColumnSeries<ChartData, String>(
          dataSource: data,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
        ),
      ],
    );
  }

  String _getOrderStatusText(dynamic status) {
    // Handle different status types
    return status.toString().split('.').last.replaceAll('_', ' ').toUpperCase();
  }

  Color _getOrderStatusColor(dynamic status) {
    final statusStr = status.toString().toLowerCase();
    if (statusStr.contains('pending')) return Colors.orange;
    if (statusStr.contains('confirmed')) return Colors.blue;
    if (statusStr.contains('transit')) return Colors.indigo;
    if (statusStr.contains('delivered')) return Colors.green;
    if (statusStr.contains('cancelled')) return Colors.red;
    return context.appColors.subtextColor;
  }

  Color _getRandomColor() {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
    ];
    return colors[DateTime.now().millisecond % colors.length];
  }

  int _getTotalInventoryCount(InventoryStatsEntity inventoryStats) {
    // Calculate total inventory count from all categories
    int total = 0;

    // Add cylinders count
    for (final status in inventoryStats.cylinders.byStatus) {
      total += status.count;
    }

    // Add spare parts count
    for (final status in inventoryStats.spareParts.byStatus) {
      total += status.count;
    }

    // Add packages count - handle dynamic type
    for (final status in inventoryStats.packages.byStatus) {
      if (status is Map<String, dynamic>) {
        total += (status['count'] as num?)?.toInt() ?? 0;
      } else if (status is ByStatusEntity) {
        total += status.count;
      }
    }

    return total;
  }

  Widget _buildCompletionGauge(
    BuildContext context,
    OrderStatsEntity orderStats,
  ) {
    final completionRate = orderStats.total > 0
        ? (orderStats.delivered / orderStats.total) * 100
        : 0.0;

    return SfRadialGauge(
      axes: <RadialAxis>[
        RadialAxis(
          ranges: <GaugeRange>[
            GaugeRange(
              startValue: 0,
              endValue: 30,
              color: Colors.red,
              startWidth: 10,
              endWidth: 10,
            ),
            GaugeRange(
              startValue: 30,
              endValue: 70,
              color: Colors.orange,
              startWidth: 10,
              endWidth: 10,
            ),
            GaugeRange(
              startValue: 70,
              endValue: 100,
              color: Colors.green,
              startWidth: 10,
              endWidth: 10,
            ),
          ],
          pointers: <GaugePointer>[
            NeedlePointer(
              value: completionRate,
              needleColor: context.appColors.primaryColor,
              knobStyle: KnobStyle(color: context.appColors.primaryColor),
            ),
          ],
          annotations: <GaugeAnnotation>[
            GaugeAnnotation(
              widget: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${completionRate.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Completed',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
              angle: 90,
              positionFactor: 0.5,
            ),
          ],
          axisLabelStyle: GaugeTextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
          majorTickStyle: MajorTickStyle(color: context.appColors.dividerColor),
          minorTickStyle: MinorTickStyle(color: context.appColors.dividerColor),
        ),
      ],
    );
  }
}

// Chart data model
class ChartData {
  final String category;
  final double value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}
