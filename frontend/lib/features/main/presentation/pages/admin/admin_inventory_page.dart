import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/cylinder_type.dart';
import 'package:frontend/core/enums/cylinder_material.dart';
import 'package:frontend/core/enums/cylinder_status.dart';
import 'package:frontend/core/enums/package_status.dart';
import 'package:frontend/core/enums/spare_part_category.dart';
import 'package:frontend/core/enums/spare_part_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/inventory/domain/entities/cylinder_entity.dart';
import 'package:frontend/features/inventory/domain/entities/package_entity.dart';
import 'package:frontend/features/inventory/domain/entities/spare_part_entity.dart';
import 'package:frontend/features/main/presentation/pages/admin/create_cylinder_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/create_package_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/create_spare_part_page.dart';

import '../../../../../core/enums/layout_type.dart';
import '../../../../shared/presentation/widgets/custom_list_grid_view.dart';

class AdminInventoryPage extends StatefulWidget {
  const AdminInventoryPage({super.key});

  @override
  State<AdminInventoryPage> createState() => _AdminInventoryPageState();
}

class _AdminInventoryPageState extends State<AdminInventoryPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // Filter states
  CylinderType? _selectedCylinderType;
  CylinderMaterial? _selectedCylinderMaterial;
  CylinderStatus? _selectedCylinderStatus;
  PackageStatus? _selectedPackageStatus;
  SparePartCategory? _selectedSparePartCategory;
  SparePartStatus? _selectedSparePartStatus;
  bool _showLowStockOnly = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadInventoryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadInventoryData({bool forceRefresh = false}) {
    final inventoryBloc = context.inventoryBloc;
    final canRefetchCylinders = forceRefresh || inventoryBloc.cylinders.isEmpty;
    final canRefetchPackages = forceRefresh || inventoryBloc.packages.isEmpty;
    final canRefetchSpareParts =
        forceRefresh || inventoryBloc.spareParts.isEmpty;

    // Load all inventory data for the tabs
    if (canRefetchCylinders) {
      context.read<InventoryBloc>().add(const GetCylindersEvent());
    }
    if (canRefetchPackages) {
      context.read<InventoryBloc>().add(const GetPackagesEvent());
    }
    if (canRefetchSpareParts) {
      context.read<InventoryBloc>().add(const GetSparePartsEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            _buildSearchAndFilters(context),
            _buildTabBar(context),
            Expanded(child: _buildTabBarView(context)),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Inventory Management',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Manage cylinders, packages, and spare parts',
                  // 'Manage Your Inventory',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => _loadInventoryData(forceRefresh: true),
                icon: Icon(
                  Icons.refresh,
                  color: context.appColors.primaryColor,
                  size: 24.sp,
                ),
              ),
              IconButton(
                onPressed: () => _showBulkActionsDialog(context),
                icon: Icon(
                  Icons.more_vert,
                  color: context.appColors.subtextColor,
                  size: 24.sp,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search inventory...',
              prefixIcon: Icon(
                Icons.search,
                color: context.appColors.subtextColor,
              ),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _applyFilters();
                      },
                      icon: Icon(
                        Icons.clear,
                        color: context.appColors.subtextColor,
                      ),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: context.appColors.dividerColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: context.appColors.dividerColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: context.appColors.primaryColor),
              ),
            ),
            onChanged: (value) => _applyFilters(),
          ),
          SizedBox(height: 12.h),
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(context, 'Low Stock', _showLowStockOnly, () {
                  setState(() {
                    _showLowStockOnly = !_showLowStockOnly;
                  });
                  _applyFilters();
                }),
                SizedBox(width: 8.w),
                _buildTabSpecificFilters(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected
              ? context.appColors.primaryColor
              : context.appColors.surfaceColor,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected
                ? context.appColors.primaryColor
                : context.appColors.dividerColor,
          ),
        ),
        child: Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: isSelected ? Colors.white : context.appColors.textColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildTabSpecificFilters(BuildContext context) {
    switch (_tabController.index) {
      case 0: // Cylinders
        return Row(
          children: [
            if (_selectedCylinderType != null) ...[
              _buildFilterChip(context, _selectedCylinderType!.name, true, () {
                setState(() {
                  _selectedCylinderType = null;
                });
                _applyFilters();
              }),
              SizedBox(width: 8.w),
            ],
            if (_selectedCylinderMaterial != null) ...[
              _buildFilterChip(
                context,
                _selectedCylinderMaterial!.name,
                true,
                () {
                  setState(() {
                    _selectedCylinderMaterial = null;
                  });
                  _applyFilters();
                },
              ),
              SizedBox(width: 8.w),
            ],
            if (_selectedCylinderStatus != null) ...[
              _buildFilterChip(
                context,
                _selectedCylinderStatus!.name,
                true,
                () {
                  setState(() {
                    _selectedCylinderStatus = null;
                  });
                  _applyFilters();
                },
              ),
              SizedBox(width: 8.w),
            ],
            IconButton(
              onPressed: () => _showCylinderFiltersDialog(context),
              icon: Icon(
                Icons.filter_list,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
            ),
          ],
        );
      case 1: // Packages
        return Row(
          children: [
            if (_selectedPackageStatus != null) ...[
              _buildFilterChip(context, _selectedPackageStatus!.name, true, () {
                setState(() {
                  _selectedPackageStatus = null;
                });
                _applyFilters();
              }),
              SizedBox(width: 8.w),
            ],
            IconButton(
              onPressed: () => _showPackageFiltersDialog(context),
              icon: Icon(
                Icons.filter_list,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
            ),
          ],
        );
      case 2: // Spare Parts
        return Row(
          children: [
            if (_selectedSparePartCategory != null) ...[
              _buildFilterChip(
                context,
                _selectedSparePartCategory!.name,
                true,
                () {
                  setState(() {
                    _selectedSparePartCategory = null;
                  });
                  _applyFilters();
                },
              ),
              SizedBox(width: 8.w),
            ],
            if (_selectedSparePartStatus != null) ...[
              _buildFilterChip(
                context,
                _selectedSparePartStatus!.name,
                true,
                () {
                  setState(() {
                    _selectedSparePartStatus = null;
                  });
                  _applyFilters();
                },
              ),
              SizedBox(width: 8.w),
            ],
            IconButton(
              onPressed: () => _showSparePartFiltersDialog(context),
              icon: Icon(
                Icons.filter_list,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(6.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
        onTap: (index) {
          setState(() {
            // Reset filters when switching tabs
            _clearFilters();
          });
        },
        tabs: const [
          Tab(text: 'Cylinders'),
          Tab(text: 'Packages'),
          Tab(text: 'Spare Parts'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildCylindersTab(context),
        _buildPackagesTab(context),
        _buildSparePartsTab(context),
      ],
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    switch (_tabController.index) {
      case 0:
        return FloatingActionButton.extended(
          onPressed: () => _showAddCylinderDialog(context),
          backgroundColor: context.appColors.primaryColor,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text(
            'Add Cylinder',
            style: TextStyle(color: Colors.white),
          ),
        );
      case 1:
        return FloatingActionButton.extended(
          onPressed: () => _showAddPackageDialog(context),
          backgroundColor: context.appColors.primaryColor,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text(
            'Add Package',
            style: TextStyle(color: Colors.white),
          ),
        );
      case 2:
        return FloatingActionButton.extended(
          onPressed: () => _showAddSparePartDialog(context),
          backgroundColor: context.appColors.primaryColor,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text(
            'Add Spare Part',
            style: TextStyle(color: Colors.white),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  // Tab content builders
  Widget _buildCylindersTab(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetCylindersLoading ||
            current is GetCylindersSuccess ||
            current is GetCylindersFailure;
      },
      builder: (context, state) {
        if (state is GetCylindersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetCylindersFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        // if (state is GetCylindersSuccess) {
        final cylinders = context.inventoryBloc.cylinders;
        // final filteredCylinders = _filterCylinders(state.cylinders);
        final filteredCylinders = _filterCylinders(cylinders);

        if (filteredCylinders.isEmpty) {
          return _buildEmptyWidget(context, 'No cylinders found');
        }

        // return ListView.builder(
        //   padding: EdgeInsets.all(16.w),
        //   itemCount: filteredCylinders.length,
        //   itemBuilder: (context, index) {
        //     final cylinder = filteredCylinders[index];
        //     return _buildCylinderCard(context, cylinder);
        //   },
        // );
        return CustomListGridView<CylinderEntity>(
          isEmpty: filteredCylinders.isEmpty,
          isLoading: false,
          showFooter: false,
          items: filteredCylinders,
          itemCount: filteredCylinders.length,
          layoutType: LayoutType.listView,
          // padding: EdgeInsets.all(16.w),
          itemBuilder: (context, cylinder) =>
              _buildCylinderCard(context, cylinder),
          emptyDataBuilder: () =>
              _buildEmptyWidget(context, 'No cylinders found'),
          onRefresh: () {
            _loadInventoryData(forceRefresh: true);
          },
        );
        // }

        // return _buildEmptyWidget(context, 'No cylinders available');
      },
    );
  }

  Widget _buildPackagesTab(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetPackagesLoading ||
            current is GetPackagesSuccess ||
            current is GetPackagesFailure;
      },
      builder: (context, state) {
        if (state is GetPackagesLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetPackagesFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        // if (state is GetPackagesSuccess) {
        final packages = context.inventoryBloc.packages;
        // final filteredPackages = _filterPackages(state.packages);
        final filteredPackages = _filterPackages(packages);

        if (filteredPackages.isEmpty) {
          return _buildEmptyWidget(context, 'No packages found');
        }

        return ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: filteredPackages.length,
          itemBuilder: (context, index) {
            final package = filteredPackages[index];
            return _buildPackageCard(context, package);
          },
        );
        // }

        // return _buildEmptyWidget(context, 'No packages available');
      },
    );
  }

  Widget _buildSparePartsTab(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetSparePartsLoading ||
            current is GetSparePartsSuccess ||
            current is GetSparePartsFailure;
      },
      builder: (context, state) {
        if (state is GetSparePartsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetSparePartsFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        // if (state is GetSparePartsSuccess) {
        final spareParts = context.inventoryBloc.spareParts;
        // final filteredSpareParts = _filterSpareParts(state.spareParts);
        final filteredSpareParts = _filterSpareParts(spareParts);

        if (filteredSpareParts.isEmpty) {
          return _buildEmptyWidget(context, 'No spare parts found');
        }

        return ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: filteredSpareParts.length,
          itemBuilder: (context, index) {
            final sparePart = filteredSpareParts[index];
            return _buildSparePartCard(context, sparePart);
          },
        );
        // }

        // return _buildEmptyWidget(context, 'No spare parts available');
      },
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48.sp,
            color: context.appColors.errorColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadInventoryData,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 48.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Try adjusting your filters or add new items',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  // Filter methods
  List<CylinderEntity> _filterCylinders(List<CylinderEntity> cylinders) {
    return cylinders.where((cylinder) {
      // Search filter
      if (_searchController.text.isNotEmpty) {
        final searchTerm = _searchController.text.toLowerCase();
        if (!cylinder.type.name.toLowerCase().contains(searchTerm) &&
            !cylinder.material.name.toLowerCase().contains(searchTerm) &&
            !cylinder.description.toLowerCase().contains(searchTerm)) {
          return false;
        }
      }

      // Type filter
      if (_selectedCylinderType != null &&
          cylinder.type != _selectedCylinderType) {
        return false;
      }

      // Material filter
      if (_selectedCylinderMaterial != null &&
          cylinder.material != _selectedCylinderMaterial) {
        return false;
      }

      // Status filter
      if (_selectedCylinderStatus != null &&
          cylinder.status != _selectedCylinderStatus) {
        return false;
      }

      // Low stock filter
      if (_showLowStockOnly && !cylinder.isLowStock) {
        return false;
      }

      return true;
    }).toList();
  }

  List<PackageEntity> _filterPackages(List<PackageEntity> packages) {
    return packages.where((package) {
      // Search filter
      if (_searchController.text.isNotEmpty) {
        final searchTerm = _searchController.text.toLowerCase();
        if (!package.name.toLowerCase().contains(searchTerm) &&
            !package.description.toLowerCase().contains(searchTerm)) {
          return false;
        }
      }

      // Status filter
      if (_selectedPackageStatus != null &&
          package.status != _selectedPackageStatus) {
        return false;
      }

      // Low stock filter
      if (_showLowStockOnly && !package.isLowStock) {
        return false;
      }

      return true;
    }).toList();
  }

  List<SparePartEntity> _filterSpareParts(List<SparePartEntity> spareParts) {
    return spareParts.where((sparePart) {
      // Search filter
      if (_searchController.text.isNotEmpty) {
        final searchTerm = _searchController.text.toLowerCase();
        if (!sparePart.name.toLowerCase().contains(searchTerm) &&
            !sparePart.description.toLowerCase().contains(searchTerm)) {
          return false;
        }
      }

      // Category filter
      if (_selectedSparePartCategory != null &&
          sparePart.category != _selectedSparePartCategory) {
        return false;
      }

      // Status filter
      if (_selectedSparePartStatus != null &&
          sparePart.status != _selectedSparePartStatus) {
        return false;
      }

      // Low stock filter
      if (_showLowStockOnly && !sparePart.isLowStock) {
        return false;
      }

      return true;
    }).toList();
  }

  void _applyFilters() {
    setState(() {
      // Trigger rebuild with current filters
    });
  }

  void _clearFilters() {
    _selectedCylinderType = null;
    _selectedCylinderMaterial = null;
    _selectedCylinderStatus = null;
    _selectedPackageStatus = null;
    _selectedSparePartCategory = null;
    _selectedSparePartStatus = null;
    _showLowStockOnly = false;
    _searchController.clear();
  }

  // Card builders
  Widget _buildCylinderCard(BuildContext context, CylinderEntity cylinder) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      color: context.appColors.surfaceColor,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${cylinder.type.name} - ${cylinder.material.name}',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: context.appColors.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        cylinder.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                _buildStockStatusChip(
                  context,
                  // cylinder.availableQuantity,
                  // cylinder.isLowStock,
                  // cylinder.isOutOfStock,
                  cylinder.status.name,
                  cylinder.status.color,
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Price',
                    '\$${cylinder.price.toStringAsFixed(2)}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Stock',
                    '${cylinder.availableQuantity}/${cylinder.quantity}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Reserved',
                    '${cylinder.reserved}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(context, 'Sold', '${cylinder.sold}'),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () =>
                      _showRestockDialog(context, cylinder.id, 'cylinder'),
                  icon: Icon(Icons.add_box, size: 16.sp),
                  label: const Text('Restock'),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showEditCylinderDialog(context, cylinder),
                  icon: Icon(Icons.edit, size: 16.sp),
                  label: const Text('Edit'),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () =>
                      _showDeleteConfirmation(context, cylinder.id, 'cylinder'),
                  icon: Icon(
                    Icons.delete,
                    size: 16.sp,
                    color: context.appColors.errorColor,
                  ),
                  label: Text(
                    'Delete',
                    style: TextStyle(color: context.appColors.errorColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockStatusChip(
    BuildContext context,
    String status,
    Color color,
  ) {
    // Color chipColor;
    // String chipText;

    // if (isOutOfStock) {
    //   chipColor = context.appColors.errorColor;
    //   chipText = 'Out of Stock';
    // } else if (isLowStock) {
    //   chipColor = Colors.orange;
    //   chipText = 'Low Stock';
    // } else {
    //   chipColor = context.appColors.successColor;
    //   chipText = 'In Stock';
    // }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.subtextColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  // Placeholder methods for dialogs and actions
  void _showBulkActionsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: context.appColors.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (BuildContext bottomSheetContext) {
        return Container(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Bulk Actions',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(bottomSheetContext).pop(),
                    icon: Icon(
                      Icons.close,
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              _buildBulkActionTile(
                context,
                'Export Inventory',
                'Export current inventory data to CSV',
                Icons.download,
                () {
                  Navigator.of(bottomSheetContext).pop();
                  _exportInventory(context);
                },
              ),
              _buildBulkActionTile(
                context,
                'Low Stock Alert',
                'View all items with low stock levels',
                Icons.warning,
                () {
                  Navigator.of(bottomSheetContext).pop();
                  _showLowStockItems(context);
                },
              ),
              _buildBulkActionTile(
                context,
                'Bulk Status Update',
                'Update status for multiple items',
                Icons.update,
                () {
                  Navigator.of(bottomSheetContext).pop();
                  _showBulkStatusUpdate(context);
                },
              ),
              _buildBulkActionTile(
                context,
                'Stock Adjustment',
                'Adjust stock levels with reason',
                Icons.tune,
                () {
                  Navigator.of(bottomSheetContext).pop();
                  _showStockAdjustmentDialog(context);
                },
              ),
              SizedBox(height: 16.h),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBulkActionTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: context.appColors.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(icon, color: context.appColors.primaryColor, size: 20.sp),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: context.appColors.textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: context.appColors.subtextColor),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16.sp,
        color: context.appColors.subtextColor,
      ),
      onTap: onTap,
    );
  }

  void _exportInventory(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Exporting inventory data...'),
        backgroundColor: context.appColors.primaryColor,
      ),
    );
  }

  void _showLowStockItems(BuildContext context) {
    setState(() {
      _showLowStockOnly = true;
    });
    _applyFilters();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Showing low stock items only'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showBulkStatusUpdate(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Bulk status update coming soon')),
    );
  }

  void _showStockAdjustmentDialog(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Stock adjustment dialog coming soon')),
    );
  }

  void _showCylinderFiltersDialog(BuildContext context) {
    // TODO: Implement cylinder filters dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Cylinder filters coming soon')),
    );
  }

  void _showPackageFiltersDialog(BuildContext context) {
    // TODO: Implement package filters dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Package filters coming soon')),
    );
  }

  void _showSparePartFiltersDialog(BuildContext context) {
    // TODO: Implement spare part filters dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Spare part filters coming soon')),
    );
  }

  void _showAddCylinderDialog(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CreateCylinderPage()));
  }

  void _showAddPackageDialog(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CreatePackagePage()));
  }

  void _showAddSparePartDialog(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreateSparePartPage()),
    );
  }

  void _showRestockDialog(
    BuildContext context,
    String itemId,
    String itemType,
  ) {
    final TextEditingController quantityController = TextEditingController();
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: context.appColors.surfaceColor,
          title: Text(
            'Restock $itemType',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SizedBox(
            width: 300.w,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Add stock quantity for this $itemType',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Quantity to add',
                    hintText: 'Enter quantity',
                    prefixIcon: Icon(
                      Icons.add_box,
                      color: context.appColors.primaryColor,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: context.appColors.dividerColor,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: context.appColors.primaryColor,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: reasonController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Reason (optional)',
                    hintText: 'Enter reason for restocking',
                    prefixIcon: Icon(
                      Icons.note,
                      color: context.appColors.primaryColor,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: context.appColors.dividerColor,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: context.appColors.primaryColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: context.appColors.subtextColor),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                final quantity = int.tryParse(quantityController.text);
                if (quantity != null && quantity > 0) {
                  _performRestock(
                    context,
                    itemId,
                    itemType,
                    quantity,
                    reasonController.text,
                  );
                  Navigator.of(dialogContext).pop();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid quantity'),
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Restock'),
            ),
          ],
        );
      },
    );
  }

  void _performRestock(
    BuildContext context,
    String itemId,
    String itemType,
    int quantity,
    String reason,
  ) {
    final inventoryBloc = context.read<InventoryBloc>();

    switch (itemType) {
      case 'cylinder':
        inventoryBloc.add(
          RestockCylinderEvent(cylinderId: itemId, quantity: quantity),
        );
        break;
      case 'package':
        inventoryBloc.add(
          RestockPackageEvent(packageId: itemId, quantity: quantity),
        );
        break;
      case 'spare part':
        inventoryBloc.add(
          RestockSparePartEvent(sparePartId: itemId, quantity: quantity),
        );
        break;
    }

    final message = 'Restocking $quantity units of $itemType...';

    SnackBarHelper.showSuccessSnackBar(context, message: message);
  }

  void _showEditCylinderDialog(BuildContext context, CylinderEntity cylinder) {
    // TODO: Implement edit cylinder dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Edit cylinder coming soon')));
  }

  void _showEditPackageDialog(BuildContext context, PackageEntity package) {
    // TODO: Implement edit package dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Edit package coming soon')));
  }

  void _showDeleteConfirmation(
    BuildContext context,
    String itemId,
    String itemType,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Item'),
        content: Text('Are you sure you want to delete this $itemType?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteItem(context, itemId, itemType);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteItem(BuildContext context, String itemId, String itemType) {
    final inventoryBloc = context.read<InventoryBloc>();

    switch (itemType) {
      case 'cylinder':
        inventoryBloc.add(DeleteCylinderEvent(cylinderId: itemId));
        break;
      case 'package':
        inventoryBloc.add(DeletePackageEvent(packageId: itemId));
        break;
      case 'spare part':
        inventoryBloc.add(DeleteSparePartEvent(sparePartId: itemId));
        break;
    }

    final message = 'Deleting $itemType...';

    SnackBarHelper.showSuccessSnackBar(context, message: message);
  }

  // Package card builder
  Widget _buildPackageCard(BuildContext context, PackageEntity package) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      color: context.appColors.surfaceColor,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        package.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: context.appColors.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        package.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                _buildStockStatusChip(
                  context,
                  // package.availableQuantity,
                  // package.isLowStock,
                  // package.isOutOfStock,
                  package.status.name,
                  package.status.color,
                ),
              ],
            ),
            SizedBox(height: 12.h),
            // Package pricing information
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: context.appColors.backgroundColor,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: context.appColors.dividerColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Pricing Details',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'Total Price',
                          '\$${package.totalPrice.toStringAsFixed(2)}',
                        ),
                      ),
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'Final Price',
                          '\$${package.finalPrice.toStringAsFixed(2)}',
                        ),
                      ),
                      if (package.discount > 0)
                        Expanded(
                          child: _buildInfoItem(
                            context,
                            'Discount',
                            '${package.discountPercentage.toStringAsFixed(1)}%',
                          ),
                        ),
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'Profit',
                          '\$${package.profitMargin.toStringAsFixed(2)}',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 12.h),
            // Stock information
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Available',
                    '${package.availableQuantity}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Total Stock',
                    '${package.quantity}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Reserved',
                    '${package.reserved}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(context, 'Sold', '${package.sold}'),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            // Included items
            if (package.includedSpareParts.isNotEmpty) ...[
              Text(
                'Included Items (${package.includedSpareParts.length})',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: context.appColors.backgroundColor,
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Wrap(
                  spacing: 8.w,
                  runSpacing: 4.h,
                  children:
                      package.includedSpareParts.take(3).map((item) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: context.appColors.primaryColor.withValues(
                              alpha: 0.1,
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Text(
                            '${item.quantity}x ${item.name}',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: context.appColors.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        );
                      }).toList()..addAll(
                        package.includedSpareParts.length > 3
                            ? [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 8.w,
                                    vertical: 4.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: context.appColors.subtextColor
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12.r),
                                  ),
                                  child: Text(
                                    '+${package.includedSpareParts.length - 3} more',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(
                                          color: context.appColors.subtextColor,
                                          fontWeight: FontWeight.w500,
                                        ),
                                  ),
                                ),
                              ]
                            : [],
                      ),
                ),
              ),
              SizedBox(height: 12.h),
            ],
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () =>
                      _showRestockDialog(context, package.id, 'package'),
                  icon: Icon(Icons.add_box, size: 16.sp),
                  label: const Text('Restock'),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showEditPackageDialog(context, package),
                  icon: Icon(Icons.edit, size: 16.sp),
                  label: const Text('Edit'),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () =>
                      _showDeleteConfirmation(context, package.id, 'package'),
                  icon: Icon(
                    Icons.delete,
                    size: 16.sp,
                    color: context.appColors.errorColor,
                  ),
                  label: Text(
                    'Delete',
                    style: TextStyle(color: context.appColors.errorColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSparePartCard(BuildContext context, SparePartEntity sparePart) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      color: context.appColors.surfaceColor,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sparePart.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: context.appColors.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        sparePart.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildStockStatusChip(
                      context,
                      // sparePart.availableQuantity,
                      // sparePart.isLowStock,
                      // sparePart.isOutOfStock,
                      sparePart.status.name,
                      sparePart.status.color,
                    ),
                    // SizedBox(height: 4.h),
                    // Container(
                    //   padding: EdgeInsets.symmetric(
                    //     horizontal: 8.w,
                    //     vertical: 2.h,
                    //   ),
                    //   decoration: BoxDecoration(
                    //     color: _getCategoryColor(
                    //       sparePart.category,
                    //     ).withValues(alpha: 0.1),
                    //     borderRadius: BorderRadius.circular(8.r),
                    //     border: Border.all(
                    //       color: _getCategoryColor(sparePart.category),
                    //     ),
                    //   ),
                    //   child: Text(
                    //     sparePart.category.name,
                    //     style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    //       color: _getCategoryColor(sparePart.category),
                    //       fontWeight: FontWeight.w600,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12.h),
            // Pricing and stock information
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Price',
                    '\$${sparePart.price.toStringAsFixed(2)}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Cost',
                    '\$${sparePart.cost.toStringAsFixed(2)}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Profit',
                    '\$${sparePart.profitMargin.toStringAsFixed(2)}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Margin',
                    '${sparePart.profitPercentage.toStringAsFixed(1)}%',
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            // Stock information
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Available',
                    '${sparePart.availableQuantity}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Total Stock',
                    '${sparePart.quantity}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Reserved',
                    '${sparePart.reserved}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(context, 'Sold', '${sparePart.sold}'),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            // Compatibility information
            if (sparePart.compatibleCylinderTypes.isNotEmpty) ...[
              Text(
                'Compatible Cylinders',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Wrap(
                spacing: 6.w,
                runSpacing: 4.h,
                children: sparePart.compatibleCylinderTypes.map((type) {
                  return Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: context.appColors.primaryColor.withValues(
                        alpha: 0.1,
                      ),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      type.name,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
              SizedBox(height: 12.h),
            ],
            // Additional information
            if (sparePart.barcode.isNotEmpty) ...[
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: context.appColors.backgroundColor,
                  borderRadius: BorderRadius.circular(6.r),
                  border: Border.all(color: context.appColors.dividerColor),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.qr_code,
                      size: 16.sp,
                      color: context.appColors.subtextColor,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Barcode: ${sparePart.barcode}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12.h),
            ],
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () =>
                      _showRestockDialog(context, sparePart.id, 'spare part'),
                  icon: Icon(Icons.add_box, size: 16.sp),
                  label: const Text('Restock'),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showEditSparePartDialog(context, sparePart),
                  icon: Icon(Icons.edit, size: 16.sp),
                  label: const Text('Edit'),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showDeleteConfirmation(
                    context,
                    sparePart.id,
                    'spare part',
                  ),
                  icon: Icon(
                    Icons.delete,
                    size: 16.sp,
                    color: context.appColors.errorColor,
                  ),
                  label: Text(
                    'Delete',
                    style: TextStyle(color: context.appColors.errorColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showEditSparePartDialog(
    BuildContext context,
    SparePartEntity sparePart,
  ) {
    // TODO: Implement edit spare part dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit spare part coming soon')),
    );
  }
}
