import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

import '../../../../../core/enums/user_role.dart';
import '../../../../authentication/domain/entities/user_entity.dart';

enum SmsType {
  individual('Individual SMS', Icons.person, Colors.blue),
  bulk('Bulk SMS', Icons.group, Colors.green),
  roleBasedSms('Role-based SMS', Icons.admin_panel_settings, Colors.purple),
  otpSms('OTP SMS', Icons.security, Colors.orange);

  const SmsType(this.label, this.icon, this.color);
  final String label;
  final IconData icon;
  final Color color;
}

class SmsMetrics {
  final int totalSent;
  final int totalFailed;
  final int successRate;
  final bool isHealthy;

  const SmsMetrics({
    required this.totalSent,
    required this.totalFailed,
    required this.successRate,
    required this.isHealthy,
  });
}

class AdminSmsManagementPage extends StatefulWidget {
  const AdminSmsManagementPage({super.key});

  @override
  State<AdminSmsManagementPage> createState() => _AdminSmsManagementPageState();
}

class _AdminSmsManagementPageState extends State<AdminSmsManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _phoneController = TextEditingController();
  final _messageController = TextEditingController();
  final _otpController = TextEditingController();
  final _bulkPhonesController = TextEditingController();

  // State variables
  SmsType _selectedSmsType = SmsType.individual;
  UserRole _selectedRole = UserRole.customer;
  bool _isLoading = false;
  bool _onlyActiveUsers = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneController.dispose();
    _messageController.dispose();
    _otpController.dispose();
    _bulkPhonesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            _buildMetricsOverview(context),
            _buildTabBar(context),
            Expanded(child: _buildTabBarView(context)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'SMS Management',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Send and manage SMS messages',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.sms,
              color: context.appColors.primaryColor,
              size: 24.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsOverview(BuildContext context) {
    final metrics = _getSmsMetrics();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Expanded(
            child: _buildMetricCard(
              context,
              'Total Sent',
              metrics.totalSent.toString(),
              Icons.send,
              Colors.blue,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildMetricCard(
              context,
              'Failed',
              metrics.totalFailed.toString(),
              Icons.error,
              Colors.red,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildMetricCard(
              context,
              'Success Rate',
              '${metrics.successRate}%',
              Icons.check_circle,
              Colors.green,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildMetricCard(
              context,
              'Service Health',
              metrics.isHealthy ? 'Healthy' : 'Issues',
              metrics.isHealthy ? Icons.health_and_safety : Icons.warning,
              metrics.isHealthy ? Colors.green : Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(6.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'Send SMS'),
          Tab(text: 'History'),
          Tab(text: 'Settings'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildSendSmsTab(context),
        _buildHistoryTab(context),
        _buildSettingsTab(context),
      ],
    );
  }

  Widget _buildSendSmsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSmsTypeSelector(context),
            SizedBox(height: 24.h),
            _buildSmsForm(context),
            SizedBox(height: 32.h),
            _buildSendButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSmsTypeSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'SMS Type',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: SmsType.values.map((type) {
            final isSelected = _selectedSmsType == type;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedSmsType = type;
                });
              },
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: isSelected
                      ? type.color.withValues(alpha: 0.1)
                      : context.appColors.surfaceColor,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: isSelected
                        ? type.color
                        : context.appColors.dividerColor,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      type.icon,
                      color: isSelected
                          ? type.color
                          : context.appColors.subtextColor,
                      size: 24.sp,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      type.label,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isSelected
                            ? type.color
                            : context.appColors.textColor,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSmsForm(BuildContext context) {
    switch (_selectedSmsType) {
      case SmsType.individual:
        return _buildIndividualSmsForm(context);
      case SmsType.bulk:
        return _buildBulkSmsForm(context);
      case SmsType.roleBasedSms:
        return _buildRoleBasedSmsForm(context);
      case SmsType.otpSms:
        return _buildOtpSmsForm(context);
    }
  }

  Widget _buildIndividualSmsForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          hint: '+252612345678',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Phone number is required';
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildTextField(
          controller: _messageController,
          label: 'Message',
          hint: 'Enter your message here...',
          maxLines: 4,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Message is required';
            }
            if (value.length > 459) {
              return 'Message too long (max 459 characters)';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildBulkSmsForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _bulkPhonesController,
          label: 'Phone Numbers',
          hint:
              'Enter phone numbers separated by commas\n+252612345678, +252612345679, ...',
          maxLines: 3,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Phone numbers are required';
            }
            final phones = value.split(',').map((e) => e.trim()).toList();
            if (phones.length > 100) {
              return 'Maximum 100 recipients allowed';
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildTextField(
          controller: _messageController,
          label: 'Message',
          hint: 'Enter your message here...',
          maxLines: 4,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Message is required';
            }
            if (value.length > 459) {
              return 'Message too long (max 459 characters)';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildRoleBasedSmsForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDropdownField(
          label: 'Target Role',
          value: _selectedRole,
          items: UserRole.values,
          onChanged: (value) {
            setState(() {
              _selectedRole = value!;
            });
          },
          itemBuilder: (role) => Text(role.name),
        ),
        SizedBox(height: 16.h),
        _buildSwitchTile(
          title: 'Only Active Users',
          subtitle: 'Send only to active users',
          value: _onlyActiveUsers,
          onChanged: (value) {
            setState(() {
              _onlyActiveUsers = value;
            });
          },
        ),
        SizedBox(height: 16.h),
        _buildTextField(
          controller: _messageController,
          label: 'Message',
          hint: 'Enter your message here...',
          maxLines: 4,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Message is required';
            }
            if (value.length > 459) {
              return 'Message too long (max 459 characters)';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildOtpSmsForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          hint: '+252612345678',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Phone number is required';
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildTextField(
          controller: _otpController,
          label: 'OTP Code',
          hint: '123456',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'OTP code is required';
            }
            if (value.length < 4 || value.length > 8) {
              return 'OTP must be 4-8 digits';
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildTextField(
          controller: _messageController,
          label: 'Custom Message (Optional)',
          hint: 'Your verification code is {OTP}. Do not share this code.',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: context.appColors.subtextColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: context.appColors.dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: context.appColors.dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: context.appColors.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: Colors.red),
            ),
            filled: true,
            fillColor: context.appColors.surfaceColor,
            contentPadding: EdgeInsets.all(12.w),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required String label,
    required T value,
    required List<T> items,
    required Function(T?) onChanged,
    required Widget Function(T) itemBuilder,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: context.appColors.subtextColor,
              ),
              items: items.map((T item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: itemBuilder(item),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: context.appColors.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _sendSms,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.appColors.primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: _isLoading
            ? SizedBox(
                height: 20.h,
                width: 20.w,
                child: const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(_selectedSmsType.icon, size: 20.sp),
                  SizedBox(width: 8.w),
                  Text(
                    'Send ${_selectedSmsType.label}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildHistoryTab(BuildContext context) {
    return Center(
      child: Text(
        'SMS History\n(Coming Soon)',
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          color: context.appColors.subtextColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSettingsTab(BuildContext context) {
    return Center(
      child: Text(
        'SMS Settings\n(Coming Soon)',
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          color: context.appColors.subtextColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _sendSms() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isLoading = false;
      });

      // TODO: Implement actual SMS sending
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${_selectedSmsType.label} sent successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Clear form
      _phoneController.clear();
      _messageController.clear();
      _otpController.clear();
      _bulkPhonesController.clear();
    }
  }

  SmsMetrics _getSmsMetrics() {
    // Mock data - replace with actual API call
    return const SmsMetrics(
      totalSent: 1247,
      totalFailed: 23,
      successRate: 98,
      isHealthy: true,
    );
  }
}
