import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/authentication/presentation/pages/login_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_dashboard_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_inventory_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_order_management_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_users_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_settings_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_sms_management_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_notification_management_page.dart';

class AdminDrawer extends StatelessWidget {
  const AdminDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Unauthenticated) {
          context.pushAndRemoveUntilRoute(const LoginPage());
        }
      },
      child: Drawer(
        backgroundColor: context.appColors.surfaceColor,
        child: SafeArea(
          child: Column(
            children: [
              _buildDrawerHeader(context),
              const SizedBox(height: 20),
              Expanded(
                child: Column(
                  children: [
                    // Main content items
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: [
                          _buildDrawerItem(
                            context,
                            icon: Icons.dashboard,
                            title: 'Dashboard',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushReplacementRoute(
                                const AdminDashboardPage(),
                              );
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.inventory,
                            title: 'Inventory Management',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushRoute(const AdminInventoryPage());
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.shopping_cart,
                            title: 'Order Management',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushRoute(
                                const AdminOrderManagementPage(),
                              );
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.people,
                            title: 'User Management',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushRoute(const AdminUsersPage());
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.sms,
                            title: 'SMS Management',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushRoute(const AdminSmsManagementPage());
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.notifications,
                            title: 'Notification Management',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushRoute(
                                const AdminNotificationManagementPage(),
                              );
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.settings,
                            title: 'Settings',
                            onTap: () {
                              Navigator.pop(context);
                              context.pushRoute(const AdminSettingsPage());
                            },
                          ),
                        ],
                      ),
                    ),

                    // Logout button at the bottom
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        children: [
                          const Divider(),
                          _buildDrawerItem(
                            context,
                            icon: Icons.logout,
                            title: 'Logout',
                            textColor: context.appColors.errorColor,
                            onTap: () => _showLogoutDialog(context),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      width: context.screenWidth,
      decoration: BoxDecoration(
        color: context.appColors.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 30.r,
            backgroundColor: Colors.white,
            child: Icon(
              Icons.admin_panel_settings,
              size: 30.sp,
              color: context.appColors.primaryColor,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'Admin Panel',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'System Management',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? context.appColors.textColor,
        size: 24.sp,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: textColor ?? context.appColors.textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
      hoverColor: context.appColors.primaryColor.withValues(alpha: 0.1),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: context.appColors.surfaceColor,
          title: Text(
            'Logout',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: Text(
                'Cancel',
                style: TextStyle(color: context.appColors.subtextColor),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(dialogContext);
                context.read<AuthenticationBloc>().add(LogoutEvent());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.errorColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
