import 'package:equatable/equatable.dart';

class UpdateUserParams extends Equatable {
  final String userId;
  final String? email;
  final String? tag;
  final List<double> coordinates;
  final String? details;
  final String? contactPhone;

  const UpdateUserParams({
    required this.userId,
    this.email,
    this.tag,
    required this.coordinates,
    this.details,
    this.contactPhone,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'email': email,
      'tag': tag,
      'coordinates': coordinates,
      'details': details,
      'contactPhone': contactPhone,
    };
  }

  @override
  List<Object?> get props => [
    userId,
    email,
    tag,
    coordinates,
    details,
    contactPhone,
  ];
}
