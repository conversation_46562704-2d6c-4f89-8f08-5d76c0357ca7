part of 'user_bloc.dart';

abstract class UserState extends Equatable {
  const UserState();

  @override
  List<Object?> get props => [];
}

class UserInitial extends UserState {}

// Register Admin or Agent States
class RegisterAdminOrAgentLoading extends UserState {}
class RegisterAdminOrAgentSuccess extends UserState {}
class RegisterAdminOrAgentFailure extends UserState {
  final AppFailure appFailure;

  const RegisterAdminOrAgentFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Update User States
class UpdateUserLoading extends UserState {}
class UpdateUserSuccess extends UserState {
  final String message;

  const UpdateUserSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}
class UpdateUserFailure extends UserState {
  final AppFailure appFailure;

  const UpdateUserFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Delete User States
class DeleteUserLoading extends UserState {}
class DeleteUserSuccess extends UserState {}
class DeleteUserFailure extends UserState {
  final AppFailure appFailure;

  const DeleteUserFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Notification Subscription States
class SubscribeToNotificationsLoading extends UserState {}
class SubscribeToNotificationsSuccess extends UserState {}
class SubscribeToNotificationsFailure extends UserState {
  final AppFailure appFailure;

  const SubscribeToNotificationsFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

class UnsubscribeFromNotificationsLoading extends UserState {}
class UnsubscribeFromNotificationsSuccess extends UserState {}
class UnsubscribeFromNotificationsFailure extends UserState {
  final AppFailure appFailure;

  const UnsubscribeFromNotificationsFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Get All Users States
class GetAllUsersLoading extends UserState {}
class GetAllUsersSuccess extends UserState {
  final List<UserEntity> users;

  const GetAllUsersSuccess({required this.users});

  @override
  List<Object?> get props => [users];
}
class GetAllUsersFailure extends UserState {
  final AppFailure appFailure;

  const GetAllUsersFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Get Current User States
class GetCurrentUserLoading extends UserState {}
class GetCurrentUserSuccess extends UserState {
  final UserEntity user;

  const GetCurrentUserSuccess({required this.user});

  @override
  List<Object?> get props => [user];
}
class GetCurrentUserFailure extends UserState {
  final AppFailure appFailure;

  const GetCurrentUserFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}