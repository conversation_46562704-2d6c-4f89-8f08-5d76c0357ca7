import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/helpers/bottom_sheet_helper.dart';

import '../../../../core/enums/selection_type.dart';

class CustomSelectFieldDisplayer<T> extends StatelessWidget {
  final String labelText;
  final List<T> selectedItems;
  final SelectionType selectionType;
  final String Function(T) displayItem;
  final String Function(T)? displaySubTitle;
  final String? Function(T)? displayImage;
  final Function(List<T>) onSelectionChanged; // Callback for selection changes
  final IconData? leadingIcon;
  final FormFieldValidator<String>? validator;
  final List<T> options; // Options to select from
  final String bottomSheetTitle;
  final bool showClearButton;

  const CustomSelectFieldDisplayer({
    super.key,
    required this.labelText,
    required this.selectedItems,
    required this.displayItem,
    this.displaySubTitle,
    required this.onSelectionChanged,
    required this.selectionType,
    required this.options, // Pass options here
    required this.bottomSheetTitle, // Bottom sheet title
    this.leadingIcon = Icons.person,
    this.validator,
    this.displayImage,
    this.showClearButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: () async {
        // Open the bottom sheet when tapped
        await BottomSheetHelper.showMultiSelectionBottomSheet(
          context: context,
          items: options,
          title: bottomSheetTitle,
          getTitle: displayItem,
          initiallySelectedItems: selectedItems,
          onSelectionChanged: onSelectionChanged,
          getAvatarUrl: displayImage,
          getSubtitle: displaySubTitle,
        );
      },
      child: FormField<String>(
        validator: validator,
        builder: (FormFieldState<String> state) {
          return InputDecorator(
            decoration: InputDecoration(
              labelText: labelText,
              labelStyle: textTheme.labelMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.r),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.r),
                borderSide: BorderSide(color: Colors.blueGrey.shade700),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.r),
                borderSide: BorderSide(color: Colors.blueGrey.shade300),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: leadingIcon != null ? 10.w : 0,
                vertical: leadingIcon != null ? 12.h : 0,
              ),
              errorText: state.hasError ? state.errorText : null,
              fillColor: Colors.transparent,
              filled: true,
            ),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: leadingIcon != null
                      ? Icon(
                          leadingIcon,
                          size: 20.h,
                          color: Colors.blueGrey.shade300,
                        )
                      : null,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        Text(
                          selectedItems.isNotEmpty
                              ? (selectionType == SelectionType.multiSelection
                                    ? selectedItems.map(displayItem).join(', ')
                                    : displayItem(selectedItems.first))
                              : labelText,
                          style: textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: context.appColors.primaryColor,
                  size: 30.h,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
