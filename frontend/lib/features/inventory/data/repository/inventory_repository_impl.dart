import 'package:fpdart/fpdart.dart';

import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';
import '../../../../core/enums/package_status.dart';
import '../../../../core/enums/spare_part_status.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/cylinder_entity.dart';
import '../../domain/entities/package_entity.dart';
import '../../domain/entities/spare_part_entity.dart';
import '../../domain/entities/package_item_entity.dart';
import '../../domain/repository/inventory_repository.dart';
import '../mappers/cylinder_mapper.dart';
import '../mappers/package_mapper.dart';
import '../mappers/spare_part_mapper.dart';
import '../source/remote/inventory_remote_datasource.dart';

class InventoryRepositoryImpl implements InventoryRepository {
  final InventoryRemoteDataSource inventoryRemoteDataSource;

  const InventoryRepositoryImpl({required this.inventoryRemoteDataSource});

  @override
  FutureEitherFailOr<List<CylinderEntity>> getCylinders({
    CylinderType? type,
    CylinderMaterial? material,
    CylinderStatus? status,
    int? page,
    int? limit,
  }) async {
    final response = await inventoryRemoteDataSource.getCylinders(
      type: type,
      material: material,
      status: status,
      page: page,
      limit: limit,
    );
    return response.fold((failure) => left(failure), (cylinderModels) {
      final cylinderEntities = CylinderMapper.toEntityList(cylinderModels);
      return right(cylinderEntities);
    });
  }

  @override
  FutureEitherFailOr<CylinderEntity> getCylinderById({
    required String cylinderId,
  }) async {
    final response = await inventoryRemoteDataSource.getCylinderById(
      cylinderId: cylinderId,
    );
    return response.fold((failure) => left(failure), (cylinderModel) {
      final cylinderEntity = CylinderMapper.toEntity(cylinderModel);
      return right(cylinderEntity);
    });
  }

  @override
  FutureEitherFailOr<List<PackageEntity>> getPackages({
    PackageStatus? status,
    int? page,
    int? limit,
  }) async {
    final response = await inventoryRemoteDataSource.getPackages(
      status: status,
      page: page,
      limit: limit,
    );
    return response.fold((failure) => left(failure), (packageModels) {
      final packageEntities = PackageMapper.toEntityList(packageModels);
      return right(packageEntities);
    });
  }

  @override
  FutureEitherFailOr<PackageEntity> getPackageById({
    required String packageId,
  }) async {
    final response = await inventoryRemoteDataSource.getPackageById(
      packageId: packageId,
    );
    return response.fold((failure) => left(failure), (packageModel) {
      final packageEntity = PackageMapper.toEntity(packageModel);
      return right(packageEntity);
    });
  }

  @override
  FutureEitherFailOr<List<SparePartEntity>> getSpareParts({
    SparePartCategory? category,
    SparePartStatus? status,
    List<CylinderType>? compatibleWith,
    int? page,
    int? limit,
  }) async {
    final response = await inventoryRemoteDataSource.getSpareParts(
      category: category,
      status: status,
      compatibleWith: compatibleWith,
      page: page,
      limit: limit,
    );
    return response.fold((failure) => left(failure), (sparePartModels) {
      final sparePartEntities = SparePartMapper.toEntityList(sparePartModels);
      return right(sparePartEntities);
    });
  }

  @override
  FutureEitherFailOr<SparePartEntity> getSparePartById({
    required String sparePartId,
  }) async {
    final response = await inventoryRemoteDataSource.getSparePartById(
      sparePartId: sparePartId,
    );
    return response.fold((failure) => left(failure), (sparePartModel) {
      final sparePartEntity = SparePartMapper.toEntity(sparePartModel);
      return right(sparePartEntity);
    });
  }

  // Cylinder management
  @override
  FutureEitherFailOr<void> restockCylinder({
    required String cylinderId,
    required int quantity,
  }) async {
    final response = await inventoryRemoteDataSource.restockCylinder(
      cylinderId: cylinderId,
      quantity: quantity,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> adjustCylinderStock({
    required String cylinderId,
    required int adjustment,
    required String reason,
  }) async {
    final response = await inventoryRemoteDataSource.adjustCylinderStock(
      cylinderId: cylinderId,
      adjustment: adjustment,
      reason: reason,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> bulkUpdateCylinderStatus({
    required List<String> cylinderIds,
    required CylinderStatus status,
  }) async {
    final response = await inventoryRemoteDataSource.bulkUpdateCylinderStatus(
      cylinderIds: cylinderIds,
      status: status,
    );
    return response;
  }

  @override
  FutureEitherFailOr<String> createCylinder({
    required CylinderType type,
    required CylinderMaterial material,
    required double price,
    required double cost,
    required String? imageUrl,
    required String description,
    required int quantity,
    required int minimumStockLevel,
    required CylinderStatus status,
  }) async {
    final response = await inventoryRemoteDataSource.createCylinder(
      type: type,
      material: material,
      price: price,
      cost: cost,
      imageUrl: imageUrl,
      description: description,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
      status: status,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> updateCylinder({
    required String cylinderId,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? quantity,
    int? minimumStockLevel,
    CylinderStatus? status,
  }) async {
    final response = await inventoryRemoteDataSource.updateCylinder(
      cylinderId: cylinderId,
      type: type,
      material: material,
      price: price,
      cost: cost,
      imageUrl: imageUrl,
      description: description,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
      status: status,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> deleteCylinder({required String cylinderId}) async {
    final response = await inventoryRemoteDataSource.deleteCylinder(
      cylinderId: cylinderId,
    );
    return response;
  }

  // Package management
  @override
  FutureEitherFailOr<void> restockPackage({
    required String packageId,
    required int quantity,
  }) async {
    final response = await inventoryRemoteDataSource.restockPackage(
      packageId: packageId,
      quantity: quantity,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> adjustPackageStock({
    required String packageId,
    required int adjustment,
    required String reason,
  }) async {
    final response = await inventoryRemoteDataSource.adjustPackageStock(
      packageId: packageId,
      adjustment: adjustment,
      reason: reason,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> bulkUpdatePackageStatus({
    required List<String> packageIds,
    required PackageStatus status,
  }) async {
    final response = await inventoryRemoteDataSource.bulkUpdatePackageStatus(
      packageIds: packageIds,
      status: status,
    );
    return response;
  }

  @override
  FutureEitherFailOr<String> createPackage({
    required String name,
    required String description,
    required String cylinderId,
    required List<PackageItemEntity> includedSpareParts,
    required double totalPrice,
    required double costPrice,
    required double discount,
    required String? imageUrl,
    required int quantity,
    required int minimumStockLevel,
  }) async {
    final response = await inventoryRemoteDataSource.createPackage(
      name: name,
      description: description,
      cylinderId: cylinderId,
      includedSpareParts: includedSpareParts,
      totalPrice: totalPrice,
      costPrice: costPrice,
      discount: discount,
      imageUrl: imageUrl,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> updatePackage({
    required String packageId,
    String? name,
    String? description,
    String? cylinderId,
    List<PackageItemEntity>? includedSpareParts,
    double? totalPrice,
    double? costPrice,
    double? discount,
    String? imageUrl,
    int? quantity,
    int? minimumStockLevel,
  }) async {
    final response = await inventoryRemoteDataSource.updatePackage(
      packageId: packageId,
      name: name,
      description: description,
      cylinderId: cylinderId,
      includedSpareParts: includedSpareParts,
      totalPrice: totalPrice,
      costPrice: costPrice,
      discount: discount,
      imageUrl: imageUrl,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> deletePackage({required String packageId}) async {
    final response = await inventoryRemoteDataSource.deletePackage(
      packageId: packageId,
    );
    return response;
  }

  // Spare Part management
  @override
  FutureEitherFailOr<void> restockSparePart({
    required String sparePartId,
    required int quantity,
  }) async {
    final response = await inventoryRemoteDataSource.restockSparePart(
      sparePartId: sparePartId,
      quantity: quantity,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> adjustSparePartStock({
    required String sparePartId,
    required int adjustment,
    required String reason,
  }) async {
    final response = await inventoryRemoteDataSource.adjustSparePartStock(
      sparePartId: sparePartId,
      adjustment: adjustment,
      reason: reason,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> bulkUpdateSparePartStatus({
    required List<String> sparePartIds,
    required SparePartStatus status,
  }) async {
    final response = await inventoryRemoteDataSource.bulkUpdateSparePartStatus(
      sparePartIds: sparePartIds,
      status: status,
    );
    return response;
  }

  @override
  FutureEitherFailOr<String> createSparePart({
    required String name,
    required String description,
    required double price,
    required double cost,
    required SparePartCategory category,
    required List<CylinderType> compatibleCylinderTypes,
    required String barcode,
    required int minimumStockLevel,
  }) async {
    final response = await inventoryRemoteDataSource.createSparePart(
      name: name,
      description: description,
      price: price,
      cost: cost,
      category: category,
      compatibleCylinderTypes: compatibleCylinderTypes,
      barcode: barcode,
      minimumStockLevel: minimumStockLevel,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> updateSparePart({
    required String sparePartId,
    String? name,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? minimumStockLevel,
  }) async {
    final response = await inventoryRemoteDataSource.updateSparePart(
      sparePartId: sparePartId,
      name: name,
      description: description,
      price: price,
      cost: cost,
      category: category,
      compatibleCylinderTypes: compatibleCylinderTypes,
      barcode: barcode,
      minimumStockLevel: minimumStockLevel,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> deleteSparePart({
    required String sparePartId,
  }) async {
    final response = await inventoryRemoteDataSource.deleteSparePart(
      sparePartId: sparePartId,
    );
    return response;
  }
}
