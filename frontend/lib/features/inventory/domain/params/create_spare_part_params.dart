import 'package:equatable/equatable.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';

class CreateSparePartParams extends Equatable {
  final String name;
  final String description;
  final double price;
  final double cost;
  final SparePartCategory category;
  final List<CylinderType> compatibleCylinderTypes;
  final String barcode;
  final int minimumStockLevel;

  const CreateSparePartParams({
    required this.name,
    required this.description,
    required this.price,
    required this.cost,
    required this.category,
    required this.compatibleCylinderTypes,
    required this.barcode,
    required this.minimumStockLevel,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'price': price,
      'cost': cost,
      'category': category.label,
      'compatibleCylinderTypes': compatibleCylinderTypes.map((type) => type.name).toList(),
      'barcode': barcode,
      'minimumStockLevel': minimumStockLevel,
    };
  }

  @override
  List<Object?> get props => [
    name,
    description,
    price,
    cost,
    category,
    compatibleCylinderTypes,
    barcode,
    minimumStockLevel,
  ];
}
