import 'package:equatable/equatable.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';

class CreateCylinderParams extends Equatable {
  final CylinderType type;
  final CylinderMaterial material;
  final double price;
  final double cost;
  final String? imageUrl;
  final String description;
  final int quantity;
  final int minimumStockLevel;
  final CylinderStatus status;

  const CreateCylinderParams({
    required this.type,
    required this.material,
    required this.price,
    required this.cost,
     this.imageUrl,
    required this.description,
    required this.quantity,
    required this.minimumStockLevel,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'material': material.label,
      'price': price,
      'cost': cost,
      'imageUrl': imageUrl,
      'description': description,
      'quantity': quantity,
      'minimumStockLevel': minimumStockLevel,
      'status': status.label,
    };
  }

  @override
  List<Object?> get props => [
    type,
    material,
    price,
    cost,
    imageUrl,
    description,
    quantity,
    minimumStockLevel,
    status,
  ];
}
