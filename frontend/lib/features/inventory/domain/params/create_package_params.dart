import 'package:equatable/equatable.dart';
import '../entities/package_item_entity.dart';

class CreatePackageParams extends Equatable {
  final String name;
  final String description;
  final String cylinderId;
  final List<PackageItemEntity> includedSpareParts;
  final double totalPrice;
  final double costPrice;
  final double discount;
  final String? imageUrl;
  final int quantity;
  final int minimumStockLevel;

  const CreatePackageParams({
    required this.name,
    required this.description,
    required this.cylinderId,
    required this.includedSpareParts,
    required this.totalPrice,
    required this.costPrice,
    required this.discount,
     this.imageUrl,
    required this.quantity,
    required this.minimumStockLevel,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'cylinderId': cylinderId,
      'includedSpareParts': includedSpareParts.map((item) => {
        'sparePartId': item.sparePartId,
        'name': item.name,
        'quantity': item.quantity,
        'unitPrice': item.unitPrice,
        'totalPrice': item.totalPrice,
      }).toList(),
      'totalPrice': totalPrice,
      'costPrice': costPrice,
      'discount': discount,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'minimumStockLevel': minimumStockLevel,
    };
  }

  @override
  List<Object?> get props => [
    name,
    description,
    cylinderId,
    includedSpareParts,
    totalPrice,
    costPrice,
    discount,
    imageUrl,
    quantity,
    minimumStockLevel,
  ];
}
