import 'package:equatable/equatable.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';

class UpdateCylinderParams extends Equatable {
  final String cylinderId;
  final CylinderType? type;
  final CylinderMaterial? material;
  final double? price;
  final double? cost;
  final String? imageUrl;
  final String? description;
  final int? quantity;
  final int? minimumStockLevel;
  final CylinderStatus? status;

  const UpdateCylinderParams({
    required this.cylinderId,
    this.type,
    this.material,
    this.price,
    this.cost,
    this.imageUrl,
    this.description,
    this.quantity,
    this.minimumStockLevel,
    this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'cylinderId': cylinderId,
      if (type != null) 'type': type!.name,
      if (material != null) 'material': material!.label,
      if (price != null) 'price': price,
      if (cost != null) 'cost': cost,
      if (imageUrl != null) 'imageUrl': imageUrl,
      if (description != null) 'description': description,
      if (quantity != null) 'quantity': quantity,
      if (minimumStockLevel != null) 'minimumStockLevel': minimumStockLevel,
      if (status != null) 'status': status!.label,
    };
  }

  @override
  List<Object?> get props => [
    cylinderId,
    type,
    material,
    price,
    cost,
    imageUrl,
    description,
    quantity,
    minimumStockLevel,
    status,
  ];
}
