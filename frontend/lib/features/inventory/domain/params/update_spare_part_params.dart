import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';

class UpdateSparePartParams {
  final String sparePartId;
  final String? name;
  final String? description;
  final double? price;
  final double? cost;
  final SparePartCategory? category;
  final List<CylinderType>? compatibleCylinderTypes;
  final String? barcode;
  final int? minimumStockLevel;

  const UpdateSparePartParams({
    required this.sparePartId,
    this.name,
    this.description,
    this.price,
    this.cost,
    this.category,
    this.compatibleCylinderTypes,
    this.barcode,
    this.minimumStockLevel,
  });
}
