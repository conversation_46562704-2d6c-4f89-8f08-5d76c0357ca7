import '../entities/package_item_entity.dart';

class UpdatePackageParams {
  final String packageId;
  final String? name;
  final String? description;
  final String? cylinderId;
  final List<PackageItemEntity>? includedSpareParts;
  final double? totalPrice;
  final double? costPrice;
  final double? discount;
  final String? imageUrl;
  final int? quantity;
  final int? minimumStockLevel;

  const UpdatePackageParams({
    required this.packageId,
    this.name,
    this.description,
    this.cylinderId,
    this.includedSpareParts,
    this.totalPrice,
    this.costPrice,
    this.discount,
    this.imageUrl,
    this.quantity,
    this.minimumStockLevel,
  });
}
