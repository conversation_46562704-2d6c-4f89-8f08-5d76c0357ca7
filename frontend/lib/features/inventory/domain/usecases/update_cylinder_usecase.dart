import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/update_cylinder_params.dart';
import '../repository/inventory_repository.dart';

class UpdateCylinderUseCase implements UseCase<void, UpdateCylinderParams> {
  final InventoryRepository repository;

  const UpdateCylinderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required UpdateCylinderParams params,
  }) async {
    final response = await repository.updateCylinder(
      cylinderId: params.cylinderId,
      type: params.type,
      material: params.material,
      price: params.price,
      cost: params.cost,
      imageUrl: params.imageUrl,
      description: params.description,
      quantity: params.quantity,
      minimumStockLevel: params.minimumStockLevel,
      status: params.status,
    );
    return response;
  }
}
