import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/create_package_params.dart';
import '../repository/inventory_repository.dart';

class CreatePackageUseCase implements UseCase<String, CreatePackageParams> {
  final InventoryRepository repository;

  const CreatePackageUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({
    required CreatePackageParams params,
  }) async {
    final response = await repository.createPackage(
      name: params.name,
      description: params.description,
      cylinderId: params.cylinderId,
      includedSpareParts: params.includedSpareParts,
      totalPrice: params.totalPrice,
      costPrice: params.costPrice,
      discount: params.discount,
      imageUrl: params.imageUrl,
      quantity: params.quantity,
      minimumStockLevel: params.minimumStockLevel,
    );
    return response;
  }
}
