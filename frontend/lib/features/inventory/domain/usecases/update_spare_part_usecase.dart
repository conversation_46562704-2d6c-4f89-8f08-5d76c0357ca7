import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/update_spare_part_params.dart';
import '../repository/inventory_repository.dart';

class UpdateSparePartUseCase implements UseCase<void, UpdateSparePartParams> {
  final InventoryRepository repository;

  const UpdateSparePartUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required UpdateSparePartParams params,
  }) async {
    final response = await repository.updateSparePart(
      sparePartId: params.sparePartId,
      name: params.name,
      description: params.description,
      price: params.price,
      cost: params.cost,
      category: params.category,
      compatibleCylinderTypes: params.compatibleCylinderTypes,
      barcode: params.barcode,
      minimumStockLevel: params.minimumStockLevel,
    );
    return response;
  }
}
