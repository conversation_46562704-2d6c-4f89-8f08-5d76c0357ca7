import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../../../../core/enums/spare_part_status.dart';

class SparePartEntity {
  final String id;
  final String name;
  final String description;
  final double price;
  final double cost;
  final SparePartCategory category;
  final List<CylinderType> compatibleCylinderTypes;
  final String barcode;
  final int quantity;
  final int reserved;
  final int sold;
  final int minimumStockLevel;
  final SparePartStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  SparePartEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.cost,
    required this.category,
    required this.compatibleCylinderTypes,
    required this.barcode,
    required this.quantity,
    required this.reserved,
    required this.sold,
    required this.minimumStockLevel,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  int get availableQuantity => quantity - reserved;
  bool get isLowStock => availableQuantity <= minimumStockLevel;
  bool get isOutOfStock => availableQuantity <= 0;
  double get profitMargin => price - cost;
  double get profitPercentage => cost > 0 ? (profitMargin / cost) * 100 : 0;

  // Helper methods
  bool canReserve(int requestedQuantity) {
    return availableQuantity >= requestedQuantity;
  }

  bool isCompatibleWith(CylinderType cylinderType) {
    return compatibleCylinderTypes.contains(cylinderType);
  }

  SparePartEntity copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? quantity,
    int? reserved,
    int? sold,
    int? minimumStockLevel,
    SparePartStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SparePartEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      category: category ?? this.category,
      compatibleCylinderTypes: compatibleCylinderTypes ?? this.compatibleCylinderTypes,
      barcode: barcode ?? this.barcode,
      quantity: quantity ?? this.quantity,
      reserved: reserved ?? this.reserved,
      sold: sold ?? this.sold,
      minimumStockLevel: minimumStockLevel ?? this.minimumStockLevel,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SparePartEntity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SparePartEntity(id: $id, name: $name, category: $category, price: $price, quantity: $quantity, status: $status)';
  }
}
