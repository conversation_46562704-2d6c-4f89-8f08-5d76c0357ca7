import 'package:frontend/core/constants/api_end_points.dart';

import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';

class CylinderEntity {
  final String id;
  final CylinderType type;
  final CylinderMaterial material;
  final double price;
  final double cost;
  final String imageUrl;
  final String description;
  final int quantity;
  final int reserved;
  final int sold;
  final int minimumStockLevel;
  final CylinderStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  CylinderEntity({
    required this.id,
    required this.type,
    required this.material,
    required this.price,
    required this.cost,
    required this.imageUrl,
    required this.description,
    required this.quantity,
    required this.reserved,
    required this.sold,
    required this.minimumStockLevel,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  int get availableQuantity => quantity - reserved;
  bool get isLowStock => availableQuantity <= minimumStockLevel;
  bool get isOutOfStock => availableQuantity <= 0;
  double get profitMargin => price - cost;
  double get profitPercentage => cost > 0 ? (profitMargin / cost) * 100 : 0;

  // Helper methods
  bool canReserve(int requestedQuantity) {
    return availableQuantity >= requestedQuantity;
  }

  String get formattedImageUrl => '${ApiEndpoints.baseUrl}$imageUrl';

  CylinderEntity copyWith({
    String? id,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? quantity,
    int? reserved,
    int? sold,
    int? minimumStockLevel,
    CylinderStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CylinderEntity(
      id: id ?? this.id,
      type: type ?? this.type,
      material: material ?? this.material,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      imageUrl: imageUrl ?? this.imageUrl,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      reserved: reserved ?? this.reserved,
      sold: sold ?? this.sold,
      minimumStockLevel: minimumStockLevel ?? this.minimumStockLevel,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CylinderEntity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CylinderEntity(id: $id, type: $type, material: $material, price: $price, quantity: $quantity, status: $status)';
  }
}
