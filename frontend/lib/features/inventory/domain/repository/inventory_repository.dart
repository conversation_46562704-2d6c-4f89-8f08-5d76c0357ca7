import '../../../../core/errors/app_failure.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';
import '../../../../core/enums/package_status.dart';
import '../../../../core/enums/spare_part_status.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../entities/cylinder_entity.dart';
import '../entities/package_entity.dart';
import '../entities/spare_part_entity.dart';
import '../entities/package_item_entity.dart';

abstract class InventoryRepository {
  // Get methods
  FutureEitherFailOr<List<CylinderEntity>> getCylinders({
    CylinderType? type,
    CylinderMaterial? material,
    CylinderStatus? status,
    int? page,
    int? limit,
  });

  FutureEitherFailOr<CylinderEntity> getCylinderById({
    required String cylinderId,
  });

  FutureEitherFailOr<List<PackageEntity>> getPackages({
    PackageStatus? status,
    int? page,
    int? limit,
  });

  FutureEitherFailOr<PackageEntity> getPackageById({required String packageId});

  FutureEitherFailOr<List<SparePartEntity>> getSpareParts({
    SparePartCategory? category,
    SparePartStatus? status,
    List<CylinderType>? compatibleWith,
    int? page,
    int? limit,
  });

  FutureEitherFailOr<SparePartEntity> getSparePartById({
    required String sparePartId,
  });

  // Cylinder management
  FutureEitherFailOr<void> restockCylinder({
    required String cylinderId,
    required int quantity,
  });

  FutureEitherFailOr<void> adjustCylinderStock({
    required String cylinderId,
    required int adjustment,
    required String reason,
  });

  FutureEitherFailOr<void> bulkUpdateCylinderStatus({
    required List<String> cylinderIds,
    required CylinderStatus status,
  });

  FutureEitherFailOr<String> createCylinder({
    required CylinderType type,
    required CylinderMaterial material,
    required double price,
    required double cost,
    required String? imageUrl,
    required String description,
    required int quantity,
    required int minimumStockLevel,
    required CylinderStatus status,
  });

  FutureEitherFailOr<void> updateCylinder({
    required String cylinderId,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? quantity,
    int? minimumStockLevel,
    CylinderStatus? status,
  });

  FutureEitherFailOr<void> deleteCylinder({required String cylinderId});

  // Package
  FutureEitherFailOr<void> restockPackage({
    required String packageId,
    required int quantity,
  });

  FutureEitherFailOr<void> adjustPackageStock({
    required String packageId,
    required int adjustment,
    required String reason,
  });

  FutureEitherFailOr<void> bulkUpdatePackageStatus({
    required List<String> packageIds,
    required PackageStatus status,
  });

  FutureEitherFailOr<String> createPackage({
    required String name,
    required String description,
    required String cylinderId,
    required List<PackageItemEntity> includedSpareParts,
    required double totalPrice,
    required double costPrice,
    required double discount,
    required String? imageUrl,
    required int quantity,
    required int minimumStockLevel,
  });

  FutureEitherFailOr<void> updatePackage({
    required String packageId,
    String? name,
    String? description,
    String? cylinderId,
    List<PackageItemEntity>? includedSpareParts,
    double? totalPrice,
    double? costPrice,
    double? discount,
    String? imageUrl,
    int? quantity,
    int? minimumStockLevel,
  });

  FutureEitherFailOr<void> deletePackage({required String packageId});

  // Spare Part
  FutureEitherFailOr<void> restockSparePart({
    required String sparePartId,
    required int quantity,
  });

  FutureEitherFailOr<void> adjustSparePartStock({
    required String sparePartId,
    required int adjustment,
    required String reason,
  });

  FutureEitherFailOr<void> bulkUpdateSparePartStatus({
    required List<String> sparePartIds,
    required SparePartStatus status,
  });

  FutureEitherFailOr<String> createSparePart({
    required String name,
    required String description,
    required double price,
    required double cost,
    required SparePartCategory category,
    required List<CylinderType> compatibleCylinderTypes,
    required String barcode,
    required int minimumStockLevel,
  });

  FutureEitherFailOr<void> updateSparePart({
    required String sparePartId,
    String? name,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? minimumStockLevel,
  });

  FutureEitherFailOr<void> deleteSparePart({required String sparePartId});
}
