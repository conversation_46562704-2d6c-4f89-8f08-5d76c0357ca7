import 'package:frontend/core/enums/user_role.dart';
import 'package:frontend/core/enums/vehicle_type.dart';

import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/user_entity.dart';

class LocationModel extends LocationEntity {
  const LocationModel({required super.type, required super.coordinates});

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      type: json['type'] ?? 'Point',
      coordinates: List<double>.from(json['coordinates'] ?? [0.0, 0.0]),
    );
  }

  Map<String, dynamic> toJson() {
    return {'type': type, 'coordinates': coordinates};
  }
}

class AddressModel extends AddressEntity {
  const AddressModel({
    super.tag,
    required super.location,
    required super.details,
    super.contactPhone,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      tag: json['tag'],
      location: LocationModel.fromJson(json['location'] ?? {}),
      details: json['details'] ?? '',
      contactPhone: json['contactPhone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (tag != null) 'tag': tag,
      'location': (location as LocationModel).toJson(),
      'details': details,
      if (contactPhone != null) 'contactPhone': contactPhone,
    };
  }
}

class VehicleModel extends VehicleEntity {
  const VehicleModel({required super.type, super.number});

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      type: VehicleType.fromString(json['type'] ?? 'motorcycle'),
      number: json['number'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'type': type.value, if (number != null) 'number': number};
  }
}

class AgentMetadataModel extends AgentMetadataEntity {
  const AgentMetadataModel({
    super.vehicle,
    super.isOnDuty = false,
    super.lastKnownLocation,
    super.rating,
  });

  factory AgentMetadataModel.fromJson(Map<String, dynamic> json) {
    return AgentMetadataModel(
      vehicle: json['vehicle'] != null
          ? VehicleModel.fromJson(json['vehicle'])
          : null,
      isOnDuty: json['isOnDuty'] ?? false,
      lastKnownLocation: json['lastKnownLocation'] != null
          ? LocationModel.fromJson(json['lastKnownLocation'])
          : null,
      rating: json['rating']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (vehicle != null) 'vehicle': (vehicle as VehicleModel).toJson(),
      'isOnDuty': isOnDuty,
      if (lastKnownLocation != null)
        'lastKnownLocation': (lastKnownLocation as LocationModel).toJson(),
      if (rating != null) 'rating': rating,
    };
  }
}

class UserModel extends UserEntity {
  final String token;

  const UserModel({
    required super.id,
    required super.phone,
    super.email,
    required super.role,
    super.username,
    super.addresses = const [],
    super.isActive = true,
    super.agentMetadata,
    super.createdAt,
    super.updatedAt,
    required this.token,
  });

  @override
  List<Object?> get props => [
    id,
    phone,
    email,
    role,
    addresses,
    isActive,
    agentMetadata,
    createdAt,
    updatedAt,
  ];

  factory UserModel.fromJson(Map<String, dynamic> json) {
    try {
      return UserModel(
        id: json['_id'] ?? json['id'] ?? '',
        phone: json['phone'] ?? '',
        email: json['email'] ?? '',
        token: json['token'] ?? '',
        username: json['username'] ?? '',
        role: UserRole.fromString(json['role'] ?? 'customer'),
        addresses:
            (json['addresses'] as List<dynamic>?)
                ?.map((address) => AddressModel.fromJson(address))
                .toList() ??
            [],
        isActive: json['isActive'] ?? true,
        agentMetadata: json['agentMetadata'] != null
            ? AgentMetadataModel.fromJson(json['agentMetadata'])
            : null,
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: ' Failed to parse UserModel : ${error.toString()} ',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: UserModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'phone': phone,
      if (email != null) 'email': email,
      'role': role.name,
      'addresses': addresses
          .map((address) => (address as AddressModel).toJson())
          .toList(),
      'isActive': isActive,
      if (agentMetadata != null)
        'agentMetadata': (agentMetadata as AgentMetadataModel).toJson(),
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  static List<UserModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => UserModel.fromJson(json)).toList();
  }
}
