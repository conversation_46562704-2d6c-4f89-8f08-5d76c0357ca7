# Inventory Management API Documentation

This document provides comprehensive documentation for the inventory management APIs including Spare Parts, Packages, and Cylinders.

## 🔄 **Inventory Management Philosophy**

**Automated Order-Driven Operations:**

- Inventory reservation, release, and sales tracking are **automatically handled** through the order lifecycle
- This ensures data consistency, proper audit trails, and prevents manual errors

**Manual Administrative Operations:**

- Direct inventory management is limited to legitimate administrative use cases
- Includes restocking, stock adjustments, analytics, and monitoring operations

## Authentication

All endpoints require authentication via Bearer token:

```
Authorization: Bearer <your-jwt-token>
```

## Base URL

```
http://localhost:3010/api/v1
```

## Response Format

All responses follow this structure:

```json
{
  "status": "success" | "fail" | "error",
  "message": "Response message",
  "data": {}, // Response data
  "meta": {}, // Additional metadata (pagination, counts, etc.)
  "errors": {} // Error details (only on error responses)
}
```

---

## 🔧 Spare Parts API

### Create Spare Part

**POST** `/spare-parts`

- **Access**: Admin only
- **Body**:

```json
{
  "name": "Regulator Valve",
  "description": "High-pressure gas regulator valve",
  "price": 25.99,
  "cost": 15.5,
  "category": "REGULATOR",
  "compatibleCylinderTypes": ["SixKg", "ThirteenKg"],
  "barcode": "SP001234",
  "minimumStockLevel": 10,
  "imageUrl": "https://example.com/image.jpg"
}
```

### Get All Spare Parts

**GET** `/spare-parts`

- **Access**: All authenticated users
- **Query Parameters**:
  - `search` (string): Text search
  - `category` (string): Filter by category
  - `status` (string): Filter by status
  - `compatibleWith` (string): Filter by compatible cylinder type
  - `lowStock` (boolean): Show only low stock items
  - `page` (number): Page number (default: 1)
  - `limit` (number): Items per page (default: 10, max: 50)

### Get Spare Part by ID

**GET** `/spare-parts/:id`

- **Access**: All authenticated users

### Update Spare Part

**PUT** `/spare-parts/:id`

- **Access**: Admin only
- **Body**: Same as create (all fields optional)

### Inventory Operations

#### Restock Spare Part

**POST** `/spare-parts/:id/restock`

- **Access**: Admin only
- **Body**:

```json
{
  "quantity": 50
}
```

#### Reserve Spare Part

**POST** `/spare-parts/:id/reserve`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 5
}
```

#### Release Reservation

**POST** `/spare-parts/:id/release`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 3
}
```

#### Mark as Sold

**POST** `/spare-parts/:id/sell`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 2
}
```

#### Discontinue Spare Part

**PUT** `/spare-parts/:id/discontinue`

- **Access**: Admin only

### Analytics & Reports

#### Get Low Stock Items

**GET** `/spare-parts/low-stock`

- **Access**: Admin, Agent

#### Get Sales Statistics

**GET** `/spare-parts/statistics`

- **Access**: Admin only

#### Search Spare Parts

**GET** `/spare-parts/search?q=regulator&limit=10`

- **Access**: All authenticated users

---

## 📦 Packages API

### Create Package

**POST** `/packages`

- **Access**: Admin only
- **Body**:

```json
{
  "name": "Standard Gas Kit",
  "description": "Complete gas cylinder package with accessories",
  "cylinder": "60f7b3b4e1234567890abcde",
  "includedSpareParts": [
    {
      "part": "60f7b3b4e1234567890abcdf",
      "quantity": 1
    },
    {
      "part": "60f7b3b4e1234567890abce0",
      "quantity": 2
    }
  ],
  "discount": 10,
  "imageUrl": "https://example.com/package.jpg",
  "quantity": 25,
  "minimumStockLevel": 5
}
```

### Get All Packages

**GET** `/packages`

- **Access**: All authenticated users
- **Query Parameters**:
  - `search` (string): Text search
  - `cylinder` (string): Filter by cylinder ID
  - `isActive` (boolean): Filter by active status
  - `minPrice` (number): Minimum price filter
  - `maxPrice` (number): Maximum price filter
  - `page` (number): Page number
  - `limit` (number): Items per page
  - `populate` (boolean): Include related data (default: true)

### Get Package by ID

**GET** `/packages/:id`

- **Access**: All authenticated users
- **Query**: `populate=true` (optional)

### Update Package

**PUT** `/packages/:id`

- **Access**: Admin only

### Package Operations

#### Toggle Package Status

**PUT** `/packages/:id/toggle-status`

- **Access**: Admin only

#### Check Availability

**GET** `/packages/:id/availability?quantity=5`

- **Access**: All authenticated users

#### Get Available Quantity

**GET** `/packages/:id/available-quantity`

- **Access**: All authenticated users

### Inventory Operations

#### Reserve Package

**POST** `/packages/:id/reserve`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 3,
  "orderId": "ORD-12345"
}
```

#### Release Reservation

**POST** `/packages/:id/release`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 2,
  "orderId": "ORD-12345"
}
```

#### Mark as Sold

**POST** `/packages/:id/sell`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 1
}
```

#### Restock Package

**POST** `/packages/:id/restock`

- **Access**: Admin only
- **Body**:

```json
{
  "quantity": 20
}
```

#### Adjust Stock

**POST** `/packages/:id/adjust-stock`

- **Access**: Admin only
- **Body**:

```json
{
  "adjustment": -5,
  "reason": "Damaged during transport"
}
```

### Analytics & Reports

#### Get Package Analytics

**GET** `/packages/analytics`

- **Access**: Admin only

#### Get Low Stock Packages

**GET** `/packages/low-stock`

- **Access**: Admin, Agent

#### Get Sales Statistics

**GET** `/packages/sales-statistics`

- **Access**: Admin only

#### Bulk Update Status

**PUT** `/packages/bulk-status`

- **Access**: Admin only
- **Body**:

```json
{
  "packageIds": ["60f7b3b4e1234567890abcde", "60f7b3b4e1234567890abcdf"],
  "isActive": false
}
```

---

## 🛢️ Cylinders API

### Create Cylinder

**POST** `/cylinders`

- **Access**: Admin only
- **Body**:

```json
{
  "type": "SixKg",
  "material": "Iron",
  "price": 45.99,
  "cost": 30.0,
  "imageUrl": "https://example.com/cylinder.jpg",
  "description": "6KG Iron Gas Cylinder",
  "quantity": 100,
  "minimumStockLevel": 20,
  "status": "Active"
}
```

### Get All Cylinders

**GET** `/cylinders`

- **Access**: All authenticated users
- **Query Parameters**:
  - `type` (string): Filter by cylinder type
  - `material` (string): Filter by material
  - `status` (string): Filter by status
  - `lowStockOnly` (boolean): Show only low stock items
  - `page` (number): Page number
  - `limit` (number): Items per page

### Get Cylinder by ID

**GET** `/cylinders/:id`

- **Access**: All authenticated users

### Update Cylinder

**PUT** `/cylinders/:id`

- **Access**: Admin only

### Delete Cylinder

**DELETE** `/cylinders/:id`

- **Access**: Admin only

### Check Availability

**GET** `/cylinders/:id/availability?quantity=10`

- **Access**: All authenticated users

### Inventory Operations

#### Reserve Cylinder

**POST** `/cylinders/:id/reserve`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 5
}
```

#### Release Reservation

**POST** `/cylinders/:id/release`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 3
}
```

#### Mark as Sold

**POST** `/cylinders/:id/sell`

- **Access**: Admin, Agent
- **Body**:

```json
{
  "quantity": 2
}
```

#### Restock Cylinder

**POST** `/cylinders/:id/restock`

- **Access**: Admin only
- **Body**:

```json
{
  "quantity": 50
}
```

### Analytics & Reports

#### Get Low Stock Cylinders

**GET** `/cylinders/low-stock`

- **Access**: Admin, Agent

#### Get Sales Statistics

**GET** `/cylinders/statistics`

- **Access**: Admin only

#### Bulk Update Status

**PUT** `/cylinders/bulk-status`

- **Access**: Admin only
- **Body**:

```json
{
  "cylinderIds": ["60f7b3b4e1234567890abcde", "60f7b3b4e1234567890abcdf"],
  "status": "Discontinued"
}
```

---

## Error Codes

| Code                      | Description                                 |
| ------------------------- | ------------------------------------------- |
| `INVALID_QUANTITY`        | Quantity must be positive                   |
| `INSUFFICIENT_STOCK`      | Not enough stock available                  |
| `CYLINDER_NOT_FOUND`      | Cylinder not found                          |
| `PACKAGE_NOT_FOUND`       | Package not found                           |
| `SPARE_PART_NOT_FOUND`    | Spare part not found                        |
| `DUPLICATE_RESOURCE`      | Resource already exists                     |
| `CONCURRENT_MODIFICATION` | Stock changed during operation              |
| `ACTIVE_RESERVATIONS`     | Cannot modify item with active reservations |

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error
