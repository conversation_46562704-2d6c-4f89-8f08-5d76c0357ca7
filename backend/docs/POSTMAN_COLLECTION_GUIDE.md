# Gas Delivery System - Postman Collection Guide

This guide provides comprehensive instructions for using the Postman collection for the Gas Delivery System Inventory Management API.

## 📁 Collection Overview

The **Gas Delivery Inventory API** Postman collection includes:

- **🔧 Spare Parts Management** - 15 endpoints for spare parts operations
- **📦 Package Management** - 18 endpoints for package operations  
- **🛢️ Cylinder Management** - 12 endpoints for cylinder operations
- **🔄 Order Management** - 6 endpoints for automated inventory through orders
- **📊 System Information** - 2 utility endpoints

**Total: 53 endpoints** covering all inventory management functionality.

## 🚀 Quick Start

### 1. Import the Collection
1. Open Postman
2. Click **Import** button
3. Select the `Gas_Delivery_Inventory_API.postman_collection.json` file
4. Click **Import**

### 2. Set Up Environment Variables
The collection uses the following variables that you should configure:

```json
{
  "baseUrl": "http://localhost:3000/api/v1",
  "authToken": "your-jwt-token-here",
  "cylinderId": "60f7b3b4e1234567890abcde",
  "packageId": "60f7b3b4e1234567890abcdf", 
  "sparePartId": "60f7b3b4e1234567890abce0",
  "orderId": "60f7b3b4e1234567890abce1",
  "customerId": "60f7b3b4e1234567890abce2"
}
```

### 3. Authentication Setup
1. The collection is pre-configured with **Bearer Token** authentication
2. Set your JWT token in the `authToken` variable
3. All requests will automatically use this token

## 🔐 Authentication & Authorization

### Getting an Auth Token
First, authenticate to get a JWT token:

```bash
POST {{baseUrl}}/auth/login
{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

Copy the token from the response and set it as the `authToken` variable.

### Role-Based Access
- **👑 Admin**: Full access to all endpoints
- **🚚 Agent**: Limited access (no create/update/delete operations)
- **👤 Customer**: Read-only access to own data

## 📋 Collection Structure

### 🔧 **Spare Parts Management**
```
├── CRUD Operations
│   ├── Create Spare Part (Admin)
│   ├── Get All Spare Parts (All)
│   ├── Get Spare Part by ID (All)
│   ├── Update Spare Part (Admin)
│   └── Search Spare Parts (All)
├── Administrative Operations
│   ├── Restock Spare Part (Admin)
│   └── Discontinue Spare Part (Admin)
└── Analytics & Reports
    ├── Get Low Stock Spare Parts (Admin/Agent)
    └── Get Spare Parts Statistics (Admin)
```

### 📦 **Package Management**
```
├── CRUD Operations
│   ├── Create Package (Admin)
│   ├── Get All Packages (All)
│   ├── Get Package by ID (All)
│   ├── Update Package (Admin)
│   └── Toggle Package Status (Admin)
├── Availability Checks
│   ├── Check Package Availability (All)
│   └── Get Available Quantity (All)
├── Administrative Operations
│   ├── Restock Package (Admin)
│   ├── Adjust Package Stock (Admin)
│   └── Bulk Update Package Status (Admin)
└── Analytics & Reports
    ├── Get Package Analytics (Admin)
    ├── Get Low Stock Packages (Admin/Agent)
    └── Get Package Sales Statistics (Admin)
```

### 🛢️ **Cylinder Management**
```
├── CRUD Operations
│   ├── Create Cylinder (Admin)
│   ├── Get All Cylinders (All)
│   ├── Get Cylinder by ID (All)
│   ├── Update Cylinder (Admin)
│   ├── Delete Cylinder (Admin)
│   └── Check Cylinder Availability (All)
├── Administrative Operations
│   ├── Restock Cylinder (Admin)
│   └── Bulk Update Cylinder Status (Admin)
└── Analytics & Reports
    ├── Get Low Stock Cylinders (Admin/Agent)
    └── Get Cylinder Statistics (Admin)
```

### 🔄 **Order Management (Automated Inventory)**
```
├── Order Lifecycle
│   ├── Create Order (Auto-Reserve Inventory) (All)
│   ├── Get Orders (All)
│   ├── Assign Agent to Order (Admin)
│   ├── Cancel Order (Auto-Release Inventory) (Customer/Admin/Agent)
│   └── Complete Order (Auto-Mark as Sold) (Admin/Agent)
└── Order Utilities
    └── Validate QR Code (Agent/Admin)
```

## 🔄 **Inventory Management Philosophy**

### ✅ **Automated Operations (Through Orders)**
These operations are **automatically handled** through the order lifecycle:

- **Inventory Reservation** → Happens on order creation
- **Inventory Release** → Happens on order cancellation  
- **Sales Tracking** → Happens on order completion

### 🛠️ **Manual Operations (Administrative)**
These operations are available for **administrative purposes**:

- **Restocking** → Adding new inventory
- **Stock Adjustments** → Handling damage, loss, corrections
- **Analytics** → Business intelligence and reporting
- **Availability Checks** → Real-time stock validation

## 📊 **Testing Workflows**

### 1. **Complete Order Workflow**
Test the full order lifecycle with automatic inventory management:

```
1. Create Spare Part → Create Package → Create Cylinder
2. Restock all items to have sufficient inventory
3. Create Order (auto-reserves inventory)
4. Assign Agent to Order
5. Complete Order (auto-marks as sold)
6. Check analytics to see updated sales statistics
```

### 2. **Inventory Management Workflow**
Test administrative inventory operations:

```
1. Check Low Stock alerts
2. Restock items that are low
3. Adjust stock for damaged items
4. View analytics and statistics
5. Bulk update statuses as needed
```

### 3. **Error Handling Workflow**
Test error scenarios:

```
1. Try to create order with insufficient inventory
2. Try to cancel already completed order
3. Try to access admin endpoints as customer
4. Try to update non-existent items
```

## 🧪 **Automated Testing Features**

The collection includes **automatic test scripts** that:

- ✅ Verify response times (< 5 seconds)
- ✅ Check content types are JSON
- ✅ Extract IDs from responses for subsequent requests
- ✅ Set global variables automatically

### Running Tests
1. Select the collection or folder
2. Click **Run** to open Collection Runner
3. Configure iterations and environment
4. Click **Run** to execute all tests

## 🔧 **Environment Configuration**

### Development Environment
```json
{
  "baseUrl": "http://localhost:3000/api/v1",
  "authToken": "dev-jwt-token"
}
```

### Staging Environment  
```json
{
  "baseUrl": "https://staging-api.gasdelivery.com/api/v1",
  "authToken": "staging-jwt-token"
}
```

### Production Environment
```json
{
  "baseUrl": "https://api.gasdelivery.com/api/v1", 
  "authToken": "production-jwt-token"
}
```

## 📝 **Sample Data**

### Creating Test Data
Use these sample requests to create test data:

**Spare Part:**
```json
{
  "name": "Regulator Valve",
  "price": 25.99,
  "cost": 15.50,
  "category": "REGULATOR",
  "minimumStockLevel": 10
}
```

**Cylinder:**
```json
{
  "type": "SixKg",
  "material": "Iron", 
  "price": 45.99,
  "cost": 30.00,
  "quantity": 100,
  "minimumStockLevel": 20
}
```

**Package:**
```json
{
  "name": "Standard Gas Kit",
  "cylinder": "{{cylinderId}}",
  "includedSpareParts": [
    {
      "part": "{{sparePartId}}",
      "quantity": 1
    }
  ],
  "quantity": 25,
  "minimumStockLevel": 5
}
```

## 🚨 **Important Notes**

### ⚠️ **Removed Operations**
The following operations have been **removed** and are now handled automatically:
- Direct inventory reservation endpoints
- Direct inventory release endpoints  
- Direct sales marking endpoints

### ✅ **Best Practices**
1. **Always check availability** before creating orders
2. **Use order lifecycle** for inventory movements
3. **Use administrative endpoints** only for warehouse operations
4. **Monitor low stock alerts** regularly
5. **Review analytics** for business insights

### 🔒 **Security Considerations**
- Never share JWT tokens
- Use environment variables for sensitive data
- Regularly rotate authentication tokens
- Test with appropriate user roles

This Postman collection provides comprehensive testing capabilities for the entire Gas Delivery System inventory management functionality while reflecting the current architecture of automated order-driven inventory operations.
