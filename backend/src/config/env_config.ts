import dotenv from 'dotenv';
import path from 'path';

// Load the appropriate .env file based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.development';

dotenv.config({ path: path.resolve(process.cwd(), envFile) });

function requireEnv(varName: any) {
  const value = process.env[varName];
  if (!value) {
    throw new Error(`❌ Missing required environment variable: ${varName}`);
  }
  return value;
}

const server = {
  env: process.env.NODE_ENV ?? 'development',
  port: parseInt(process.env.PORT || '3000'),
  url: requireEnv('SERVER_URL'),
  saltRounds: parseInt(process.env.SALT_ROUND || '10'),
  jwtSecret: requireEnv('JWT_SECRET'),
  qrSecretKey: requireEnv('QR_SECRET_KEY'),
  databaseUrl: requireEnv('DATABASE_URL'),
  disableOtpVerification: process.env.DISABLE_OTP_VERIFICATION === 'true',
};

// 🔥 Firebase
const firebase = {
  projectId: requireEnv('FIREBASE_PROJECT_ID'),
  privateKey: requireEnv('FIREBASE_PRIVATE_KEY').replace(/\\n/g, '\n'),
  clientEmail: requireEnv('FIREBASE_CLIENT_EMAIL'),
};

// 💳 Payment - Hodan
//  const hodan = {
//   merchantUid: requireEnv('HODAN_MERCHANT_UID'),
//   apiUserId: requireEnv('HODAN_MERCHANT_API_USER_ID'),
//   apiKey: requireEnv('HODAN_MERCHANT_API_KEY'),
//   apiUrl: requireEnv('HODAN_MERCHANT_API_URL'),
// };

// 💳 Payment - Rasiin
const payment = {
  rasiin: {
    merchantUid: requireEnv('RASIIN_MERCHANT_UID'),
    apiUserId: requireEnv('RASIIN_MERCHANT_API_USER_ID'),
    apiKey: requireEnv('RASIIN_MERCHANT_API_KEY'),
    apiUrl: requireEnv('RASIIN_MERCHANT_API_URL'),
  },
};

// 📩 SMS Provider
const sms = {
  username: requireEnv('SMS_USERNAME'),
  password: requireEnv('SMS_PASSWORD'),
  providerUrl: requireEnv('SMS_PROVIDER_URL'),
  senderId: requireEnv('SMS_SENDER_ID'),
  timeout: parseInt(process.env.SMS_TIMEOUT || '30000'),
};

export const config = {
  server,
  firebase,
  // hodan, // uncomment when needed
  payment,
  sms,
} as const; // Validate QR payload using JWT
