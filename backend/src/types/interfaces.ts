import { Document, Schema } from 'mongoose';
import { JwtPayload } from 'jsonwebtoken';
import {
  UserRole,
  OrderStatus,
  CylinderType,
  PaymentMethod,
  DeliveryVerificationMethod,
  CylinderMaterial,
  // Warehouse,
  VehicleType,
  NotificationStatus,
} from '../enums/enums';
import { NotificationTopic } from '../utils/notification_utils';

// GeoJSON Location (for addresses/delivery)
export interface ILocation {
  type: 'Point';
  coordinates: [number, number]; // [longitude, latitude]
}

// Address Sub-Document
export interface IAddress {
  tag?: 'home' | 'work' | 'other';
  location: ILocation;
  details: string;
  contactPhone?: string;
}

// Agent Metadata (Extended User)
export interface IAgent extends Document {
  userId: Schema.Types.ObjectId;
  vehicle: {
    type: VehicleType;
    number: string;
  };
  isOnDuty: boolean;
  lastKnownLocation?: ILocation;
  rating?: number;
}

export interface IOtp {
  code: string;
  expirationTime: Date;
}

export interface INotificationSettings {
  fcmToken: string;
  topics: NotificationTopic[];
  isEnabled: boolean;
}

// User Base Document
export interface IUser extends Document {
  phone: string;
  email?: string;
  role: UserRole;
  passwordHash: string;
  addresses: IAddress[];
  isActive: boolean;
  agentMetadata?: IAgent;
  otp?: IOtp;
  notification?: INotificationSettings;
  createdAt: Date;
  updatedAt: Date;
}

// Order Document
export interface IOrder extends Document {
  customerId: Schema.Types.ObjectId;
  cylinderType: CylinderType;
  quantity: number;
  deliveryAddress: IAddress;
  status: OrderStatus;
  assignedAgentId?: Schema.Types.ObjectId;
  scheduledSlot: { start: Date; end: Date }; // Time window
  payment: {
    method: PaymentMethod;
    amount: number;
    transactionId?: string;
    isPaid: boolean;
  };
  verification: {
    method: DeliveryVerificationMethod;
    code?: string; // OTP or QR data
    isVerified: boolean;
  };
  createdAt: Date;
}

export interface TokenPayload extends JwtPayload {
  userId: string | Schema.Types.ObjectId;
  // role: 'USER' | 'PROVIDER';
  role: UserRole;
}

export interface QRTokenPayload {
  orderId: string;
  expiresIn?: string | number;
}

export interface ITempOtp extends Document {
  phone: string;
  code: string;
  expirationTime: Date;
  createdAt: Date;
}

export interface INotification extends Document {
  userId: Schema.Types.ObjectId;
  title: string;
  body: string;
  data: { [key: string]: string };
  imageUrl: string;
  topic?: NotificationTopic;
  status: NotificationStatus;
  deliveredAt?: Date;
  readAt?: Date;
  error?: string;
  result?: any;
}
