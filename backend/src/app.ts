import express, { Request, Response, NextFunction } from 'express';
import { json, urlencoded } from 'express';
import helmet from 'helmet';
import { globalErrorHandler } from './middleware/error_handler_middle_ware';

// import cors from "cors";
import { sendResponse } from './utils/response';
import appRouter from './routes';
import { NotFoundError } from './errors/app_errors';
import { loggerInterceptor } from './middleware/logger_interceptor_middleware';

const app = express();

// middleware
app.use(json());
app.use(urlencoded({ extended: true }));

// security
app.use(helmet());
// app.use(cors());
// app.options("*", cors());

/// Logger Interceptor for request and response
app.use(loggerInterceptor);

// routes
app.use(appRouter);

// test api
app.get('/', (req: Request, res: Response) => {
  sendResponse(res, 200, 'success', 'Welcome to Gas System Project Backend');
  return;
});
// /health
app.get('/health', (req: Request, res: Response) => {
  sendResponse(res, 200, 'success', 'Service is up and running');
  return;
});

// 404 handler
app.use((req: Request, res: Response, next: NextFunction) => {
  next(new NotFoundError(`Route not found: ${req.method} ${req.originalUrl}`));
});

// error handler
app.use(globalErrorHandler);

export default app;
