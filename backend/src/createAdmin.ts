import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import { User } from './models/user.model';
import { UserRole } from './enums/enums';
import { config } from './config/env_config';
import { generateToken } from './utils/jwt_utils';

/// npx ts-node src/createAdmin.ts

async function createAdmin() {
  try {
    // Connect to database using the configured URL
    await mongoose.connect(config.server.databaseUrl);
    console.log('Connected to database');

    // Create admin user
    const admin = await User.create({
      phone: '+252619597744',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('123123', 10),
      role: UserRole.AGENT,
      isActive: true,
    });

    const token = generateToken(admin._id.toString(), admin.role);

    console.log('Admin created:', {
      adminId: admin._id,
      token,
    });
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin:', error);
    process.exit(1);
  }
}

// Execute the function
createAdmin();
