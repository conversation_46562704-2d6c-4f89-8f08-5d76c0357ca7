import { Request, Response, NextFunction } from 'express';
import { dashboardService } from '../services';
import { sendResponse } from '../utils/response';
import { UserRole } from '../enums/enums';
import logger from '../config/logger';
import { BadRequestError, NotFoundError, ForbiddenError } from '../errors/app_errors';

/**
 * Dashboard controller handling admin, agent, and user dashboard endpoints
 */
class DashboardController {
  /**
   * Get admin dashboard data
   * @route GET /api/v1/dashboard/admin
   * @access Admin only
   */
  async getAdminDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      // Check if user is admin
      if (req.user?.role !== UserRole.ADMIN) {
        throw new ForbiddenError('Access denied. Admin role required.');
      }

      // Parse date range from query parameters
      const { startDate, endDate } = req.query;
      let dateRange: { startDate: Date; endDate: Date } | undefined;

      if (startDate && endDate) {
        dateRange = {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
        };

        // Validate date range
        if (dateRange.startDate > dateRange.endDate) {
          throw new BadRequestError('Start date cannot be after end date');
        }
      }

      const dashboardData = await dashboardService.getAdminDashboard(dateRange);

      logger.info('Admin dashboard data retrieved successfully', {
        adminId: req.user.userId,
        dateRange,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Admin dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      logger.error('Failed to retrieve admin dashboard data', {
        error: error.message,
        adminId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });
      next(error);
    }
  }

  /**
   * Get supervisor dashboard data with restricted access
   * @route GET /api/v1/dashboard/supervisor
   * @access Supervisor only
   */
  async getSupervisorDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      // Check if user is supervisor
      if (req.user?.role !== UserRole.SUPERVISOR) {
        throw new ForbiddenError('Access denied. Supervisor role required.');
      }
      const dashboardData = await dashboardService.getSupervisorDashboard();

      logger.info('Supervisor dashboard data retrieved successfully', {
        supervisorId: req.user.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Supervisor dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      logger.error('Failed to retrieve supervisor dashboard data', {
        error: error.message,
        supervisorId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });
      next(error);
    }
  }

  /**
   * Get agent dashboard data
   * @route GET /api/v1/dashboard/agent/:agentId?
   * @access Agent (own data) or Admin (any agent data)
   */
  async getAgentDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const requestedAgentId = (req.params.agentId || req.user?.userId)?.toString();

      if (!requestedAgentId) {
        throw new BadRequestError('Agent ID is required');
      }

      // Authorization check
      if (req.user?.role === UserRole.AGENT && req.user.userId !== requestedAgentId) {
        throw new ForbiddenError('Agents can only access their own dashboard data');
      } else if (req.user?.role !== UserRole.AGENT && req.user?.role !== UserRole.ADMIN) {
        throw new ForbiddenError('Access denied. Agent or Admin role required.');
      }

      // Parse date range from query parameters
      const { startDate, endDate } = req.query;
      let dateRange: { startDate: Date; endDate: Date } | undefined;

      if (startDate && endDate) {
        dateRange = {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
        };

        // Validate date range
        if (dateRange.startDate > dateRange.endDate) {
          throw new BadRequestError('Start date cannot be after end date');
        }
      }

      const dashboardData = await dashboardService.getAgentDashboard(requestedAgentId, dateRange);

      logger.info('Agent dashboard data retrieved successfully', {
        requesterId: req.user?.userId,
        agentId: requestedAgentId,
        dateRange,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Agent dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      logger.error('Failed to retrieve agent dashboard data', {
        error: error.message,
        requesterId: req.user?.userId,
        agentId: req.params.agentId,
        timestamp: new Date().toISOString(),
      });
      next(error);
    }
  }

  /**
   * Get user/customer dashboard data
   * @route GET /api/v1/dashboard/user/:userId?
   * @access Customer (own data) or Admin (any user data)
   */
  async getUserDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const requestedUserId = (req.params.userId || req.user?.userId)?.toString();

      if (!requestedUserId) {
        throw new BadRequestError('User ID is required');
      }

      // Authorization check
      if (req.user?.role === UserRole.CUSTOMER && req.user.userId !== requestedUserId) {
        throw new ForbiddenError('Customers can only access their own dashboard data');
      } else if (req.user?.role !== UserRole.CUSTOMER && req.user?.role !== UserRole.ADMIN) {
        throw new ForbiddenError('Access denied. Customer or Admin role required.');
      }

      // Parse date range from query parameters
      const { startDate, endDate } = req.query;
      let dateRange: { startDate: Date; endDate: Date } | undefined;

      if (startDate && endDate) {
        dateRange = {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
        };

        // Validate date range
        if (dateRange.startDate > dateRange.endDate) {
          throw new BadRequestError('Start date cannot be after end date');
        }
      }

      const dashboardData = await dashboardService.getUserDashboard(requestedUserId, dateRange);

      logger.info('User dashboard data retrieved successfully', {
        requesterId: req.user?.userId,
        userId: requestedUserId,
        dateRange,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'User dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      logger.error('Failed to retrieve user dashboard data', {
        error: error.message,
        requesterId: req.user?.userId,
        userId: req.params.userId,
        timestamp: new Date().toISOString(),
      });
      next(error);
    }
  }

  /**
   * Get dashboard summary for current user based on their role
   * @route GET /api/v1/dashboard/summary
   * @access Authenticated users
   */
  async getDashboardSummary(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.userId || !req.user?.role) {
        throw new BadRequestError('User authentication required');
      }

      let dashboardData;
      const userId = req.user.userId.toString();
      const userRole = req.user.role;

      // Get dashboard data based on user role
      switch (userRole) {
        case UserRole.ADMIN:
          dashboardData = await dashboardService.getAdminDashboard();
          break;
        case UserRole.SUPERVISOR:
          dashboardData = await dashboardService.getSupervisorDashboard();
          break;
        case UserRole.AGENT:
          dashboardData = await dashboardService.getAgentDashboard(userId);
          break;
        case UserRole.CUSTOMER:
          dashboardData = await dashboardService.getUserDashboard(userId);
          break;
        default:
          throw new BadRequestError('Invalid user role');
      }

      logger.info('Dashboard summary retrieved successfully', {
        userId,
        userRole,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Dashboard summary retrieved successfully', {
        data: {
          userRole,
          dashboard: dashboardData,
        },
      });
    } catch (error) {
      logger.error('Failed to retrieve dashboard summary', {
        error: error.message,
        userId: req.user?.userId,
        userRole: req.user?.role,
        timestamp: new Date().toISOString(),
      });
      next(error);
    }
  }

  /**
   * Get dashboard reports (admin only)
   * @route GET /api/v1/dashboard/reports
   * @access Admin only
   */
  async getDashboardReports(req: Request, res: Response, next: NextFunction) {
    try {
      // Check if user is admin
      if (req.user?.role !== UserRole.ADMIN) {
        throw new ForbiddenError('Access denied. Admin role required.');
      }

      const { reportType, startDate, endDate } = req.query;

      if (!reportType) {
        throw new BadRequestError('Report type is required');
      }

      // Parse date range
      let dateRange: { startDate: Date; endDate: Date } | undefined;
      if (startDate && endDate) {
        dateRange = {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
        };

        if (dateRange.startDate > dateRange.endDate) {
          throw new BadRequestError('Start date cannot be after end date');
        }
      }

      // Get specific report data based on type
      let reportData;
      switch (reportType) {
        case 'orders':
          reportData = await dashboardService.getAdminDashboard(dateRange);
          reportData = {
            orderStats: reportData.orderStats,
            recentOrders: reportData.recentOrders,
          };
          break;
        case 'revenue':
          reportData = await dashboardService.getAdminDashboard(dateRange);
          reportData = {
            revenueStats: reportData.revenueStats,
            paymentStats: reportData.paymentStats,
          };
          break;
        case 'inventory':
          reportData = await dashboardService.getAdminDashboard(dateRange);
          reportData = {
            inventoryStats: reportData.inventoryStats,
            topProducts: reportData.topProducts,
          };
          break;
        case 'agents':
          reportData = await dashboardService.getAdminDashboard(dateRange);
          reportData = {
            agentPerformance: reportData.agentPerformance,
            userStats: reportData.userStats,
          };
          break;
        default:
          throw new BadRequestError(
            'Invalid report type. Available types: orders, revenue, inventory, agents'
          );
      }

      logger.info('Dashboard report generated successfully', {
        adminId: req.user.userId,
        reportType,
        dateRange,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', `${reportType} report generated successfully`, {
        data: {
          reportType,
          dateRange,
          report: reportData,
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to generate dashboard report', {
        error: error.message,
        adminId: req.user?.userId,
        reportType: req.query.reportType,
        timestamp: new Date().toISOString(),
      });
      next(error);
    }
  }
}

export const dashboardController = new DashboardController();
