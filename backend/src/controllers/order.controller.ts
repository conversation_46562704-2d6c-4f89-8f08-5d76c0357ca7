import { Request, Response, NextFunction } from 'express';
import { orderService } from '../services/order.services';
import { sendResponse } from '../utils/response';
import { UserRole, OrderStatus, PaymentMethod } from '../enums/enums';
import logger from '../config/logger';
import { BadRequestError } from '../errors/app_errors';

class OrderController {
  /**
   * Create a new order
   */
  async createOrder(req: Request, res: Response, next: NextFunction) {
    try {
      const { items, deliveryAddress, paymentMethod, customerId: bodyCustomerId } = req.body;

      // Priority: body.customerId > req.user.userId
      const customerId = bodyCustomerId || req.user?.userId;

      if (!customerId) {
        throw new BadRequestError(
          'Customer ID is required. Either provide it in the request body or ensure you are authenticated.'
        );
      }

      const result = await orderService.createOrder(
        customerId.toString(), // Ensure string conversion
        items,
        deliveryAddress,
        paymentMethod
      );

      logger.info('Order created successfully', {
        orderId: result._id,
        customerId,
        items,
        deliveryAddress,
        paymentMethod,
      });

      sendResponse(res, 201, 'success', 'Order created successfully', {
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all orders
   */
  async getOrders(req: Request, res: Response, next: NextFunction) {
    try {
      const filters = {
        customer: req.query.customer?.toString(),
        status: req.query.status as OrderStatus,
        paymentMethod: req.query.paymentMethod as PaymentMethod,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      };

      const requestingUser = {
        userId: req.user?.userId.toString(),
        role: req.user?.role,
      };

      const orders = await orderService.getOrders(filters, requestingUser);

      sendResponse(res, 200, 'success', 'Orders retrieved successfully', {
        data: orders,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Assign agent to order
   */
  async assignAgentToOrder(req: Request, res: Response, next: NextFunction) {
    try {
      const orderId = req.params.id;
      const agentId = req.body.agentId;
      if (!agentId) {
        throw new BadRequestError('Agent ID is required');
      }
      if (!orderId) {
        throw new BadRequestError('Order ID is required');
      }
      const result = await orderService.assignAgentToOrder(orderId, agentId);
      sendResponse(res, 200, 'success', 'Agent assigned to order successfully', {
        data: result,
        meta: {
          orderId,
          agentId,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(req: Request, res: Response, next: NextFunction) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        throw new BadRequestError('Order ID is required');
      }
      const result = await orderService.cancelOrder(orderId);
      sendResponse(res, 200, 'success', 'Order cancelled successfully', {
        data: result,
        meta: {
          orderId,
        },
      });
    } catch (error) {
      next(error);
      logger.error('Order cancellation failed', {
        orderId: req.params.id,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Complete order
   */
  async completeOrder(req: Request, res: Response, next: NextFunction) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        throw new BadRequestError('Order ID is required');
      }
      const result = await orderService.completeOrder(orderId);
      sendResponse(res, 200, 'success', 'Order completed successfully', {
        data: result,
        meta: {
          orderId,
        },
      });
    } catch (error) {
      next(error);
      logger.error('Order completion failed', {
        orderId: req.params.id,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async validateOrderInQRCode(req: Request, res: Response, next: NextFunction) {
    try {
      const { qrCode } = req.body;
      if (!qrCode) {
        throw new BadRequestError('QR code is required');
      }
      const result = await orderService.validateOrderInQRCode(qrCode);
      sendResponse(res, 200, 'success', 'Order validated successfully', {
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async updateOrder(req: Request, res: Response, next: NextFunction) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        throw new BadRequestError('Order ID is required');
      }
      const { customerId, items, deliveryAddress, paymentMethod } = req.body;
      const result = await orderService.updateOrder(
        orderId,
        customerId,
        items,
        deliveryAddress,
        paymentMethod
      );
      sendResponse(res, 200, 'success', 'Order updated successfully', {
        data: result,
        meta: {
          orderId,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async deleteOrder(req: Request, res: Response, next: NextFunction) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        throw new BadRequestError('Order ID is required');
      }
      const result = await orderService.deleteOrder(orderId);
      sendResponse(res, 200, 'success', 'Order deleted successfully', {
        data: result,
        meta: {
          orderId,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export const orderController = new OrderController();
