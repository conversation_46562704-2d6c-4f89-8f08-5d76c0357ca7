import { Request, Response, NextFunction } from 'express';
import { cylinderService } from '../services/cylinder.services';
import { sendResponse } from '../utils/response';
import { BadRequestError, ValidationError } from '../errors/app_errors';
import { CylinderType, CylinderMaterial, CylinderStatus } from '../enums/enums';
import { Types } from 'mongoose';
import logger from '../config/logger';

class CylinderController {
  /**
   * @route   POST /api/v1/cylinders
   * @desc    Create a new cylinder type
   * @access  Private (Admin only)
   */
  async createCylinder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        type,
        material,
        price,
        cost,
        imageUrl,
        description,
        quantity,
        minimumStockLevel,
        status,
      } = req.body;

      // Validation
      if (!type || !material || !price || !cost) {
        throw new ValidationError('Type, material, price, and cost are required');
      }

      if (!Object.values(CylinderType).includes(type)) {
        throw new ValidationError('Invalid cylinder type');
      }

      if (!Object.values(CylinderMaterial).includes(material)) {
        throw new ValidationError('Invalid cylinder material');
      }

      if (price <= 0 || cost <= 0) {
        throw new ValidationError('Price and cost must be positive numbers');
      }

      if (status && !Object.values(CylinderStatus).includes(status)) {
        throw new ValidationError('Invalid cylinder status');
      }

      const cylinderData = {
        type,
        material,
        price: Number(price),
        cost: Number(cost),
        imageUrl: imageUrl?.trim(),
        description: description?.trim(),
        quantity: quantity ? Number(quantity) : undefined,
        minimumStockLevel: minimumStockLevel ? Number(minimumStockLevel) : undefined,
        status,
      };

      const cylinder = await cylinderService.createCylinder(cylinderData);

      logger.info('Cylinder created successfully', {
        cylinderId: cylinder._id,
        type: cylinder.type,
        material: cylinder.material,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 201, 'success', 'Cylinder created successfully', {
        data: cylinder,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/cylinders
   * @desc    Get all cylinders with filtering and pagination
   * @access  Private (All authenticated users)
   */
  async getCylinders(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { type, material, status, lowStockOnly, page = 1, limit = 10 } = req.query;

      // Validation
      const pageNum = Math.max(1, parseInt(page as string) || 1);
      const limitNum = Math.min(50, Math.max(1, parseInt(limit as string) || 10));

      const filters: any = {};
      if (type) filters.type = type as CylinderType;
      if (material) filters.material = material as CylinderMaterial;
      if (status) filters.status = status as CylinderStatus;
      if (lowStockOnly === 'true') filters.lowStockOnly = true;

      const result = await cylinderService.listCylinders(
        filters,
        {
          page: pageNum,
          limit: limitNum,
        },
        {
          userId: req.user?.userId.toString(),
          role: req.user?.role,
        }
      );

      sendResponse(res, 200, 'success', 'Cylinders retrieved successfully', {
        data: result.data,
        meta: {
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: result.total,
            pages: Math.ceil(result.total / limitNum),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/cylinders/:id
   * @desc    Get cylinder by ID
   * @access  Private (All authenticated users)
   */
  async getCylinderById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid cylinder ID');
      }

      const cylinder = await cylinderService.getCylinderById(id);

      sendResponse(res, 200, 'success', 'Cylinder retrieved successfully', {
        data: cylinder,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   PUT /api/v1/cylinders/:id
   * @desc    Update cylinder details
   * @access  Private (Admin only)
   */
  async updateCylinder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid cylinder ID');
      }

      // Validate numeric fields if provided
      if (updateData.price !== undefined && updateData.price <= 0) {
        throw new ValidationError('Price must be positive');
      }
      if (updateData.cost !== undefined && updateData.cost <= 0) {
        throw new ValidationError('Cost must be positive');
      }

      // Validate enum fields if provided
      if (updateData.type && !Object.values(CylinderType).includes(updateData.type)) {
        throw new ValidationError('Invalid cylinder type');
      }
      if (updateData.material && !Object.values(CylinderMaterial).includes(updateData.material)) {
        throw new ValidationError('Invalid cylinder material');
      }
      if (updateData.status && !Object.values(CylinderStatus).includes(updateData.status)) {
        throw new ValidationError('Invalid cylinder status');
      }

      const cylinder = await cylinderService.updateCylinder(id, updateData);

      logger.info('Cylinder updated successfully', {
        cylinderId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Cylinder updated successfully', {
        data: cylinder,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   DELETE /api/v1/cylinders/:id
   * @desc    Delete a cylinder type
   * @access  Private (Admin only)
   */
  async deleteCylinder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid cylinder ID');
      }

      const cylinder = await cylinderService.deleteCylinder(id);

      logger.info('Cylinder deleted successfully', {
        cylinderId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Cylinder deleted successfully', {
        data: cylinder,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/cylinders/:id/availability
   * @desc    Check cylinder availability
   * @access  Private (All authenticated users)
   */
  async checkCylinderAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity = 1 } = req.query;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid cylinder ID');
      }

      const requestedQuantity = Math.max(1, parseInt(quantity as string) || 1);
      const availability = await cylinderService.checkAvailability(id, requestedQuantity);

      sendResponse(res, 200, 'success', 'Cylinder availability checked successfully', {
        data: availability,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/cylinders/low-stock
   * @desc    Get cylinders with low stock
   * @access  Private (Admin, Agent)
   */
  async getLowStockCylinders(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const cylinders = await cylinderService.getLowStockAlerts();

      sendResponse(res, 200, 'success', 'Low stock cylinders retrieved successfully', {
        data: cylinders,
        meta: { count: cylinders.length },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/cylinders/statistics
   * @desc    Get cylinder sales statistics
   * @access  Private (Admin only)
   */
  async getCylinderStatistics(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await cylinderService.getSalesStatistics();

      sendResponse(res, 200, 'success', 'Cylinder statistics retrieved successfully', {
        data: statistics,
      });
    } catch (error) {
      next(error);
    }
  }

  // ==================== ADMINISTRATIVE INVENTORY METHODS ====================
  // Note: Reservation, release, and sales operations are now handled automatically
  // through the order lifecycle. Only administrative operations remain.

  /**
   * @route   POST /api/v1/cylinders/:id/restock
   * @desc    Restock cylinder
   * @access  Private (Admin only)
   */
  async restockCylinder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity } = req.body;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid cylinder ID');
      }

      if (!quantity || quantity <= 0) {
        throw new ValidationError('Quantity must be a positive number');
      }

      const cylinder = await cylinderService.restock(id, Number(quantity));

      logger.info('Cylinder restocked successfully', {
        cylinderId: id,
        quantity: Number(quantity),
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Cylinder restocked successfully', {
        data: cylinder,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   PUT /api/v1/cylinders/bulk-status
   * @desc    Bulk update cylinder statuses
   * @access  Private (Admin only)
   */
  async bulkUpdateCylinderStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { cylinderIds, status } = req.body;

      if (!Array.isArray(cylinderIds) || cylinderIds.length === 0) {
        throw new ValidationError('Cylinder IDs array is required');
      }

      if (!status || !Object.values(CylinderStatus).includes(status)) {
        throw new ValidationError('Valid status is required');
      }

      // Validate all cylinder IDs
      for (const id of cylinderIds) {
        if (!Types.ObjectId.isValid(id)) {
          throw new ValidationError(`Invalid cylinder ID: ${id}`);
        }
      }

      const modifiedCount = await cylinderService.bulkUpdateStatus(cylinderIds, status);

      logger.info('Bulk cylinder status update completed', {
        cylinderIds,
        status,
        modifiedCount,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Cylinder statuses updated successfully', {
        data: { modifiedCount },
      });
    } catch (error) {
      next(error);
    }
  }
}

export const cylinderController = new CylinderController();
