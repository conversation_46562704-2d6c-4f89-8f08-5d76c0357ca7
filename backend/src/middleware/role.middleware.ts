import { Request, Response, NextFunction } from 'express';
import { UnauthorizedError } from '../errors/app_errors';
import { UserRole } from '../enums/enums';

export const validateRole = (allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // log user role and allowed roles and userId
      console.log('User role and allowed roles', req.user?.role, allowedRoles);
      console.log('User ID', req.user?.userId);

      // Check if user exists in request (should be added by authenticate middleware)
      if (!req.user) {
        throw new UnauthorizedError('Authentication required');
      }

      // Check if user role is in allowed roles
      if (!allowedRoles.includes(req.user.role as UserRole)) {
        console.log('User role not allowed', req.user.role, allowedRoles);
        throw new UnauthorizedError('You do not have permission to access this resource');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};
