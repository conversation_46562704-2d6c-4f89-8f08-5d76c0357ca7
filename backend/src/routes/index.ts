import express, { Router } from 'express';
import userRouter from './user.routes';
import smsRouter from './sms.routes';
import notificationRouter from './notification.routes';
import orderRouter from './order.routes';
import sparePartRouter from './spare_part.routes';
import packageRouter from './package.routes';
import cylinderRouter from './cylinder.routes';
import paymentRouter from './payment.routes';
import dashboardRouter from './dashboard.routes';
import testRouter from './test.routes';
import path from 'path';

const appRouter = Router();

const baseRoute = '/api/v1';

// Existing routes
appRouter.use(`${baseRoute}/users`, userRouter);
appRouter.use(`${baseRoute}/sms`, smsRouter);
appRouter.use(`${baseRoute}/notifications`, notificationRouter);
appRouter.use(`${baseRoute}/orders`, orderRouter);

// Payment management routes
appRouter.use(`${baseRoute}/payments`, paymentRouter);

// Inventory management routes
appRouter.use(`${baseRoute}/spare-parts`, sparePartRouter);
appRouter.use(`${baseRoute}/packages`, packageRouter);
appRouter.use(`${baseRoute}/cylinders`, cylinderRouter);

// Dashboard routes
appRouter.use(`${baseRoute}/dashboard`, dashboardRouter);

// Test Routes
appRouter.use(testRouter);

// Static routes

// app.use('/images/cylinders', express.static(path.join(__dirname, 'public', 'images', 'cylinders')));
// const cylinderPath = path.join(__dirname, 'public', 'images', 'cylinders');
const cylinderPath = path.join(__dirname, '..','..', 'public', 'images', 'cylinders');

console.log(cylinderPath);
appRouter.use(
  // '/images/cylinders',
  '/api/v1/images/cylinders',
  express.static(
    cylinderPath
    // {
    // fallthrough: false,
    // setHeaders: (res, path) => {
    //   if (!fs.existsSync(path)) {
    //     res.redirect('/images/cylinders/default.png');
    //   }
    // },
    // }
  )
);

export default appRouter;
