import { Router } from 'express';
import { orderController } from '../controllers/order.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const orderRouter = Router();

// Apply authentication middleware to all order routes
orderRouter.use(authenticate);

/**
 * @route   POST /api/v1/orders
 * @desc    Create a new order
 * @access  Private (Customers and admins only)
 * @body    { items[], deliveryAddress, paymentMethod? }
 */
orderRouter.post(
  '/',
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN]),
  orderController.createOrder
);

/**
 * @route   GET /api/v1/orders
 * @desc    Get all orders
 * @access  Private (Customers only)
 * @query   { customer?, status?, paymentMethod?, startDate?, endDate? }
 */
orderRouter.get('/', orderController.getOrders);

/**
 * @route   PUT /api/v1/orders/:id/assign-agent
 * @desc    Assign agent to order
 * @access  Private (Admin, Agent)
 * @body    { agentId }
 */
orderRouter.put(
  '/:id/assign-agent',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  orderController.assignAgentToOrder
);

/**
 * @route   PUT /api/v1/orders/:id/cancel
 * @desc    Cancel order
 * @access  Private (Admin, Agent)
 */
orderRouter.put(
  '/:id/cancel',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  orderController.cancelOrder
);

/**
 * @route   PUT /api/v1/orders/:id/complete
 * @desc    Complete order
 * @access  Private (Admin, Agent)
 */
orderRouter.put(
  '/:id/complete',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  orderController.completeOrder
);

/**
 * @route   POST /api/v1/orders/validate
 * @desc    Validate order in QR code
 * @access  Private (Admin, Agent)
 * @body    { qrCode }
 */
orderRouter.post(
  '/validate-qr',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  orderController.validateOrderInQRCode
);

/**
 * @route   PUT /api/v1/orders/:id
 * @desc    Update order
 * @access  Private (Admin, Agent)
 * @body    { customerId?, items?, deliveryAddress?, paymentMethod? }
 */
orderRouter.put(
  '/:id',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  orderController.updateOrder
);

/**
 * @route   DELETE /api/v1/orders/:id
 * @desc    Delete order
 * @access  Private (Admin, Agent)
 */
orderRouter.delete(
  '/:id',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  orderController.deleteOrder
);

export default orderRouter;
