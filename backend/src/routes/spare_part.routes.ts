import { Router } from 'express';
import { sparePartController } from '../controllers/spare_part.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const sparePartRouter = Router();

// Apply authentication middleware to all spare part routes
sparePartRouter.use(authenticate);

// ==================== CRUD OPERATIONS ====================

/**
 * @route   POST /api/v1/spare-parts
 * @desc    Create a new spare part
 * @access  Private (Admin only)
 * @body    { name, description?, price, cost, category, compatibleCylinderTypes?, barcode?, minimumStockLevel?, imageUrl? }
 */
sparePartRouter.post('/', validateRole([UserRole.ADMIN]), sparePartController.createSparePart);

/**
 * @route   GET /api/v1/spare-parts
 * @desc    Get all spare parts with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { search?, category?, status?, compatibleWith?, lowStock?, page?, limit? }
 */
sparePartRouter.get('/', sparePartController.getSpareParts);

/**
 * @route   GET /api/v1/spare-parts/search
 * @desc    Search spare parts
 * @access  Private (All authenticated users)
 * @query   { q, limit? }
 */
sparePartRouter.get('/search', sparePartController.searchSpareParts);

/**
 * @route   GET /api/v1/spare-parts/low-stock
 * @desc    Get spare parts with low stock
 * @access  Private (Admin, Agent)
 */
sparePartRouter.get(
  '/low-stock',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  sparePartController.getLowStockSpareParts
);

/**
 * @route   GET /api/v1/spare-parts/statistics
 * @desc    Get spare parts sales statistics
 * @access  Private (Admin only)
 */
sparePartRouter.get(
  '/statistics',
  validateRole([UserRole.ADMIN]),
  sparePartController.getSparePartStatistics
);

/**
 * @route   GET /api/v1/spare-parts/:id
 * @desc    Get spare part by ID
 * @access  Private (All authenticated users)
 */
sparePartRouter.get('/:id', sparePartController.getSparePartById);

/**
 * @route   PUT /api/v1/spare-parts/:id
 * @desc    Update spare part details
 * @access  Private (Admin only)
 * @body    { name?, description?, price?, cost?, category?, compatibleCylinderTypes?, barcode?, minimumStockLevel?, imageUrl? }
 */
sparePartRouter.put('/:id', validateRole([UserRole.ADMIN]), sparePartController.updateSparePart);

// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.

/**
 * @route   POST /api/v1/spare-parts/:id/restock
 * @desc    Restock spare part
 * @access  Private (Admin only)
 * @body    { quantity }
 */
sparePartRouter.post(
  '/:id/restock',
  validateRole([UserRole.ADMIN]),
  sparePartController.restockSparePart
);

/**
 * @route   PUT /api/v1/spare-parts/:id/discontinue
 * @desc    Discontinue a spare part
 * @access  Private (Admin only)
 */
sparePartRouter.put(
  '/:id/discontinue',
  validateRole([UserRole.ADMIN]),
  sparePartController.discontinueSparePart
);

export default sparePartRouter;
