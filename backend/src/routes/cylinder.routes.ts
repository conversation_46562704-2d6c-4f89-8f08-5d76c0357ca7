import { Router } from 'express';
import { cylinderController } from '../controllers/cylinder.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const cylinderRouter = Router();

// Apply authentication middleware to all cylinder routes
cylinderRouter.use(authenticate);

// ==================== CRUD OPERATIONS ====================

/**
 * @route   POST /api/v1/cylinders
 * @desc    Create a new cylinder type
 * @access  Private (Admin only)
 * @body    { type, material, price, cost, imageUrl?, description?, quantity?, minimumStockLevel?, status? }
 */
cylinderRouter.post('/', validateRole([UserRole.ADMIN]), cylinderController.createCylinder);

/**
 * @route   GET /api/v1/cylinders
 * @desc    Get all cylinders with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { type?, material?, status?, lowStockOnly?, page?, limit? }
 */
cylinderRouter.get('/', cylinderController.getCylinders);

/**
 * @route   GET /api/v1/cylinders/low-stock
 * @desc    Get cylinders with low stock
 * @access  Private (Admin, Agent)
 */
cylinderRouter.get(
  '/low-stock',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  cylinderController.getLowStockCylinders
);

/**
 * @route   GET /api/v1/cylinders/statistics
 * @desc    Get cylinder sales statistics
 * @access  Private (Admin only)
 */
cylinderRouter.get(
  '/statistics',
  validateRole([UserRole.ADMIN]),
  cylinderController.getCylinderStatistics
);

/**
 * @route   GET /api/v1/cylinders/:id
 * @desc    Get cylinder by ID
 * @access  Private (All authenticated users)
 */
cylinderRouter.get('/:id', cylinderController.getCylinderById);

/**
 * @route   PUT /api/v1/cylinders/:id
 * @desc    Update cylinder details
 * @access  Private (Admin only)
 * @body    { type?, material?, price?, cost?, imageUrl?, description?, minimumStockLevel?, status? }
 */
cylinderRouter.put('/:id', validateRole([UserRole.ADMIN]), cylinderController.updateCylinder);

/**
 * @route   DELETE /api/v1/cylinders/:id
 * @desc    Delete a cylinder type
 * @access  Private (Admin only)
 */
cylinderRouter.delete('/:id', validateRole([UserRole.ADMIN]), cylinderController.deleteCylinder);

/**
 * @route   GET /api/v1/cylinders/:id/availability
 * @desc    Check cylinder availability
 * @access  Private (All authenticated users)
 * @query   { quantity? }
 */
cylinderRouter.get('/:id/availability', cylinderController.checkCylinderAvailability);

// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.

/**
 * @route   POST /api/v1/cylinders/:id/restock
 * @desc    Restock cylinder
 * @access  Private (Admin only)
 * @body    { quantity }
 */
cylinderRouter.post(
  '/:id/restock',
  validateRole([UserRole.ADMIN]),
  cylinderController.restockCylinder
);

/**
 * @route   PUT /api/v1/cylinders/bulk-status
 * @desc    Bulk update cylinder statuses
 * @access  Private (Admin only)
 * @body    { cylinderIds, status }
 */
cylinderRouter.put(
  '/bulk-status',
  validateRole([UserRole.ADMIN]),
  cylinderController.bulkUpdateCylinderStatus
);

export default cylinderRouter;
