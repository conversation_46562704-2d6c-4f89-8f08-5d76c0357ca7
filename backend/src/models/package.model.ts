import { Schema, model, Document } from 'mongoose';
import { BadRequestError } from '../errors/app_errors';

export interface IPackageItem {
  type: 'SparePart';
  part: Schema.Types.ObjectId;
  quantity: number;
  _id?: Schema.Types.ObjectId; // For subdocument operations
}

export interface IPackage extends Document {
  name: string;
  description?: string;
  cylinder: Schema.Types.ObjectId;
  includedSpareParts: IPackageItem[];
  totalPrice: number;
  costPrice: number;
  discount?: number;
  imageUrl?: string;
  isActive: boolean;
  quantity: number; // Total physical count
  reserved: number; // Reserved for orders but not yet sold
  sold: number; // Total sold count (for reporting)
  lastRestockedAt?: Date; // Last restock date
  minimumStockLevel: number; // Reorder threshold
  availableQuantity: number; // Derived field for available quantity
  createdAt: Date;
  updatedAt: Date;
}

const PackageSchema = new Schema<IPackage>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: { type: String },
    cylinder: {
      type: Schema.Types.ObjectId,
      ref: 'Cylinder',
      required: true,
    },
    includedSpareParts: [
      {
        part: {
          type: Schema.Types.ObjectId,
          ref: 'SparePart',
          required: true,
        },
        quantity: {
          type: Number,
          default: 1,
          min: 1,
        },
      },
    ],
    totalPrice: {
      type: Number,
      required: true,
      min: 0,
    },
    costPrice: {
      type: Number,
      min: 0,
    },
    discount: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    imageUrl: { type: String },
    isActive: {
      type: Boolean,
      default: true,
    },
    quantity: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    lastRestockedAt: Date,
    minimumStockLevel: { type: Number, default: 10, min: 0 },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Compound indexes for performance
PackageSchema.index({ name: 'text', description: 'text' });
PackageSchema.index({ isActive: 1 });
PackageSchema.index({ totalPrice: 1 });
PackageSchema.index({ quantity: 1, minimumStockLevel: 1 });
PackageSchema.index({ quantity: 1 }); // Low stock alerts
PackageSchema.index({ quantity: 1, reserved: 1 }); // Helps with availableQuantity calculations

// Virtuals
PackageSchema.virtual('availableQuantity').get(function (this: IPackage) {
  return Math.max(0, this.quantity - this.reserved);
});

// Hook for ensuring data consistency
PackageSchema.pre('save', function (next) {
  if (this.reserved > this.quantity) {
    throw new BadRequestError('Reserved quantity cannot exceed total quantity');
  }
  next();
});

export const Package = model<IPackage>('Package', PackageSchema);
