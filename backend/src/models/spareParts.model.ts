import { CylinderType, SparePartCategory, SparePartStatus } from '../enums/enums';
import { Schema, model, Document } from 'mongoose';

export interface ISparePart extends Document {
  name: string;
  description?: string;
  price: number;
  cost: number; // Purchase cost for profit calculations
  stock: number;
  reserved: number;
  sold: number;
  status: SparePartStatus;
  category: SparePartCategory;
  compatibleCylinderTypes: CylinderType[];
  barcode?: string;
  location?: string;
  minimumStockLevel: number;
  lastRestockedAt?: Date;
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  availableQuantity: number;
}

const SparePartSchema = new Schema<ISparePart>(
  {
    name: { type: String, required: true, unique: true, trim: true },
    description: { type: String },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    stock: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    status: {
      type: String,
      enum: Object.values(SparePartStatus),
      default: SparePartStatus.AVAILABLE,
    },
    category: {
      type: String,
      enum: Object.values(SparePartCategory),
      required: true,
    },
    compatibleCylinderTypes: [
      {
        type: String,
        enum: Object.values(CylinderType),
      },
    ],
    barcode: { type: String, unique: true, sparse: true },
    minimumStockLevel: { type: Number, default: 5 },
    lastRestockedAt: { type: Date },
    imageUrl: { type: String },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
SparePartSchema.index({ status: 1 });
SparePartSchema.index({ stock: 1 });
SparePartSchema.index({ category: 1 });
SparePartSchema.index({ compatibleCylinderTypes: 1 });
SparePartSchema.index(
  { name: 'text', description: 'text', barcode: 'text' },
  { weights: { name: 3, barcode: 2, description: 1 } }
);

// Virtuals
SparePartSchema.virtual('availableQuantity').get(function () {
  return Math.max(0, this.stock - this.reserved);
});

SparePartSchema.path('price').validate(function (value) {
  return value >= this.cost;
}, 'Price must be >= cost');

export const SparePart = model<ISparePart>('SparePart', SparePartSchema);
