import { Schema, model, Document } from 'mongoose';
import { OrderStatus, PaymentMethod } from '../enums/enums';

export enum OrderItemType {
  SparePart = 'SPARE_PART',
  Cylinder = 'CYLINDER',
  Package = 'PACKAGE',
}

export interface IOrderItem {
  itemType: OrderItemType;
  itemId: Schema.Types.ObjectId | string;
  quantity: number;
}

export interface IOrder extends Document {
  customer: Schema.Types.ObjectId | string;
  payment: Schema.Types.ObjectId | string;
  deliveryAgent?: Schema.Types.ObjectId | string;
  items: IOrderItem[];
  totalAmount: number;
  status: OrderStatus;
  deliveryAddress: string;
  paymentMethod: PaymentMethod;
  createdAt: Date;
  updatedAt: Date;
  deliveredAt?: Date;
  cancelledAt?: Date;
  failedAt?: Date;
  failureReason?: string;
  qrCode: string;
  qrCodeExpiresAt: Date;
}

const OrderSchema = new Schema<IOrder>(
  {
    customer: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    payment: { type: Schema.Types.ObjectId, ref: 'Payment' },
    deliveryAgent: { type: Schema.Types.ObjectId, ref: 'User', required: false },
    items: [
      {
        itemType: {
          type: String,
          enum: Object.values(OrderItemType),
          required: true,
        },
        itemId: { type: Schema.Types.ObjectId, required: true },
        quantity: { type: Number, default: 1 },
      },
    ],
    totalAmount: { type: Number, required: true },
    status: {
      type: String,
      enum: Object.values(OrderStatus),
      default: OrderStatus.PENDING,
    },
    deliveryAddress: { type: String, required: true },
    paymentMethod: {
      type: String,
      enum: Object.values(PaymentMethod),
      // required: true,
      default: PaymentMethod.WAAFI_PREAUTH,
    },
    deliveredAt: Date,
    cancelledAt: Date,
    failedAt: Date,
    failureReason: String,
    qrCode: { type: String, unique: true },
    qrCodeExpiresAt: Date,
  },
  { timestamps: true }
);

// Index for order tracking
OrderSchema.index({ customer: 1, status: 1 });
OrderSchema.index({ createdAt: -1 }); // Recent orders first

export const Order = model<IOrder>('Order', OrderSchema);
