import mongoose, { ClientSession, Types } from 'mongoose';
import { Package, IPackage, IPackageItem } from '../models/package.model';
import { Cylinder } from '../models/cylinder.model';
import { SparePart } from '../models/spareParts.model';
import { BadRequestError, NotFoundError, DuplicateResourceError } from '../errors/app_errors';
import logger from '../config/logger';

class PackageService {
  /**
   * Create a new package
   * @throws {DuplicateResourceError} If package with same name exists
   * @throws {NotFoundError} If cylinder or spare parts not found
   * @throws {BadRequestError} If validation fails
   */
  async createPackage(
    data: {
      name: string;
      description?: string;
      cylinder: Types.ObjectId;
      includedSpareParts: IPackageItem[];
      totalPrice?: number;
      costPrice?: number;
      discount?: number;
      imageUrl?: string;
      isActive?: boolean;
    },
    session?: ClientSession
  ): Promise<IPackage> {
    const sessionToUse = session || (await mongoose.startSession());
    const shouldCommit = !session;

    try {
      if (!session) sessionToUse.startTransaction();

      // Check for duplicate package name
      const existingPackage = await Package.findOne({ name: data.name }).session(sessionToUse);
      if (existingPackage) {
        throw new DuplicateResourceError('Package with this name already exists', {
          code: 'DUPLICATE_PACKAGE',
        });
      }

      // Validate cylinder exists
      const cylinderExists = await Cylinder.exists({ _id: data.cylinder }).session(sessionToUse);
      if (!cylinderExists) {
        throw new NotFoundError('Cylinder not found', {
          code: 'CYLINDER_NOT_FOUND',
        });
      }

      // Validate spare parts exist and are active
      await this.validateSpareParts(data.includedSpareParts, sessionToUse);

      // Calculate costPrice if not provided
      let costPrice = data.costPrice;
      if (!costPrice) {
        costPrice = await this.calculatePackageCost(data.includedSpareParts, sessionToUse);
      }

      // Calculate totalPrice if not provided (apply discount if specified)
      let totalPrice = data.totalPrice;
      if (!totalPrice) {
        totalPrice = await this.calculatePackagePrice(costPrice, data.discount || 0);
      }

      const newPackage = new Package({
        isActive: true,
        quantity: 0,
        reserved: 0,
        sold: 0,
        minimumStockLevel: 10,
        ...data,
        costPrice,
        totalPrice,
      });

      await newPackage.save({ session: sessionToUse });

      if (shouldCommit) {
        await sessionToUse.commitTransaction();
      }

      return newPackage;
    } catch (error) {
      if (shouldCommit) {
        try {
          await sessionToUse.abortTransaction();
        } catch (error) {
          logger.error('Failed to abort transaction', {
            error: error.message,
            timestamp: new Date().toISOString(),
          });
        }
      }
      throw error;
    } finally {
      if (shouldCommit) {
        sessionToUse.endSession();
      }
    }
  }

  /**
   * Update package details
   * @throws {NotFoundError} If package or related items not found
   */
  async updatePackage(
    id: Types.ObjectId,
    updateData: Partial<IPackage>,
    session?: ClientSession
  ): Promise<IPackage> {
    const sessionToUse = session || (await mongoose.startSession());
    const shouldCommit = !session;

    try {
      if (!session) sessionToUse.startTransaction();

      // Prevent certain fields from being updated directly
      const {
        totalPrice,
        costPrice,
        quantity,
        reserved,
        sold,
        availableQuantity,
        ...safeUpdateData
      } = updateData;

      // Create a properly typed update object
      const finalUpdateData: Partial<IPackage> = { ...safeUpdateData };

      // Handle spare parts updates
      if (updateData.includedSpareParts) {
        await this.validateSpareParts(updateData.includedSpareParts, sessionToUse);

        // Recalculate prices if parts changed
        const newCostPrice = await this.calculatePackageCost(
          updateData.includedSpareParts,
          sessionToUse
        );

        finalUpdateData.costPrice = newCostPrice;
        finalUpdateData.totalPrice = await this.calculatePackagePrice(
          newCostPrice,
          updateData.discount || 0
        );
      } else if (updateData.discount !== undefined) {
        // Only discount changed - recalculate totalPrice
        const packageDoc = await Package.findById(id).session(sessionToUse);
        if (!packageDoc) throw new NotFoundError('Package not found');

        finalUpdateData.totalPrice = await this.calculatePackagePrice(
          packageDoc.costPrice,
          updateData.discount
        );
      }

      const updatedPackage = await Package.findByIdAndUpdate(id, finalUpdateData, {
        new: true,
        runValidators: true,
        session: sessionToUse,
      });

      if (!updatedPackage) {
        throw new NotFoundError('Package not found', {
          code: 'PACKAGE_NOT_FOUND',
        });
      }

      if (shouldCommit) {
        await sessionToUse.commitTransaction();
      }

      return updatedPackage;
    } catch (error) {
      if (shouldCommit) {
        try {
          await sessionToUse.abortTransaction();
        } catch (error) {
          logger.error('Failed to abort transaction', {
            error: error.message,
            timestamp: new Date().toISOString(),
          });
        }
      }
      throw error;
    } finally {
      if (shouldCommit) {
        sessionToUse.endSession();
      }
    }
  }

  /**
   * Get package by ID with populated data
   * @throws {NotFoundError} If package not found
   */
  async getPackageById(id: Types.ObjectId, populate = true): Promise<IPackage> {
    let query = Package.findById(id);

    if (populate) {
      query = query.populate('cylinder').populate('includedSpareParts.part');
    }

    const packageDoc = await query;

    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    return packageDoc;
  }

  /**
   * List packages with filtering and pagination
   */
  async listPackages(
    filters: {
      search?: string;
      cylinder?: Types.ObjectId;
      isActive?: boolean;
      minPrice?: number;
      maxPrice?: number;
    } = {},
    pagination: { page: number; limit: number } = { page: 1, limit: 10 },
    populate = true
  ): Promise<{ data: IPackage[]; total: number }> {
    const query: any = {};

    // Text search
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    // Cylinder filter
    if (filters.cylinder) {
      query.cylinder = filters.cylinder;
    }

    // Active filter
    if (filters.isActive !== undefined) {
      query.isActive = filters.isActive;
    }

    // Price range filter
    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      query.totalPrice = {};
      if (filters.minPrice !== undefined) query.totalPrice.$gte = filters.minPrice;
      if (filters.maxPrice !== undefined) query.totalPrice.$lte = filters.maxPrice;
    }

    let findQuery = Package.find(query)
      .sort({ createdAt: -1 })
      .skip((pagination.page - 1) * pagination.limit)
      .limit(pagination.limit);

    if (populate) {
      findQuery = findQuery.populate('cylinder').populate('includedSpareParts.part');
    }

    const [data, total] = await Promise.all([findQuery.exec(), Package.countDocuments(query)]);

    return { data, total };
  }

  /**
   * Toggle package active status
   * @throws {NotFoundError} If package not found
   */
  async togglePackageStatus(id: Types.ObjectId, session?: ClientSession): Promise<IPackage> {
    const packageDoc = await Package.findById(id).session(session || null);

    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    const updatedPackage = await Package.findByIdAndUpdate(
      id,
      { isActive: !packageDoc.isActive },
      { new: true, session }
    );

    return updatedPackage!;
  }

  /**
   * Validate all spare parts in a package
   * @throws {NotFoundError} If any spare part not found
   * @throws {BadRequestError} If any spare part is inactive
   */
  private async validateSpareParts(items: IPackageItem[], session?: ClientSession): Promise<void> {
    if (!items || items.length === 0) {
      throw new BadRequestError('Package must include at least one spare part', {
        code: 'EMPTY_PACKAGE',
      });
    }

    const partIds = items.map(item => item.part);
    const spareParts = await SparePart.find({
      _id: { $in: partIds },
    }).session(session || null);

    // Check all parts exist
    if (spareParts.length !== items.length) {
      const foundIds = spareParts.map(p => p._id.toString());
      const missingIds = partIds.filter(id => !foundIds.includes(id.toString()));

      throw new NotFoundError(`Spare parts not found: ${missingIds.join(', ')}`, {
        code: 'SPARE_PARTS_NOT_FOUND',
      });
    }

    // Check all parts are active
    const inactiveParts = spareParts.filter(p => p.status !== 'AVAILABLE');
    if (inactiveParts.length > 0) {
      throw new BadRequestError(
        `Cannot include inactive spare parts: ${inactiveParts.map(p => p.name).join(', ')}`,
        { code: 'INACTIVE_SPARE_PARTS' }
      );
    }
  }

  /**
   * Calculate package cost based on included spare parts
   */
  private async calculatePackageCost(
    items: IPackageItem[],
    session?: ClientSession
  ): Promise<number> {
    const partIds = items.map(item => item.part);
    const spareParts = await SparePart.find({
      _id: { $in: partIds },
    }).session(session || null);

    return items.reduce((total, item) => {
      const part = spareParts.find(p => p._id.toString() === item.part.toString());
      if (!part) return total;
      return total + part.cost * item.quantity;
    }, 0);
  }

  /**
   * Calculate package price with discount
   */
  private async calculatePackagePrice(costPrice: number, discount: number): Promise<number> {
    if (discount < 0 || discount > 100) {
      throw new BadRequestError('Discount must be between 0 and 100', {
        code: 'INVALID_DISCOUNT',
      });
    }

    const priceBeforeDiscount = costPrice * 1.5; // 50% markup by default
    return priceBeforeDiscount * (1 - discount / 100);
  }

  /**
   * Get package analytics
   */
  async getPackageAnalytics(): Promise<{
    totalPackages: number;
    activePackages: number;
    averagePrice: number;
    mostIncludedPart: {
      part: Types.ObjectId;
      count: number;
    };
  }> {
    const results = await Promise.all([
      Package.countDocuments(),
      Package.countDocuments({ isActive: true }),
      Package.aggregate([{ $group: { _id: null, avgPrice: { $avg: '$totalPrice' } } }]),
      Package.aggregate([
        { $unwind: '$includedSpareParts' },
        { $group: { _id: '$includedSpareParts.part', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 1 },
      ]),
    ]);

    return {
      totalPackages: results[0],
      activePackages: results[1],
      averagePrice: results[2][0]?.avgPrice || 0,
      mostIncludedPart: {
        part: results[3][0]?._id || null,
        count: results[3][0]?.count || 0,
      },
    };
  }

  // ==================== INVENTORY MANAGEMENT METHODS ====================

  /**
   * Reserve packages for an order
   * @param packageId - The package ID to reserve
   * @param quantity - Number of packages to reserve
   * @param orderId - The order ID for tracking
   * @param session - Optional MongoDB session for transactions
   * @throws {BadRequestError} If quantity is invalid or insufficient stock
   * @throws {NotFoundError} If package not found
   */
  async reservePackage(
    packageId: string,
    quantity: number,
    orderId: string,
    session?: ClientSession
  ): Promise<{ modifiedCount: number; newStatus?: boolean }> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const packageDoc = await Package.findById(packageId).session(session || null);
    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    if (!packageDoc.isActive) {
      throw new BadRequestError(`Cannot reserve inactive package`, {
        code: 'INACTIVE_PACKAGE',
      });
    }

    if (packageDoc.availableQuantity < quantity) {
      throw new BadRequestError('Insufficient package stock', {
        code: 'INSUFFICIENT_PACKAGE_STOCK',
        details: {
          available: packageDoc.availableQuantity,
          requested: quantity,
        },
      });
    }

    const update: any = {
      $inc: { reserved: quantity },
    };

    // Update status if this reservation brings us to out of stock
    let newStatus: boolean | undefined;
    if (packageDoc.quantity - (packageDoc.reserved + quantity) <= 0) {
      // update.$set = { isActive: false };
      newStatus = false;
    }

    const result = await Package.updateOne(
      {
        _id: packageId,
        $expr: { $gte: [{ $subtract: ['$quantity', '$reserved'] }, quantity] },
      },
      update,
      { session }
    );

    if (result.modifiedCount === 0) {
      throw new DuplicateResourceError('Package stock changed during reservation', {
        code: 'CONCURRENT_MODIFICATION',
      });
    }

    logger.info('Package reserved successfully', {
      packageId,
      quantity,
      orderId,
      timestamp: new Date().toISOString(),
    });

    return {
      modifiedCount: result.modifiedCount,
      newStatus,
    };
  }

  /**
   * Release reserved packages (when order is cancelled)
   * @param packageId - The package ID to release reservation for
   * @param quantity - Number of packages to release
   * @param orderId - The order ID for tracking
   * @param session - Optional MongoDB session for transactions
   * @throws {BadRequestError} If quantity is invalid or exceeds reserved amount
   * @throws {NotFoundError} If package not found
   */
  async releaseReservation(
    packageId: string,
    quantity: number,
    orderId: string,
    session?: ClientSession
  ): Promise<{ modifiedCount: number; newStatus?: boolean }> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const packageDoc = await Package.findById(packageId).session(session || null);
    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    if (packageDoc.reserved < quantity) {
      throw new BadRequestError('Cannot release more than reserved quantity', {
        code: 'INVALID_RESERVATION_RELEASE',
        details: {
          reserved: packageDoc.reserved,
          toRelease: quantity,
        },
      });
    }

    const update: any = {
      $inc: { reserved: -quantity },
    };

    // Update status if we're coming back from out of stock
    let newStatus: boolean | undefined;
    if (!packageDoc.isActive && packageDoc.quantity - (packageDoc.reserved - quantity) > 0) {
      update.$set = { isActive: true };
      newStatus = true;
    }

    const result = await Package.updateOne(
      { _id: packageId, reserved: { $gte: quantity } },
      update,
      { session }
    );

    if (result.modifiedCount === 0) {
      throw new DuplicateResourceError('Package stock changed during release', {
        code: 'CONCURRENT_MODIFICATION',
      });
    }

    logger.info('Package reservation released successfully', {
      packageId,
      quantity,
      orderId,
      timestamp: new Date().toISOString(),
    });

    return {
      modifiedCount: result.modifiedCount,
      newStatus,
    };
  }

  /**
   * Mark reserved packages as sold (when order is completed)
   * @param packageId - The package ID to mark as sold
   * @param quantity - Number of packages sold
   * @param session - Optional MongoDB session for transactions
   * @throws {BadRequestError} If quantity is invalid
   * @throws {NotFoundError} If package not found
   */
  async markPackageAsSold(
    packageId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<IPackage> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const packageDoc = await Package.findByIdAndUpdate(
      packageId,
      {
        $inc: {
          reserved: -quantity,
          sold: quantity,
          quantity: -quantity, // Physical count decreases when sold
        },
      },
      { new: true, session }
    );

    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    logger.info('Package marked as sold successfully', {
      packageId,
      quantity,
      remainingQuantity: packageDoc.quantity,
      timestamp: new Date().toISOString(),
    });

    return packageDoc;
  }

  /**
   * Get sales statistics aggregated by package
   * @returns Sales statistics including total sold and breakdown by package
   */
  async getPackageSalesStatistics(): Promise<{
    totalSold: number;
    byPackage: Array<{
      packageId: string;
      name: string;
      sold: number;
      revenue: number;
    }>;
    totalRevenue: number;
  }> {
    const packages = await Package.find({ sold: { $gt: 0 } });

    let totalSold = 0;
    let totalRevenue = 0;
    const byPackage = packages.map(pkg => {
      totalSold += pkg.sold;
      const revenue = pkg.sold * pkg.totalPrice;
      totalRevenue += revenue;

      return {
        packageId: pkg._id.toString(),
        name: pkg.name,
        sold: pkg.sold,
        revenue,
      };
    });

    return {
      totalSold,
      byPackage: byPackage.sort((a, b) => b.sold - a.sold), // Sort by most sold
      totalRevenue,
    };
  }

  /**
   * Restock packages and update status if needed
   * @param packageId - The package ID to restock
   * @param quantity - Number of packages to add to stock
   * @param session - Optional MongoDB session for transactions
   * @throws {BadRequestError} If quantity is invalid
   * @throws {NotFoundError} If package not found
   */
  async restockPackage(
    packageId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<IPackage> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const update: any = {
      $inc: { quantity },
      lastRestockedAt: new Date(),
    };

    // Update status if coming from inactive
    update.$set = {
      isActive: true,
      // status : PackageStatus.Active,
    };

    const packageDoc = await Package.findByIdAndUpdate(packageId, update, {
      new: true,
      session,
    });

    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    logger.info('Package restocked successfully', {
      packageId,
      quantity,
      newQuantity: packageDoc.quantity,
      timestamp: new Date().toISOString(),
    });

    return packageDoc;
  }

  /**
   * Adjust package stock (for damage, loss, etc.)
   * @param packageId - The package ID to adjust stock for
   * @param adjustment - Stock adjustment (positive or negative)
   * @param reason - Reason for adjustment
   * @param session - Optional MongoDB session for transactions
   * @throws {BadRequestError} If adjustment would result in negative stock
   * @throws {NotFoundError} If package not found
   */
  async adjustPackageStock(
    packageId: string,
    adjustment: number,
    reason: string,
    session?: ClientSession
  ): Promise<IPackage> {
    if (adjustment === 0) {
      throw new BadRequestError('Adjustment cannot be zero', {
        code: 'INVALID_ADJUSTMENT',
      });
    }

    const packageDoc = await Package.findById(packageId).session(session || null);
    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    if (packageDoc.quantity + adjustment < 0) {
      throw new BadRequestError('Adjustment would result in negative stock', {
        code: 'NEGATIVE_STOCK_ADJUSTMENT',
        details: {
          currentQuantity: packageDoc.quantity,
          adjustment,
          resultingQuantity: packageDoc.quantity + adjustment,
        },
      });
    }

    const update: any = {
      $inc: { quantity: adjustment },
    };

    // Update status based on resulting quantity
    if (packageDoc.quantity + adjustment <= 0) {
      update.$set = { isActive: false };
    } else if (!packageDoc.isActive && adjustment > 0) {
      update.$set = { isActive: true };
    }

    const updatedPackage = await Package.findByIdAndUpdate(packageId, update, {
      new: true,
      session,
    });

    logger.info('Package stock adjusted', {
      packageId,
      adjustment,
      reason,
      newQuantity: updatedPackage!.quantity,
      timestamp: new Date().toISOString(),
    });

    return updatedPackage!;
  }

  /**
   * Get packages with low stock (quantity <= minimumStockLevel)
   * @returns Array of packages that need restocking
   */
  async getLowStockPackages(): Promise<IPackage[]> {
    return Package.find({
      $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
      isActive: true, // Only active packages
    }).sort({ quantity: 1 }); // Sort by most critical first
  }

  /**
   * Check package availability
   * @param packageId - The package ID to check
   * @param requestedQuantity - Quantity requested
   * @throws {NotFoundError} If package not found
   * @returns Availability information
   */
  async checkPackageAvailability(
    packageId: string,
    requestedQuantity: number
  ): Promise<{ available: boolean; availableQuantity: number; isActive: boolean }> {
    const packageDoc = await Package.findById(packageId);
    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    return {
      available: packageDoc.isActive && packageDoc.availableQuantity >= requestedQuantity,
      availableQuantity: packageDoc.availableQuantity,
      isActive: packageDoc.isActive,
    };
  }

  /**
   * Get available package quantity (total - reserved)
   * @param packageId - The package ID to check
   * @throws {NotFoundError} If package not found
   * @returns Available quantity
   */
  async getAvailablePackageQuantity(packageId: string): Promise<number> {
    const packageDoc = await Package.findById(packageId);
    if (!packageDoc) {
      throw new NotFoundError('Package not found', {
        code: 'PACKAGE_NOT_FOUND',
      });
    }

    return packageDoc.availableQuantity;
  }

  /**
   * Bulk update package statuses
   * @param packageIds - Array of package IDs to update
   * @param isActive - New active status
   * @param session - Optional MongoDB session for transactions
   * @returns Number of modified documents
   */
  async bulkUpdatePackageStatus(
    packageIds: string[],
    isActive: boolean,
    session?: ClientSession
  ): Promise<number> {
    const result = await Package.updateMany(
      { _id: { $in: packageIds } },
      { $set: { isActive } },
      { session }
    );

    logger.info('Bulk package status update completed', {
      packageIds,
      isActive,
      modifiedCount: result.modifiedCount,
      timestamp: new Date().toISOString(),
    });

    return result.modifiedCount;
  }
}

export const packageService = new PackageService();
