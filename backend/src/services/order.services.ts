import mongoose, { ClientSession, Types } from 'mongoose';
import { Order, Payment, Cylinder, User, SparePart, Package } from '../models';
import { PaymentMethod, PaymentStatus, UserRole, OrderStatus } from '../enums/enums';
import logger from '../config/logger';
import {
  NotFoundError,
  BadRequestError,
  InternalServerError,
  ValidationError,
  AppError,
  PaymentError,
  UnauthorizedError,
} from '../errors/app_errors';
import { IOrderItem, OrderItemType } from '../models/order.model';
import { generateQRPayload, validateQRPayload } from '../utils/jwt_utils';
import {
  smsService,
  paymentService,
  packageService,
  cylinderService,
  sparePartService,
  // notificationService, // we will implement later
} from './index';

class OrderService {
  constructor() {}

  public async createOrder(
    customerId: string | Types.ObjectId,
    items: { itemType: string; itemId: string | Types.ObjectId; quantity: number }[],
    deliveryAddress: string,
    paymentMethod: PaymentMethod = PaymentMethod.WAAFI_PREAUTH
  ) {
    return this.executeWithRetry(async () => {
      return this.createOrderTransaction(customerId, items, deliveryAddress, paymentMethod);
    });
  }

  /**
   * Execute MongoDB transaction with retry logic for transient errors
   * @param operation - The transaction operation to execute
   * @param maxRetries - Maximum number of retry attempts
   * @returns Promise<T> - Result of the operation
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Check if error is retryable
        const isRetryable = this.isRetryableError(error);

        logger.warn('Transaction attempt failed', {
          attempt,
          maxRetries,
          isRetryable,
          error: error.message,
          timestamp: new Date().toISOString(),
        });

        if (!isRetryable || attempt === maxRetries) {
          throw error;
        }

        // Exponential backoff with jitter
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000) + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Check if an error is retryable (transient MongoDB errors)
   * @param error - The error to check
   * @returns boolean - Whether the error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (!error) return false;

    // Payment errors should NEVER be retried
    if (
      error instanceof PaymentError ||
      error.code === 'PAYMENT_FAILED' ||
      error.code === 'WAAFI_PAYMENT_FAILED' ||
      error.message?.includes('Payment Failed') ||
      error.message?.includes('payment') ||
      error.message?.includes('balance')
    ) {
      return false;
    }

    // Business logic errors should not be retried
    const nonRetryableErrors = [
      'CUSTOMER_NOT_FOUND',
      'ORDER_EXISTS',
      'INVALID_ITEM_FORMAT',
      'INVALID_ITEM_TYPE',
      'ITEM_NOT_FOUND',
      'INSUFFICIENT_STOCK',
    ];

    if (error.code && nonRetryableErrors.includes(error.code)) {
      return false;
    }

    // MongoDB transient transaction errors
    const retryableErrorCodes = [
      112, // WriteConflict
      11000, // DuplicateKey (in some cases)
      16500, // TransientTransactionError
      251, // NoSuchTransaction
    ];

    // Check error code
    if (error.code && retryableErrorCodes.includes(error.code)) {
      return true;
    }

    // Check error labels
    if (error.errorLabels && Array.isArray(error.errorLabels)) {
      return error.errorLabels.includes('TransientTransactionError');
    }

    // Check for application-level concurrent modification errors
    if (error.code === 'CONCURRENT_MODIFICATION') {
      return true;
    }

    // Check error message for known transient patterns
    const retryableMessages = [
      'WriteConflict',
      'TransientTransactionError',
      'NoSuchTransaction',
      'Transaction has been aborted',
      'Cylinder stock changed during reservation',
      'Package stock changed during reservation',
      'stock changed during',
    ];

    const errorMessage = error.message || '';
    return retryableMessages.some(msg => errorMessage.includes(msg));
  }

  private async createOrderTransaction(
    customerId: string | Types.ObjectId,
    items: { itemType: string; itemId: string | Types.ObjectId; quantity: number }[],
    deliveryAddress: string,
    paymentMethod: PaymentMethod = PaymentMethod.WAAFI_PREAUTH
  ) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      // Validate input
      if (!customerId) {
        throw new BadRequestError('Customer ID is required');
      }
      if (!items || !items.length) {
        throw new BadRequestError('Items are required');
      }
      if (!deliveryAddress) {
        throw new BadRequestError('Delivery address is required');
      }
      if (!paymentMethod) {
        throw new BadRequestError('Payment method is required');
      }

      // CRITICAL FIX: Validate customer INSIDE transaction with session
      const customer = await User.findById(customerId).session(session);
      if (!customer) {
        throw new NotFoundError('Customer not found', {
          code: 'CUSTOMER_NOT_FOUND',
          details: {
            customerId,
          },
        });
      }

      const existingOrder = await Order.findOne({
        customer: customerId,
        items: { $eq: items },
        status: { $in: [OrderStatus.PENDING, OrderStatus.CONFIRMED] },
      }).session(session);

      if (existingOrder) {
        throw new BadRequestError('Order already exists', {
          code: 'ORDER_EXISTS',
          details: {
            orderId: existingOrder._id.toString(),
          },
        });
      }

      // Validate items and check availability
      const validatedItems = await this.validateItems(items, { session });

      // Check inventory availability for all items
      await this.checkInventoryAvailability(validatedItems, session);

      // Calculate total amount
      const totalAmount = validatedItems.reduce((acc, item) => {
        return acc + item.price * item.quantity;
      }, 0);
      logger.info(`Total amount calculated: ${totalAmount}`);

      // Create payment record first WITH SESSION
      const payment = new Payment({
        orderId: null, // Will be set after order creation
        userId: customerId,
        method: paymentMethod,
        amount: totalAmount,
        status: PaymentStatus.PENDING,
        metadata: {
          deliveryDetails: {
            estimatedDeliveryTime: '30 minutes',
            deliveryAddress,
          },
        },
      });
      const savedPayment = await payment.save({ session });

      // Create order WITH SESSION and link to payment
      const order = new Order({
        customer: customerId,
        payment: savedPayment._id, // Link to payment
        items: validatedItems,
        totalAmount,
        status: OrderStatus.PENDING,
        deliveryAddress,
        paymentMethod,
      });
      const savedOrder = await order.save({ session });

      // Update payment with order ID
      await Payment.findByIdAndUpdate(savedPayment._id, { orderId: savedOrder._id }, { session });

      // Reserve inventory for all items
      await this.reserveInventoryForOrder(savedOrder._id.toString(), validatedItems, session);

      // Generate QR code
      const qrCode = generateQRPayload({ orderId: savedOrder._id.toString() });
      const qrCodeExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Update order WITH SESSION
      const updatedOrder = await Order.findByIdAndUpdate(
        savedOrder._id,
        { qrCode, qrCodeExpiresAt },
        { new: true, session }
      )
        .populate('customer')
        .populate('deliveryAgent');

      await session.commitTransaction();

      // Initiate payment
      let finalOrder = updatedOrder;
      if (paymentMethod === PaymentMethod.WAAFI_PREAUTH) {
        try {
          await paymentService.initiatePreauthorization(
            savedPayment._id.toString(),
            customer.phone,
            // totalAmount, for production
            0.01, // for testing
            {
              estimatedDeliveryTime: '30 minutes',
              deliveryAddress,
            }
          );

          // Payment successful - send success SMS
          try {
            await smsService.sendSms(customer.phone, 'Your order has been created successfully');
          } catch (smsError) {
            logger.error('Failed to send success SMS', {
              error: smsError.message,
              orderId: savedOrder._id.toString(),
              timestamp: new Date().toISOString(),
            });
          }
        } catch (error) {
          logger.error('Payment preauthorization failed', {
            orderId: savedOrder._id.toString(),
            error: error.message,
            errorType: typeof error,
            errorConstructor: error.constructor?.name,
            isAppError: error instanceof AppError,
            isPaymentError: error instanceof PaymentError,
            timestamp: new Date().toISOString(),
          });

          // Handle payment failure AFTER successful order creation
          const failedOrder = await this.handlePostCommitPaymentFailure(
            savedOrder._id.toString(),
            validatedItems,
            error.message
          );

          // Use failed order if available, otherwise keep original order
          finalOrder = failedOrder || updatedOrder;

          // Send failure SMS
          try {
            await smsService.sendSms(
              customer.phone,
              'Your order could not be processed due to payment failure. Please try again.'
            );
          } catch (smsError) {
            logger.error('Failed to send failure SMS', {
              error: smsError.message,
              orderId: savedOrder._id.toString(),
              timestamp: new Date().toISOString(),
            });
          }

          // Throw the original payment error to preserve the specific error message
          if (error instanceof AppError) {
            // If it's already an AppError (like WaaFiPaymentFailed), throw it directly
            throw error;
          } else {
            // If it's a generic error, wrap it with the original message
            throw new PaymentError(
              error.message || 'Payment preauthorization failed',
              400,
              'PAYMENT_FAILED',
              {
                orderId: savedOrder._id.toString(),
                details: { originalError: error.message },
              }
            );
          }
        }
      } else {
        // For non-payment methods, send success SMS
        try {
          await smsService.sendSms(customer.phone, 'Your order has been created successfully');
        } catch (smsError) {
          logger.error('Failed to send SMS', {
            error: smsError.message,
            orderId: savedOrder._id.toString(),
            timestamp: new Date().toISOString(),
          });
        }
      }

      return finalOrder;
    } catch (error) {
      logger.error('Order creation failed', {
        customerId,
        items,
        deliveryAddress,
        paymentMethod,
        error: error.message,
        errorType: typeof error,
        errorConstructor: error.constructor?.name,
        isAppError: error instanceof AppError,
        isPaymentError: error instanceof PaymentError,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // ENHANCED TRANSACTION FAILURE HANDLING
      try {
        // Check if transaction is still active before aborting
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Transaction aborted successfully', {
            customerId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort transaction', {
          error: abortError.message,
          originalError: error.message,
          customerId,
          timestamp: new Date().toISOString(),
        });
      }

      // Re-throw known errors, wrap unknown errors
      this.handleError(error);
    } finally {
      // SAFE SESSION CLEANUP
      try {
        if (!session.hasEnded) {
          await session.endSession();
          logger.debug('Session ended successfully', {
            customerId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (sessionError) {
        logger.error('Failed to end session', {
          error: sessionError.message,
          customerId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  private async validateItems(
    items: { itemType: string; itemId: string | Types.ObjectId; quantity: number }[],
    options?: { session?: ClientSession }
  ) {
    const validatedItems = [];

    for (const item of items) {
      if (!item.itemType || !item.itemId || !item.quantity) {
        throw new BadRequestError('Invalid item format', {
          code: 'INVALID_ITEM_FORMAT',
          details: { item },
        });
      }

      let itemDoc;
      switch (item.itemType) {
        case OrderItemType.Cylinder:
          itemDoc = await Cylinder.findById(item.itemId).session(options?.session || null);
          break;
        case OrderItemType.SparePart:
          itemDoc = await SparePart.findById(item.itemId).session(options?.session || null);
          break;
        case OrderItemType.Package:
          itemDoc = await Package.findById(item.itemId).session(options?.session || null);
          break;
        // Add cases for other item types
        default:
          throw new BadRequestError('Invalid item type', {
            code: 'INVALID_ITEM_TYPE',
            details: { itemType: item.itemType },
          });
      }

      if (!itemDoc) {
        throw new NotFoundError('Item not found', {
          code: 'ITEM_NOT_FOUND',
          details: { itemId: item.itemId, itemType: item.itemType },
        });
      }
      //   if (itemDoc.stock < item.quantity) {
      //     throw new BadRequestError('Insufficient stock', {
      //       code: 'INSUFFICIENT_STOCK',
      //       details: { itemId: item.itemId, itemType: item.itemType, available: itemDoc.stock, requested: item.quantity },
      //     });
      //   }

      validatedItems.push({
        itemType: item.itemType,
        itemId: item.itemId,
        quantity: item.quantity,
        price: (itemDoc as any).price,
      });
    }

    // CRITICAL FIX: Do NOT end the session here - it's still needed for the transaction
    // The session will be ended in the finally block of the calling method

    return validatedItems;
  }

  public async assignAgentToOrder(
    orderId: string | Types.ObjectId,
    agentId: string | Types.ObjectId
  ) {
    try {
      const order = await Order.findById(orderId);
      if (!order) {
        throw new NotFoundError('Order not found', {
          code: 'ORDER_NOT_FOUND',
          details: {
            orderId,
          },
        });
      }
      if (order.status !== OrderStatus.PENDING && order.status !== OrderStatus.CONFIRMED) {
        // throw new BadRequestError('Order is not pending', {
        throw new BadRequestError('Order is not pending or confirmed', {
          code: 'ORDER_NOT_PENDING',
          details: {
            orderId,
            currentStatus: order.status,
          },
        });
      }

      const agent = await User.findById(agentId);
      if (!agent) {
        throw new NotFoundError('Agent not found', {
          code: 'AGENT_NOT_FOUND',
          details: {
            agentId,
          },
        });
      }
      if (agent.role !== UserRole.AGENT) {
        throw new BadRequestError('User is not an agent', {
          code: 'USER_NOT_AGENT',
          details: {
            agentId,
            role: agent.role,
          },
        });
      }

      const updatedOrder = await Order.findByIdAndUpdate(
        orderId,
        { deliveryAgent: agentId, status: OrderStatus.OUT_FOR_DELIVERY, AssignedAt: new Date() },
        { new: true }
      );

      return updatedOrder;
    } catch (error) {
      logger.error('Failed to assign agent to order', {
        orderId,
        agentId,
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      // Re-throw known errors, wrap unknown errors
      this.handleError(error);
    }
  }

  public async cancelOrder(orderId: string | Types.ObjectId) {
    return this.executeWithRetry(async () => {
      return this.cancelOrderTransaction(orderId);
    });
  }

  private async cancelOrderTransaction(orderId: string | Types.ObjectId) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      const order = await Order.findById(orderId).session(session);
      if (!order) {
        throw new NotFoundError('Order not found', {
          code: 'ORDER_NOT_FOUND',
          details: {
            orderId,
          },
        });
      }
      if (order.status !== OrderStatus.PENDING && order.status !== OrderStatus.OUT_FOR_DELIVERY) {
        throw new BadRequestError('Order cannot be cancelled', {
          code: 'ORDER_CANNOT_BE_CANCELLED',
          details: {
            orderId,
            currentStatus: order.status,
          },
        });
      }

      // Release reserved inventory
      await this.releaseInventoryForOrder(orderId.toString(), order.items as any, session);

      // Cancel payment
      if (order.paymentMethod === PaymentMethod.WAAFI_PREAUTH && order.payment) {
        try {
          await paymentService.cancelPreauthorization(order.payment.toString());
        } catch (error) {
          logger.error('Failed to cancel payment', {
            orderId,
            paymentId: order.payment.toString(),
            error: error.message,
            timestamp: new Date().toISOString(),
          });
          // Don't throw error here - we still want to cancel the order even if payment cancellation fails
        }
      }

      const updatedOrder = await Order.findByIdAndUpdate(
        orderId,
        { status: OrderStatus.CANCELLED, cancelledAt: new Date() },
        { new: true, session }
      );

      // check updatedOrder payment status
      if (updatedOrder.paymentMethod === PaymentMethod.WAAFI_PREAUTH && updatedOrder.payment) {
        const payment = await Payment.findById(updatedOrder.payment);
        if (!payment) {
          logger.warn('Payment not found for cancelled order', {
            orderId: updatedOrder._id.toString(),
            paymentId: updatedOrder.payment.toString(),
            timestamp: new Date().toISOString(),
          });
        } else if (payment.status !== PaymentStatus.CANCELLED) {
          logger.warn('Payment was not cancelled successfully', {
            orderId: updatedOrder._id.toString(),
            paymentId: payment._id.toString(),
            paymentStatus: payment.status,
            timestamp: new Date().toISOString(),
          });
          // Don't throw error - order cancellation should succeed even if payment cancellation fails
        }
      }

      await session.commitTransaction();
      return updatedOrder;
    } catch (error) {
      logger.error('Failed to cancel order', {
        orderId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // ENHANCED TRANSACTION FAILURE HANDLING
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Cancel order transaction aborted successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort cancel order transaction', {
          error: abortError.message,
          originalError: error.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }

      // Re-throw known errors, wrap unknown errors
      this.handleError(error);
    } finally {
      // SAFE SESSION CLEANUP
      try {
        if (!session.hasEnded) {
          await session.endSession();
          logger.debug('Cancel order session ended successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (sessionError) {
        logger.error('Failed to end cancel order session', {
          error: sessionError.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  // complete or deliver order
  public async completeOrder(orderId: string | Types.ObjectId) {
    return this.executeWithRetry(async () => {
      return this.completeOrderTransaction(orderId);
    });
  }

  private async completeOrderTransaction(orderId: string | Types.ObjectId) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      const order = await Order.findById(orderId).session(session);
      if (!order) {
        throw new NotFoundError('Order not found', {
          code: 'ORDER_NOT_FOUND',
          details: {
            orderId,
          },
        });
      }
      if (order.status !== OrderStatus.OUT_FOR_DELIVERY) {
        throw new BadRequestError('Order is not assigned', {
          code: 'ORDER_NOT_ASSIGNED',
          details: {
            orderId,
            currentStatus: order.status,
          },
        });
      }

      // Mark inventory items as sold
      await this.markInventoryAsSold(order.items as any, session);

      // Capture payment
      if (order.paymentMethod === PaymentMethod.WAAFI_PREAUTH && order.payment) {
        await paymentService.capturePreauthorizedPayment(order.payment.toString());
      }

      const updatedOrder = await Order.findByIdAndUpdate(
        orderId,
        { status: OrderStatus.DELIVERED, deliveredAt: new Date() },
        { new: true, session }
      );

      // check updatedOrder payment status
      if (updatedOrder.paymentMethod === PaymentMethod.WAAFI_PREAUTH && updatedOrder.payment) {
        const payment = await Payment.findById(updatedOrder.payment);
        if (!payment) {
          throw new NotFoundError('Payment not found', {
            code: 'PAYMENT_NOT_FOUND',
            details: {
              paymentId: updatedOrder.payment.toString(),
            },
          });
        }
        if (payment.status !== PaymentStatus.CAPTURED) {
          throw new PaymentError(
            `Payment is not captured, status is ${payment.status}`,
            400,
            'PAYMENT_NOT_CAPTURED',
            {
              paymentId: payment._id.toString(),
              orderId: payment.orderId.toString(),
              details: {
                currentStatus: payment.status,
              },
            }
          );
        }
      }

      await session.commitTransaction();
      return updatedOrder;
    } catch (error) {
      logger.error('Failed to complete order', {
        orderId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // ENHANCED TRANSACTION FAILURE HANDLING
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Complete order transaction aborted successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort complete order transaction', {
          error: abortError.message,
          originalError: error.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }

      // Re-throw known errors, wrap unknown errors
      // if (error instanceof AppError) {
      //   throw error;
      // }

      // throw new InternalServerError('Failed to complete order', {
      //   code: 'COMPLETE_ORDER_FAILED',
      //   details: { originalError: error.message },
      // });
      this.handleError(error);
    } finally {
      // SAFE SESSION CLEANUP
      try {
        if (!session.hasEnded) {
          await session.endSession();
          logger.debug('Complete order session ended successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (sessionError) {
        logger.error('Failed to end complete order session', {
          error: sessionError.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  // Validate order in qr code data
  public async validateOrderInQRCode(qrCode: string) {
    try {
      const { success, orderId, expiresAt, message } = validateQRPayload(qrCode);

      if (!success) {
        throw new BadRequestError(message, {
          code: 'INVALID_QR_CODE',
          details: {
            message,
          },
        });
      }

      if (!orderId) {
        throw new BadRequestError('Missing orderId in token', {
          code: 'MISSING_ORDER_ID',
          details: {
            message,
          },
        });
      }

      if (expiresAt && expiresAt < new Date()) {
        throw new BadRequestError('QR code has expired', {
          code: 'QR_CODE_EXPIRED',
          details: {
            expiresAt,
          },
        });
      }

      const order = await Order.findById(orderId)
        .populate('customer', 'phone email')
        .populate('deliveryAgent', 'phone email');
      if (!order) {
        throw new NotFoundError('Order not found', {
          code: 'ORDER_NOT_FOUND',
          details: {
            orderId,
          },
        });
      }

      return order;
    } catch (error) {
      logger.error('Failed to validate order in QR code', {
        qrCode,
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      // Re-throw known errors, wrap unknown errors
      this.handleError(error);
    }
  }

  // ==================== INVENTORY MANAGEMENT METHODS ====================

  /**
   * Check inventory availability for all items in the order
   * @param items - Validated order items
   * @param session - MongoDB session for transaction
   * @throws {BadRequestError} If any item has insufficient stock
   */
  private async checkInventoryAvailability(
    items: { itemType: string; itemId: string | Types.ObjectId; quantity: number; price: number }[],
    session: ClientSession
  ): Promise<void> {
    for (const item of items) {
      try {
        switch (item.itemType) {
          case OrderItemType.Cylinder:
            const cylinderAvailability = await cylinderService.checkAvailability(
              item.itemId.toString(),
              item.quantity
            );
            if (!cylinderAvailability.available) {
              throw new BadRequestError('Insufficient cylinder stock', {
                code: 'INSUFFICIENT_CYLINDER_STOCK',
                details: {
                  itemId: item.itemId,
                  requested: item.quantity,
                  available: cylinderAvailability.availableQuantity,
                },
              });
            }
            break;

          case OrderItemType.Package:
            const packageAvailability = await packageService.checkPackageAvailability(
              item.itemId.toString(),
              item.quantity
            );
            if (!packageAvailability.available) {
              throw new BadRequestError('Insufficient package stock', {
                code: 'INSUFFICIENT_PACKAGE_STOCK',
                details: {
                  itemId: item.itemId,
                  requested: item.quantity,
                  available: packageAvailability.availableQuantity,
                },
              });
            }
            break;

          case OrderItemType.SparePart:
            const sparePartDoc = await SparePart.findById(item.itemId).session(session);
            if (!sparePartDoc) {
              throw new NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
                details: { itemId: item.itemId },
              });
            }
            const availableQuantity = Math.max(
              0,
              (sparePartDoc as any).quantity - (sparePartDoc as any).reserved
            );
            if (availableQuantity < item.quantity) {
              throw new BadRequestError('Insufficient spare part stock', {
                code: 'INSUFFICIENT_SPARE_PART_STOCK',
                details: {
                  itemId: item.itemId,
                  requested: item.quantity,
                  available: availableQuantity,
                },
              });
            }
            break;

          default:
            throw new BadRequestError('Invalid item type for availability check', {
              code: 'INVALID_ITEM_TYPE',
              details: { itemType: item.itemType },
            });
        }
      } catch (error) {
        logger.error('Inventory availability check failed', {
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
        // throw error;
        this.handleError(error);
      }
    }
  }

  /**
   * Reserve inventory for all items in an order
   * @param orderId - The order ID for tracking
   * @param items - Order items to reserve
   * @param session - MongoDB session for transaction
   */
  private async reserveInventoryForOrder(
    orderId: string,
    items: { itemType: string; itemId: string | Types.ObjectId; quantity: number; price: number }[],
    session: ClientSession
  ): Promise<void> {
    for (const item of items) {
      try {
        switch (item.itemType) {
          case OrderItemType.Cylinder:
            await cylinderService.reserveCylinder(item.itemId.toString(), item.quantity, session);
            break;

          case OrderItemType.Package:
            await packageService.reservePackage(
              item.itemId.toString(),
              item.quantity,
              orderId,
              session
            );
            break;

          case OrderItemType.SparePart:
            await sparePartService.reserve(item.itemId.toString(), item.quantity, session);
            break;

          default:
            throw new BadRequestError('Invalid item type for reservation', {
              code: 'INVALID_ITEM_TYPE',
              details: { itemType: item.itemType },
            });
        }

        logger.info('Inventory reserved for order item', {
          orderId,
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Inventory reservation failed', {
          orderId,
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
        // throw error;
        this.handleError(error);
      }
    }
  }

  /**
   * Release reserved inventory for all items in an order (when order is cancelled)
   * @param orderId - The order ID for tracking
   * @param items - Order items to release
   * @param session - MongoDB session for transaction
   */
  private async releaseInventoryForOrder(
    orderId: string,
    items: { itemType: OrderItemType; itemId: string | Types.ObjectId; quantity: number }[],
    session: ClientSession
  ): Promise<void> {
    for (const item of items) {
      try {
        switch (item.itemType) {
          case OrderItemType.Cylinder:
            await cylinderService.releaseReservation(
              item.itemId.toString(),
              item.quantity,
              session
            );
            break;

          case OrderItemType.Package:
            await packageService.releaseReservation(
              item.itemId.toString(),
              item.quantity,
              orderId,
              session
            );
            break;

          case OrderItemType.SparePart:
            await sparePartService.release(item.itemId.toString(), item.quantity, session);
            break;

          default:
            throw new BadRequestError('Invalid item type for release', {
              code: 'INVALID_ITEM_TYPE',
              details: { itemType: item.itemType },
            });
        }

        logger.info('Inventory reservation released for order item', {
          orderId,
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Inventory reservation release failed', {
          orderId,
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
        // throw error;
        this.handleError(error);
      }
    }
  }

  /**
   * Mark inventory items as sold (when order is completed/delivered)
   * @param items - Order items to mark as sold
   * @param session - MongoDB session for transaction
   */
  private async markInventoryAsSold(
    items: { itemType: OrderItemType; itemId: string | Types.ObjectId; quantity: number }[],
    session: ClientSession
  ): Promise<void> {
    for (const item of items) {
      try {
        switch (item.itemType) {
          case OrderItemType.Cylinder:
            await cylinderService.markAsSold(item.itemId.toString(), item.quantity, session);
            break;

          case OrderItemType.Package:
            await packageService.markPackageAsSold(item.itemId.toString(), item.quantity, session);
            break;

          case OrderItemType.SparePart:
            await sparePartService.markAsSold(item.itemId.toString(), item.quantity, session);
            break;

          default:
            throw new BadRequestError('Invalid item type for sale', {
              code: 'INVALID_ITEM_TYPE',
              details: { itemType: item.itemType },
            });
        }

        logger.info('Inventory marked as sold for order item', {
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Inventory sale marking failed', {
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
        // throw error;
        this.handleError(error);
      }
    }
  }

  /**
   * Handle Payment errors by marking order as failed and releasing inventory
   * @param orderId - The order ID for tracking
   * @param items - Order items to release
   * @param session - MongoDB session for transaction
   */
  private async handlePostCommitPaymentFailure(
    orderId: string,
    items: { itemType: OrderItemType; itemId: string | Types.ObjectId; quantity: number }[],
    errorMessage: string
  ): Promise<any> {
    const newSession = await mongoose.startSession();
    try {
      newSession.startTransaction();

      const failedOrder = await Order.findByIdAndUpdate(
        orderId,
        {
          status: OrderStatus.FAILED,
          failedAt: new Date(),
          failureReason: errorMessage,
        },
        { new: true, session: newSession }
      );

      await this.releaseInventoryForOrder(orderId, items, newSession);
      await newSession.commitTransaction();

      logger.info('Payment failure handled successfully', {
        orderId,
        errorMessage,
        timestamp: new Date().toISOString(),
      });

      return failedOrder;
    } catch (error) {
      logger.error('Failed to handle payment failure', {
        orderId,
        error: error.message,
        stack: error.stack,
        originalPaymentError: errorMessage,
        timestamp: new Date().toISOString(),
      });

      // ENHANCED TRANSACTION FAILURE HANDLING
      try {
        if (newSession.inTransaction()) {
          await newSession.abortTransaction();
          logger.info('Payment failure handling transaction aborted successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort payment failure handling transaction', {
          error: abortError.message,
          originalError: error.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }

      // Return null to indicate failure handling failed
      return null;
    } finally {
      // SAFE SESSION CLEANUP
      try {
        if (!newSession.hasEnded) {
          await newSession.endSession();
          logger.debug('Payment failure handling session ended successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (sessionError) {
        logger.error('Failed to end payment failure handling session', {
          error: sessionError.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  public async updateOrder(
    orderId: string,
    customerId?: string,
    items?: { itemType: string; itemId: string | Types.ObjectId; quantity: number }[],
    deliveryAddress?: string,
    paymentMethod?: PaymentMethod
  ): Promise<void> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      const order = await Order.findById(orderId).session(session);
      if (!order) {
        throw new NotFoundError('Order not found', {
          code: 'ORDER_NOT_FOUND',
          details: {
            orderId,
          },
        });
      }

      // Update customer
      if (customerId) {
        const customer = await User.findById(customerId).session(session);
        if (!customer) {
          throw new NotFoundError('Customer not found', {
            code: 'CUSTOMER_NOT_FOUND',
            details: {
              customerId,
            },
          });
        }
        order.customer = customer._id.toString();
      }

      // Update items
      if (items) {
        // Validate items and check availability
        const validatedItems = await this.validateItems(items, { session });
        // Check inventory availability for all items
        await this.checkInventoryAvailability(validatedItems, session);
        // Update order items
        // order.items = validatedItems;
        order.items = [
          ...validatedItems.map(item => ({
            itemType: item.itemType,
            itemId: item.itemId.toString(),
            quantity: item.quantity,
          })),
        ];
      }

      // Update delivery address
      if (deliveryAddress) {
        order.deliveryAddress = deliveryAddress;
      }

      // Update payment method
      if (paymentMethod) {
        order.paymentMethod = paymentMethod;
      }

      // Save order
      await order.save({ session });

      await session.commitTransaction();
    } catch (error) {
      logger.error('Failed to update order', {
        orderId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // ENHANCED TRANSACTION FAILURE HANDLING
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Update order transaction aborted successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort update order transaction', {
          error: abortError.message,
          originalError: error.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }

      // Re-throw known errors, wrap unknown errors
      // if (error instanceof AppError) {
      //   throw error;
      // }

      // throw new InternalServerError('Failed to update order', {
      //   code: 'UPDATE_ORDER_FAILED',
      //   details: { originalError: error.message },
      // });
      this.handleError(error);
    } finally {
      // SAFE SESSION CLEANUP
      try {
        if (!session.hasEnded) {
          await session.endSession();
          logger.debug('Update order session ended successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (sessionError) {
        logger.error('Failed to end update order session', {
          error: sessionError.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  public async deleteOrder(orderId: string): Promise<void> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      const order = await Order.findById(orderId).session(session);
      if (!order) {
        throw new NotFoundError('Order not found', {
          code: 'ORDER_NOT_FOUND',
          details: {
            orderId,
          },
        });
      }

      await Order.findByIdAndDelete(orderId).session(session);

      await session.commitTransaction();
    } catch (error) {
      logger.error('Failed to delete order', {
        orderId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // ENHANCED TRANSACTION FAILURE HANDLING
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Delete order transaction aborted successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort delete order transaction', {
          error: abortError.message,
          originalError: error.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }

      // Re-throw known errors, wrap unknown errors
      // if (error instanceof AppError) {
      //   throw error;
      // }

      // throw new InternalServerError('Failed to delete order', {
      //   code: 'DELETE_ORDER_FAILED',
      //   details: { originalError: error.message },
      // });
      this.handleError(error);
    } finally {
      // SAFE SESSION CLEANUP
      try {
        if (!session.hasEnded) {
          await session.endSession();
          logger.debug('Delete order session ended successfully', {
            orderId,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (sessionError) {
        logger.error('Failed to end delete order session', {
          error: sessionError.message,
          orderId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  /// -----------   Get Orders  ----------- ///

  public async getOrders(
    // filters: {
    //   customer?: string | Types.ObjectId;
    //   status?: OrderStatus;
    //   paymentMethod?: PaymentMethod;
    //   deliveryAgent?: string | Types.ObjectId;
    // } = {}
    filters: {
      customer?: string | Types.ObjectId;
      status?: OrderStatus;
      paymentMethod?: PaymentMethod;
      deliveryAgent?: string | Types.ObjectId;
      startDate?: Date;
      endDate?: Date;
      page?: number;
      limit?: number;
    } = {},
    requestingUser: {
      userId: string | Types.ObjectId;
      role: UserRole;
    }
  ) {
    try {
      // const query: any = {};
      // if (filters.customer) query.customer = filters.customer;
      // if (filters.status) query.status = filters.status;
      // if (filters.paymentMethod) query.paymentMethod = filters.paymentMethod;
      // if (filters.deliveryAgent) query.deliveryAgent = filters.deliveryAgent;

      // const orders = await Order.find(query).populate('customer deliveryAgent');

      const DEFAULT_PAGE = 1;
      const DEFAULT_LIMIT = 20;
      const MAX_LIMIT = 100;

      const page = Math.max(filters.page || DEFAULT_PAGE, 1);
      const limit = Math.min(filters.limit || DEFAULT_LIMIT, MAX_LIMIT);
      const skip = (page - 1) * limit;

      const query = this.buildQuery(filters, requestingUser);
      const orders = await Order.find(query)
        .populate('customer deliveryAgent')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      return orders;
    } catch (error) {
      // logger.error('Failed to get orders', {
      //   filters,
      //   error: error.message,
      //   timestamp: new Date().toISOString(),
      // });

      // // Re-throw known errors, wrap unknown errors
      // if (error instanceof AppError) {
      //   throw error;
      // }

      // throw new InternalServerError('Failed to get orders', {
      //   code: 'GET_ORDERS_FAILED',
      //   details: { originalError: error.message },
      // });
      this.handleError(error);
    }
  }

  private buildQuery(filters: any, user: { userId: string | Types.ObjectId; role: UserRole }) {
    const query: any = {};

    // Customer restrictions
    if (user.role === UserRole.CUSTOMER) {
      const customerId = filters.customer ?? user.userId;
      if (customerId.toString() !== user.userId.toString()) {
        throw new UnauthorizedError('Customers can only view their own orders');
      }
      query.customer = customerId;

      // Status restriction
      if (filters.status && filters.status === OrderStatus.FAILED) {
        throw new ValidationError('Customers cannot view failed orders', {
          code: 'INVALID_STATUS_FILTER',
        });
      }

      query.status = filters.status ?? { $nin: [OrderStatus.FAILED, OrderStatus.CANCELLED] };
    }

    // Agent restrictions
    else if (user.role === UserRole.AGENT) {
      const agentId = filters.deliveryAgent ?? user.userId;
      if (agentId.toString() !== user.userId.toString()) {
        throw new UnauthorizedError('Agents can only view their own assigned orders');
      }
      query.deliveryAgent = agentId;

      const allowedStatuses = [
        OrderStatus.CONFIRMED,
        OrderStatus.OUT_FOR_DELIVERY,
        OrderStatus.DELIVERED,
      ];
      if (filters.status) {
        if (!allowedStatuses.includes(filters.status)) {
          throw new ValidationError(
            'Agents can only view CONFIRMED, OUT_FOR_DELIVERY, or DELIVERED orders',
            {
              code: 'INVALID_STATUS_FILTER',
            }
          );
        }
        query.status = filters.status;
      } else {
        query.status = { $in: allowedStatuses };
      }
    }

    // Admin
    else if (user.role === UserRole.ADMIN) {
      if (filters.customer) query.customer = filters.customer;
      if (filters.deliveryAgent) query.deliveryAgent = filters.deliveryAgent;
      if (filters.status) query.status = filters.status;
    }

    // Common to all roles
    if (filters.paymentMethod) {
      query.paymentMethod = filters.paymentMethod;
    }

    return query;
  }

  // handle error
  private handleError(error: any) {
    // logger.error(message, {
    //   error: error.message,
    //   stack: error.stack,
    //   timestamp: new Date().toISOString(),
    // });

    // if (error instanceof AppError) {
    //   throw error;
    // }

    // throw new InternalServerError(message, {
    //   code,
    //   details: { originalError: error.message },
    // });
    throw error;
  }
}

export const orderService = new OrderService();
