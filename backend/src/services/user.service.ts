import mongoose, { Types } from 'mongoose';
import { User, TempOtp } from '../models';
import { IUser, TokenPayload } from '../types/interfaces';
import { UserRole, VehicleType } from '../enums/enums';
import { hashPassword } from '../utils/hash_utils';
import { generateToken } from '../utils/jwt_utils';
import {
  NotFoundError,
  ValidationError,
  UnauthorizedError,
  DuplicateResourceError,
  BadRequestError,
  InternalServerError,
} from '../errors/app_errors';
import {
  subscribeToTopic,
  unsubscribeFromTopic,
  NotificationTopic,
} from '../utils/notification_utils';
import { smsService } from './sms.services';
import logger from '../config/logger';
import { generateOtp } from '../utils/otp_utils';
import { appConstants } from '../constants/app_constants';
import { config } from '../config/env_config';

class UserServices {
  private shouldSkipOtp(): boolean {
    return config.server.disableOtpVerification;
  }

  // private utility function to send OTP
  private async sendOtp(phone: string, otp: string) {
    try {
      // await smsService.sendSms(phone, `Your OTP is ${otp}`, { isOtp: true });
      await smsService.sendSms(phone, appConstants.otpMessage(otp), { isOtp: true });
    } catch (error) {
      logger.error('Failed to send OTP SMS', {
        error: error.message,
        phone,
        timestamp: new Date().toISOString(),
      });
      // throw new InternalServerError('Failed to send OTP SMS', {
      // throw new InternalServerError('Internal server error', {
      //   code: 'OTP_SMS_FAILED',
      //   details: { originalError: error.message },
      // });
    }
  }

  async login(phone: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      logger.info(`Login attempt for phone: ${phone}`);

      // const { code: otp, expirationTime } = generateOtp();
      // Generate OTP or use dummy value if skipping verification
      const { code: otp, expirationTime } = this.shouldSkipOtp()
        ? { code: '000000', expirationTime: new Date(Date.now() + 10 * 60 * 1000) }
        : generateOtp();

      logger.info(`Generated OTP: ${otp}, Expires at: ${expirationTime}`);

      // Check if user exists first
      const user = await User.findOne({ phone }).session(session);
      logger.info(`Existing user found: ${user ? 'Yes' : 'No'}`);

      if (!user) {
        // Clean up any existing TempOtp for this phone
        await TempOtp.deleteMany({ phone }).session(session);

        // Store OTP in temporary collection
        const newTempUser = await TempOtp.create(
          [
            {
              phone,
              code: otp,
              expirationTime,
              createdAt: new Date(),
            },
          ],
          { session }
        );

        logger.info(`TempOtp created with ID: ${newTempUser[0]._id}`);

        // Attempt to send SMS
        await this.sendOtp(phone, otp);

        await session.commitTransaction();
        logger.info('Transaction committed for new user');

        return {
          isNewUser: true,
          _id: newTempUser[0]._id,
          phone: newTempUser[0].phone,
        };
      }

      if (!user.isActive) {
        throw new UnauthorizedError('Your account has been deactivated');
      }

      logger.info(`Updating existing user OTP...`);
      await User.updateOne(
        { phone },
        { $set: { otp: { code: otp, expirationTime } } },
        { session }
      );

      // Attempt to send SMS - if this fails, the transaction will roll back
      if (!this.shouldSkipOtp()) {
        await this.sendOtp(phone, otp);
      }

      await session.commitTransaction();
      logger.info('Transaction committed for existing user');

      return {
        isNewUser: false,
        _id: user._id,
        phone: user.phone,
        email: user.email,
        role: user.role,
        addresses: user.addresses,
        isActive: user.isActive,
        agentMetadata: user.agentMetadata,
      };
    } catch (error) {
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Login transaction aborted successfully', {
            phone,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort login transaction', {
          error: abortError.message,
          originalError: error.message,
          phone,
          timestamp: new Date().toISOString(),
        });
      }

      logger.error('Login transaction aborted', {
        phone,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      session.endSession();
    }
  }

  async verifyOtp(phone: string, otp: string) {
    logger.info(`Verifying OTP for phone: ${phone}`);
    const now = new Date();

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // STEP 1: Check for new user in TempOtp
      const tempOtp = await TempOtp.findOne({
        phone,
        code: otp,
        expirationTime: { $gt: now },
      }).session(session);

      if (tempOtp) {
        logger.info(`Valid TempOtp found. Creating new user.`);

        const newUser = await User.create(
          [
            {
              phone,
              role: UserRole.CUSTOMER,
              isActive: true,
              addresses: [],
            },
          ],
          { session }
        );

        await TempOtp.deleteOne({ _id: tempOtp._id }).session(session);

        await session.commitTransaction();

        const token = generateToken(newUser[0]._id.toString(), newUser[0].role);
        logger.info(`New user created: ${newUser[0]._id}`);
        return { token, user: newUser[0] };
      }

      // STEP 2: Check existing user
      const user = await User.findOne({ phone }).session(session);

      if (!user) {
        const expiredTempOtp = await TempOtp.findOne({ phone }).session(session);
        if (expiredTempOtp) {
          logger.warn(`OTP expired for unregistered user`);
          throw new UnauthorizedError('OTP has expired. Please request a new one.');
        }

        logger.warn(`No user found with phone: ${phone}`);
        throw new NotFoundError('User not found. Please register first.');
      }

      // STEP 3: Validate OTP on existing user
      if (!user.otp || !user.otp.code) {
        logger.warn(`No OTP set for user: ${user._id}`);
        throw new UnauthorizedError('OTP not found. Please request a new one.');
      }

      if (user.otp.code !== otp) {
        logger.warn(`Incorrect OTP entered for user: ${user._id}`);
        throw new UnauthorizedError('Incorrect OTP. Please try again.');
      }

      if (user.otp.expirationTime <= now) {
        logger.warn(`Expired OTP entered for user: ${user._id}`);
        throw new UnauthorizedError('OTP has expired. Please request a new one.');
      }

      await User.updateOne({ phone }, { $set: { otp: null } }).session(session);

      await session.commitTransaction();

      const token = generateToken(user._id.toString(), user.role);
      logger.info(`User verified successfully: ${user._id}`);
      return { token, user };
    } catch (error) {
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Transaction aborted due to OTP verification failure', {
            phone,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Failed to abort transaction after OTP verification failure', {
          error: abortError.message,
          originalError: error.message,
          phone,
          timestamp: new Date().toISOString(),
        });
      }
      logger.error('OTP verification failed', {
        phone,
        otp,
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      if (!session.hasEnded) {
        await session.endSession();
      }
    }
  }

  async resendOtp(phone: string) {
    logger.info(`Resend OTP requested for phone: ${phone}`);

    const user = await User.findOne({ phone }).select('otp phone');
    if (!user) {
      logger.warn(`No user found with phone: ${phone}`);
      throw new NotFoundError('User not found with this phone number');
    }

    const now = new Date();

    // Check if existing OTP is still valid
    if (user.otp?.expirationTime && user.otp.expirationTime > now) {
      const secondsLeft = Math.ceil((user.otp.expirationTime.getTime() - now.getTime()) / 1000);
      logger.warn(`OTP already sent for ${phone}, expires in ${secondsLeft}s`);
      throw new BadRequestError(
        `OTP already sent. Please wait ${secondsLeft} seconds before requesting a new one.`
      );
    }

    // Generate a secure 6-digit OTP
    // const { code: otp, expirationTime } = generateOtp();
    // Generate OTP or use dummy value if skipping verification
    const { code: otp, expirationTime } = this.shouldSkipOtp()
      ? { code: '000000', expirationTime: new Date(Date.now() + 10 * 60 * 1000) }
      : generateOtp();
    // const expirationTime = new Date(now.getTime() + 2 * 60 * 1000); // 2 minutes

    // Send OTP via SMS
    if (!this.shouldSkipOtp()) {
      await this.sendOtp(phone, otp);
    }

    // Update user's OTP in one atomic operation
    await User.updateOne(
      { phone },
      { $set: { 'otp.code': otp, 'otp.expirationTime': expirationTime } }
    );

    // In production, don't return the OTP — only for testing or debugging
    return { message: 'OTP sent successfully' };
  }

  /**
   * Register a new admin or agent user (admin only)
   */
  async registerAdminOrAgent(
    userData: {
      phone: string;
      email?: string;
      password: string;
      role: UserRole.ADMIN | UserRole.AGENT;
      agentMetadata?: {
        vehicle: {
          type: VehicleType;
          number: string;
        };
      };
    },
    currentUser: TokenPayload
  ) {
    // Verify current user is admin
    if (currentUser.role !== UserRole.ADMIN) {
      throw new UnauthorizedError('Only admins can register new admins or agents');
    }

    // Check if user already exists
    const existingUser = await User.findOne({ phone: userData.phone });
    if (existingUser) {
      throw new DuplicateResourceError('User with this phone number already exists');
    }

    // Hash password
    const passwordHash = await hashPassword(userData.password);

    // Prepare user data
    const newUserData: Partial<IUser> = {
      phone: userData.phone,
      email: userData.email,
      passwordHash,
      role: userData.role,
      isActive: true,
    };

    // Add agent metadata if role is agent
    if (userData.role === UserRole.AGENT && userData.agentMetadata) {
      newUserData.agentMetadata = {
        vehicle: {
          type: userData.agentMetadata.vehicle.type,
          number: userData.agentMetadata.vehicle.number,
        },
        isOnDuty: false,
        lastKnownLocation: {
          type: 'Point',
          coordinates: [0, 0], // Default coordinates
        },
        rating: 0,
      } as any;
    }

    // Create new user
    const newUser = await User.create(newUserData);

    return {
      _id: newUser._id,
      phone: newUser.phone,
      email: newUser.email,
      role: newUser.role,
      isActive: newUser.isActive,
      agentMetadata: newUser.agentMetadata,
    };
  }

  /**
   * Get single user data
   */
  async getSingleUser(userId: string | Types.ObjectId, currentUser: TokenPayload) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only access their own data unless they're admins
    if (currentUser.role !== UserRole.ADMIN && currentUser.userId !== userId.toString()) {
      throw new UnauthorizedError('You can only access your own user data');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    return {
      _id: user._id,
      phone: user.phone,
      email: user.email,
      role: user.role,
      addresses: user.addresses,
      isActive: user.isActive,
      agentMetadata: user.agentMetadata,
    };
  }

  /**
   * Get all users data (admin only)
   */
  async getAllUsers(
    currentUser: TokenPayload,
    options: {
      role?: UserRole;
      isActive?: boolean;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ) {
    // Verify current user is admin
    if (currentUser.role !== UserRole.ADMIN) {
      throw new UnauthorizedError('Only admins can access all users data');
    }

    // Build query
    const query: any = {};
    if (options.role) query.role = options.role;
    if (options.isActive !== undefined) query.isActive = options.isActive;

    // Pagination
    const page = options.page || 1;
    const limit = options.limit || 10;
    const skip = (page - 1) * limit;

    // Sorting
    const sortOptions: any = {};
    if (options.sortBy) {
      sortOptions[options.sortBy] = options.sortOrder === 'desc' ? -1 : 1;
    } else {
      sortOptions.createdAt = -1; // Default sort by creation date, newest first
    }

    // Execute query
    const users = await User.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .select('-passwordHash');

    // Get total count for pagination
    const totalUsers = await User.countDocuments(query);

    return {
      users,
      pagination: {
        total: totalUsers,
        page,
        limit,
        pages: Math.ceil(totalUsers / limit),
      },
    };
  }

  /**
   * Update user data
   */
  async updateUser(
    userId: string | Types.ObjectId,
    updateData: {
      email?: string;
      password?: string;
      addresses?: Array<{
        tag?: string;
        coordinates: [number, number];
        details: string;
        contactPhone?: string;
      }>;
      isActive?: boolean;
      agentMetadata?: {
        vehicle?: {
          type?: string;
          number?: string;
        };
        isOnDuty?: boolean;
        lastKnownLocation?: {
          coordinates: [number, number];
        };
      };
    },
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only update their own data unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only update your own user data');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prepare update data
    const updateFields: any = {};

    // Basic fields
    if (updateData.email) updateFields.email = updateData.email;

    // Password update
    if (updateData.password) {
      updateFields.passwordHash = await hashPassword(updateData.password);
    }

    // Addresses update
    if (updateData.addresses && updateData.addresses.length > 0) {
      updateFields.addresses = updateData.addresses.map(addr => ({
        tag: addr.tag || 'home',
        location: {
          type: 'Point',
          coordinates: addr.coordinates,
        },
        details: addr.details,
        contactPhone: addr.contactPhone || user.phone,
      }));
    }

    // Active status (admin only)
    if (isAdmin && updateData.isActive !== undefined) {
      updateFields.isActive = updateData.isActive;
    }

    // Agent metadata updates
    if (user.role === UserRole.AGENT && updateData.agentMetadata) {
      // Vehicle updates (admin only)
      if (isAdmin && updateData.agentMetadata.vehicle) {
        updateFields['agentMetadata.vehicle'] = {
          ...user.agentMetadata?.vehicle,
          ...updateData.agentMetadata.vehicle,
        };
      }

      // Duty status (can be updated by the agent or admin)
      if (updateData.agentMetadata.isOnDuty !== undefined) {
        updateFields['agentMetadata.isOnDuty'] = updateData.agentMetadata.isOnDuty;
      }

      // Location updates (can be updated by the agent or admin)
      if (updateData.agentMetadata.lastKnownLocation) {
        updateFields['agentMetadata.lastKnownLocation'] = {
          type: 'Point',
          coordinates: updateData.agentMetadata.lastKnownLocation.coordinates,
        };
      }
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userObjectId,
      { $set: updateFields },
      { new: true }
    ).select('-passwordHash');

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    return updatedUser;
  }

  /**
   * Delete user (admin only or self-delete)
   */
  async deleteUser(userId: string | Types.ObjectId, currentUser: TokenPayload) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only delete their own account unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only delete your own account');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prevent deleting the last admin
    if (user.role === UserRole.ADMIN) {
      const adminCount = await User.countDocuments({ role: UserRole.ADMIN });
      if (adminCount <= 1) {
        throw new ValidationError('Cannot delete the last admin account');
      }
    }

    // Delete user
    await User.findByIdAndDelete(userObjectId);

    return { success: true, message: 'User deleted successfully' };
  }

  /**
   * Subscribe user to notification topic
   */
  async subscribeToTopic(
    userId: string | Types.ObjectId,
    topic: NotificationTopic,
    deviceToken: string,
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only subscribe themselves unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only manage your own subscriptions');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Subscribe to topic
    const result = await subscribeToTopic([deviceToken], topic);

    return {
      success: result.successCount > 0,
      topic,
      failureCount: result.failureCount,
      successCount: result.successCount,
    };
  }

  /**
   * Unsubscribe user from notification topic
   */
  async unsubscribeFromTopic(
    userId: string | Types.ObjectId,
    topic: NotificationTopic,
    deviceToken: string,
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only unsubscribe themselves unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only manage your own subscriptions');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Unsubscribe from topic
    const result = await unsubscribeFromTopic([deviceToken], topic);

    return {
      success: result.successCount > 0,
      topic,
      failureCount: result.failureCount,
      successCount: result.successCount,
    };
  }

  /**
   * Toggle user active status (admin only)
   */
  async toggleUserActiveStatus(
    userId: string | Types.ObjectId,
    isActive: boolean,
    currentUser: TokenPayload
  ) {
    // Verify current user is admin
    if (currentUser.role !== UserRole.ADMIN) {
      throw new UnauthorizedError('Only admins can toggle user active status');
    }

    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prevent deactivating the last admin
    if (user.role === UserRole.ADMIN && !isActive) {
      const adminCount = await User.countDocuments({ role: UserRole.ADMIN, isActive: true });
      if (adminCount <= 1) {
        throw new ValidationError('Cannot deactivate the last admin account');
      }
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userObjectId,
      { $set: { isActive } },
      { new: true }
    );

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    return updatedUser;
  }

  /**
   * Toggle agent on duty status (admin or agent only)
   */
  async toggleAgentOnDutyStatus(
    userId: string | Types.ObjectId,
    isOnDuty: boolean,
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only toggle their own status unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only toggle your own on duty status');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userObjectId,
      { $set: { 'agentMetadata.isOnDuty': isOnDuty } },
      { new: true }
    );

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    return updatedUser;
  }
}

// singleton user services export
export const userService = new UserServices();
