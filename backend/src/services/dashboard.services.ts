import { Types } from 'mongoose';
import { Order, Payment, User, Cylinder, SparePart, Package } from '../models';
import { OrderStatus, UserRole, CylinderStatus, SparePartStatus } from '../enums/enums';
import logger from '../config/logger';
import { NotFoundError, InternalServerError } from '../errors/app_errors';

/**
 * Dashboard service providing comprehensive analytics and reporting
 * for admin, agent, and user roles
 */
class DashboardService {
  constructor() {}

  /**
   * Get comprehensive admin dashboard data
   * @param dateRange - Optional date range filter
   * @returns Admin dashboard analytics
   */
  async getAdminDashboard(dateRange?: { startDate: Date; endDate: Date }) {
    try {
      logger.info('Fetching admin dashboard data', { dateRange });

      const dateFilter = dateRange
        ? {
            createdAt: {
              $gte: dateRange.startDate,
              $lte: dateRange.endDate,
            },
          }
        : {};

      // Execute all queries in parallel for better performance
      const [
        orderStats,
        revenueStats,
        inventoryStats,
        userStats,
        recentOrders,
        topProducts,
        agentPerformance,
        paymentStats,
      ] = await Promise.all([
        this.getOrderStatistics(dateFilter),
        this.getRevenueStatistics(dateFilter),
        this.getInventoryStatistics(),
        this.getUserStatistics(dateFilter),
        this.getRecentOrders(10),
        this.getTopProducts(dateFilter),
        this.getAgentPerformance(dateFilter),
        this.getPaymentStatistics(dateFilter),
      ]);

      return {
        orderStats,
        revenueStats,
        inventoryStats,
        userStats,
        recentOrders,
        topProducts,
        agentPerformance,
        paymentStats,
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error('Failed to fetch admin dashboard data', {
        error: error.message,
        dateRange,
        timestamp: new Date().toISOString(),
      });
      throw new InternalServerError('Failed to fetch admin dashboard data');
    }
  }

  /**
   * Get agent dashboard data
   * @param agentId - Agent user ID
   * @param dateRange - Optional date range filter
   * @returns Agent dashboard analytics
   */
  async getAgentDashboard(agentId: string, dateRange?: { startDate: Date; endDate: Date }) {
    try {
      logger.info('Fetching agent dashboard data', { agentId, dateRange });

      // Validate agent exists and has correct role
      const agent = await User.findById(agentId);
      if (!agent || agent.role !== UserRole.AGENT) {
        throw new NotFoundError('Agent not found or invalid role');
      }

      const dateFilter = dateRange
        ? {
            createdAt: {
              $gte: dateRange.startDate,
              $lte: dateRange.endDate,
            },
          }
        : {};

      // Execute queries in parallel
      const [
        assignedOrders,
        completedOrders,
        pendingOrders,
        earnings,
        recentDeliveries,
        performanceMetrics,
      ] = await Promise.all([
        this.getAgentOrderCount(agentId, dateFilter),
        this.getAgentCompletedOrders(agentId, dateFilter),
        this.getAgentPendingOrders(agentId),
        this.getAgentEarnings(agentId, dateFilter),
        this.getAgentRecentDeliveries(agentId, 10),
        this.getAgentPerformanceMetrics(agentId, dateFilter),
      ]);

      return {
        agentInfo: {
          id: agent._id,
          phone: agent.phone,
          email: agent.email,
          isOnDuty: agent.agentMetadata?.isOnDuty || false,
          rating: agent.agentMetadata?.rating || 0,
          vehicle: agent.agentMetadata?.vehicle,
        },
        orderStats: {
          total: assignedOrders,
          completed: completedOrders,
          pending: pendingOrders,
          completionRate: assignedOrders > 0 ? (completedOrders / assignedOrders) * 100 : 0,
        },
        earnings,
        recentDeliveries,
        performanceMetrics,
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error('Failed to fetch agent dashboard data', {
        error: error.message,
        agentId,
        dateRange,
        timestamp: new Date().toISOString(),
      });
      if (error instanceof NotFoundError) throw error;
      throw new InternalServerError('Failed to fetch agent dashboard data');
    }
  }

  /**
   * Get supervisor dashboard data with restricted access
   * @returns Supervisor dashboard analytics (today's data only)
   */
  async getSupervisorDashboard() {
    try {
      logger.info('Fetching supervisor dashboard data');

      // Force today's date filter - supervisors can only see today's data
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        23,
        59,
        59,
        999
      );

      const todayFilter = {
        createdAt: {
          $gte: startOfDay,
          $lte: endOfDay,
        },
      };

      // Execute queries in parallel - only today's data
      const [todayOrderStats, todaySalesStats, availableAgents, todayOrders, todayTopProducts] =
        await Promise.all([
          this.getSupervisorOrderStatistics(todayFilter),
          this.getSupervisorSalesStatistics(todayFilter),
          this.getAvailableAgents(),
          this.getSupervisorTodayOrders(10),
          this.getSupervisorTopProducts(todayFilter),
        ]);

      return {
        supervisorInfo: {
          // id: supervisor._id,
          // phone: supervisor.phone,
          // email: supervisor.email,
        },
        todayStats: {
          orders: todayOrderStats,
          sales: todaySalesStats,
        },
        availableAgents,
        todayOrders,
        todayTopProducts,
        restrictions: {
          dataScope: 'TODAY_ONLY',
          canAssignOrders: true,
          canEditInventory: false,
          canSeeCostData: false,
        },
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error('Failed to fetch supervisor dashboard data', {
        error: error.message,
        // supervisorId,
        timestamp: new Date().toISOString(),
      });
      if (error instanceof NotFoundError) throw error;
      throw new InternalServerError('Failed to fetch supervisor dashboard data');
    }
  }

  /**
   * Get user/customer dashboard data
   * @param userId - Customer user ID
   * @param dateRange - Optional date range filter
   * @returns Customer dashboard analytics
   */
  async getUserDashboard(userId: string, dateRange?: { startDate: Date; endDate: Date }) {
    try {
      logger.info('Fetching user dashboard data', { userId, dateRange });

      // Validate user exists
      const user = await User.findById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      const dateFilter = dateRange
        ? {
            createdAt: {
              $gte: dateRange.startDate,
              $lte: dateRange.endDate,
            },
          }
        : {};

      // Execute queries in parallel
      const [orderStats, totalSpent, recentOrders, favoriteProducts, deliveryStats] =
        await Promise.all([
          this.getUserOrderStatistics(userId, dateFilter),
          this.getUserTotalSpent(userId, dateFilter),
          this.getUserRecentOrders(userId, 5),
          this.getUserFavoriteProducts(userId),
          this.getUserDeliveryStatistics(userId, dateFilter),
        ]);

      return {
        userInfo: {
          id: user._id,
          phone: user.phone,
          email: user.email,
          addresses: user.addresses,
          memberSince: user.createdAt,
        },
        orderStats,
        totalSpent,
        recentOrders,
        favoriteProducts,
        deliveryStats,
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error('Failed to fetch user dashboard data', {
        error: error.message,
        userId,
        dateRange,
        timestamp: new Date().toISOString(),
      });
      if (error instanceof NotFoundError) throw error;
      throw new InternalServerError('Failed to fetch user dashboard data');
    }
  }

  /**
   * Get order statistics for admin dashboard
   */
  private async getOrderStatistics(dateFilter: any) {
    const pipeline = [
      { $match: dateFilter },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
        },
      },
    ];

    const results = await Order.aggregate(pipeline);
    const stats = {
      total: 0,
      pending: 0,
      confirmed: 0,
      outForDelivery: 0,
      delivered: 0,
      cancelled: 0,
      failed: 0,
      totalRevenue: 0,
    };

    results.forEach(result => {
      stats.total += result.count;
      stats.totalRevenue += result.totalAmount;

      switch (result._id) {
        case OrderStatus.PENDING:
          stats.pending = result.count;
          break;
        case OrderStatus.CONFIRMED:
          stats.confirmed = result.count;
          break;
        case OrderStatus.OUT_FOR_DELIVERY:
          stats.outForDelivery = result.count;
          break;
        case OrderStatus.DELIVERED:
          stats.delivered = result.count;
          break;
        case OrderStatus.CANCELLED:
          stats.cancelled = result.count;
          break;
        case OrderStatus.FAILED:
          stats.failed = result.count;
          break;
      }
    });

    return stats;
  }

  /**
   * Get revenue statistics with trends
   */
  private async getRevenueStatistics(dateFilter: any) {
    const pipeline = [
      {
        $match: { ...dateFilter, status: { $in: [OrderStatus.DELIVERED, OrderStatus.CONFIRMED] } },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
          },
          dailyRevenue: { $sum: '$totalAmount' },
          orderCount: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': 1 as 1, '_id.month': 1 as 1, '_id.day': 1 as 1 } },
    ];

    const dailyStats = await Order.aggregate(pipeline);

    const totalRevenue = dailyStats.reduce((sum, day) => sum + day.dailyRevenue, 0);
    const averageDailyRevenue = dailyStats.length > 0 ? totalRevenue / dailyStats.length : 0;

    return {
      totalRevenue,
      averageDailyRevenue,
      dailyStats,
      totalOrders: dailyStats.reduce((sum, day) => sum + day.orderCount, 0),
    };
  }

  /**
   * Get inventory statistics
   */
  private async getInventoryStatistics() {
    const [cylinderStats, sparePartStats, packageStats] = await Promise.all([
      this.getCylinderInventoryStats(),
      this.getSparePartInventoryStats(),
      this.getPackageInventoryStats(),
    ]);

    return {
      cylinders: cylinderStats,
      spareParts: sparePartStats,
      packages: packageStats,
    };
  }

  /**
   * Get cylinder inventory statistics
   */
  private async getCylinderInventoryStats() {
    const pipeline = [
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalQuantity: { $sum: '$quantity' },
          totalReserved: { $sum: '$reserved' },
          totalSold: { $sum: '$sold' },
          totalValue: { $sum: { $multiply: ['$quantity', '$price'] } },
        },
      },
    ];

    const results = await Cylinder.aggregate(pipeline);
    const lowStockItems = await Cylinder.find({
      $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
      status: CylinderStatus.Active,
    }).select('type material quantity minimumStockLevel');

    return {
      byStatus: results,
      lowStockItems,
      lowStockCount: lowStockItems.length,
    };
  }

  /**
   * Get spare part inventory statistics
   */
  private async getSparePartInventoryStats() {
    const pipeline = [
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalQuantity: { $sum: '$quantity' },
          totalValue: { $sum: { $multiply: ['$quantity', '$price'] } },
        },
      },
    ];

    const results = await SparePart.aggregate(pipeline);
    const lowStockItems = await SparePart.find({
      $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
      status: SparePartStatus.AVAILABLE,
    }).select('name category quantity minimumStockLevel');

    return {
      byStatus: results,
      lowStockItems,
      lowStockCount: lowStockItems.length,
    };
  }

  /**
   * Get package inventory statistics
   */
  private async getPackageInventoryStats() {
    const pipeline = [
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalQuantity: { $sum: '$quantity' },
          totalValue: { $sum: { $multiply: ['$quantity', '$price'] } },
        },
      },
    ];

    const results = await Package.aggregate(pipeline);
    return { byStatus: results };
  }

  /**
   * Get user statistics
   */
  private async getUserStatistics(dateFilter: any) {
    const pipeline = [
      { $match: dateFilter },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
        },
      },
    ];

    const results = await User.aggregate(pipeline);
    const stats = {
      total: 0,
      customers: 0,
      agents: 0,
      admins: 0,
      supervisors: 0,
      activeAgents: 0,
    };

    results.forEach(result => {
      stats.total += result.count;
      switch (result._id) {
        case UserRole.CUSTOMER:
          stats.customers = result.count;
          break;
        case UserRole.AGENT:
          stats.agents = result.count;
          break;
        case UserRole.ADMIN:
          stats.admins = result.count;
          break;
        case UserRole.SUPERVISOR:
          stats.supervisors = result.count;
          break;
      }
    });

    // Get active agents count
    stats.activeAgents = await User.countDocuments({
      role: UserRole.AGENT,
      'agentMetadata.isOnDuty': true,
    });

    return stats;
  }

  /**
   * Get recent orders for admin dashboard
   */
  private async getRecentOrders(limit: number) {
    return await Order.find()
      .populate('customer', 'phone email')
      .populate('deliveryAgent', 'phone email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('customer deliveryAgent totalAmount status createdAt deliveryAddress');
  }

  /**
   * Get top products by sales
   */
  private async getTopProducts(dateFilter: any) {
    const pipeline: any[] = [
      { $match: { ...dateFilter, status: OrderStatus.DELIVERED } },
      { $unwind: '$items' },
      {
        $group: {
          _id: {
            itemType: '$items.itemType',
            itemId: '$items.itemId',
          },
          totalQuantity: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.quantity', '$totalAmount'] } },
          orderCount: { $sum: 1 },
        },
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 10 },
    ];

    const results = await Order.aggregate(pipeline);

    // Populate product details
    const populatedResults = await Promise.all(
      results.map(async result => {
        let productDetails = null;

        switch (result._id.itemType) {
          case 'CYLINDER':
            productDetails = await Cylinder.findById(result._id.itemId).select(
              'type material price'
            );
            break;
          case 'SPARE_PART':
            productDetails = await SparePart.findById(result._id.itemId).select(
              'name category price'
            );
            break;
          case 'PACKAGE':
            productDetails = await Package.findById(result._id.itemId).select(
              'name description price'
            );
            break;
        }

        return {
          ...result,
          productDetails,
        };
      })
    );

    return populatedResults;
  }

  /**
   * Get agent performance metrics
   */
  private async getAgentPerformance(dateFilter: any) {
    const pipeline: any[] = [
      { $match: { ...dateFilter, deliveryAgent: { $exists: true } } },
      {
        $group: {
          _id: '$deliveryAgent',
          totalOrders: { $sum: 1 },
          completedOrders: {
            $sum: { $cond: [{ $eq: ['$status', OrderStatus.DELIVERED] }, 1, 0] },
          },
          totalRevenue: { $sum: '$totalAmount' },
          avgDeliveryTime: { $avg: { $subtract: ['$deliveredAt', '$createdAt'] } },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'agent',
        },
      },
      { $unwind: '$agent' },
      {
        $project: {
          agentId: '$_id',
          agentPhone: '$agent.phone',
          agentRating: '$agent.agentMetadata.rating',
          totalOrders: 1,
          completedOrders: 1,
          completionRate: {
            $multiply: [{ $divide: ['$completedOrders', '$totalOrders'] }, 100],
          },
          totalRevenue: 1,
          avgDeliveryTime: 1,
        },
      },
      { $sort: { completionRate: -1 } },
    ];

    return await Order.aggregate(pipeline);
  }

  /**
   * Get payment statistics
   */
  private async getPaymentStatistics(dateFilter: any) {
    const pipeline = [
      { $match: dateFilter },
      {
        $group: {
          _id: {
            method: '$method',
            status: '$status',
          },
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
        },
      },
    ];

    const results = await Payment.aggregate(pipeline);

    const stats: {
      byMethod: Record<string, { count: number; amount: number }>;
      byStatus: Record<string, { count: number; amount: number }>;
      totalProcessed: number;
      totalAmount: number;
    } = {
      byMethod: {},
      byStatus: {},
      totalProcessed: 0,
      totalAmount: 0,
    };

    results.forEach(result => {
      const method = result._id.method;
      const status = result._id.status;

      if (!stats.byMethod[method]) {
        stats.byMethod[method] = { count: 0, amount: 0 };
      }
      if (!stats.byStatus[status]) {
        stats.byStatus[status] = { count: 0, amount: 0 };
      }

      stats.byMethod[method].count += result.count;
      stats.byMethod[method].amount += result.totalAmount;
      stats.byStatus[status].count += result.count;
      stats.byStatus[status].amount += result.totalAmount;

      stats.totalProcessed += result.count;
      stats.totalAmount += result.totalAmount;
    });

    return stats;
  }

  // Agent-specific helper methods
  /**
   * Get agent order count
   */
  private async getAgentOrderCount(agentId: string, dateFilter: any) {
    return await Order.countDocuments({ deliveryAgent: agentId, ...dateFilter });
  }

  /**
   * Get agent completed orders count
   */
  private async getAgentCompletedOrders(agentId: string, dateFilter: any) {
    return await Order.countDocuments({
      deliveryAgent: agentId,
      status: OrderStatus.DELIVERED,
      ...dateFilter,
    });
  }

  /**
   * Get agent pending orders count
   */
  private async getAgentPendingOrders(agentId: string) {
    return await Order.countDocuments({
      deliveryAgent: agentId,
      status: { $in: [OrderStatus.CONFIRMED, OrderStatus.OUT_FOR_DELIVERY] },
    });
  }

  /**
   * Get agent earnings
   */
  private async getAgentEarnings(agentId: string, dateFilter: any) {
    const pipeline: any[] = [
      {
        $match: {
          deliveryAgent: new Types.ObjectId(agentId),
          status: OrderStatus.DELIVERED,
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: null as any,
          totalEarnings: { $sum: '$totalAmount' },
          totalOrders: { $sum: 1 },
          avgOrderValue: { $avg: '$totalAmount' },
        },
      },
    ];

    const result = await Order.aggregate(pipeline);
    return result[0] || { totalEarnings: 0, totalOrders: 0, avgOrderValue: 0 };
  }

  /**
   * Get agent recent deliveries
   */
  private async getAgentRecentDeliveries(agentId: string, limit: number) {
    return await Order.find({
      deliveryAgent: agentId,
      status: OrderStatus.DELIVERED,
    })
      .populate('customer', 'phone email')
      .sort({ deliveredAt: -1 })
      .limit(limit)
      .select('customer totalAmount deliveredAt deliveryAddress');
  }

  /**
   * Get agent performance metrics (renamed from getAgentPerformanceMetrics)
   */
  private async getAgentPerformanceMetrics(agentId: string, dateFilter: any) {
    const pipeline: any[] = [
      {
        $match: {
          deliveryAgent: new Types.ObjectId(agentId),
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
          },
          totalOrders: { $sum: 1 },
          completedOrders: {
            $sum: { $cond: [{ $eq: ['$status', OrderStatus.DELIVERED] }, 1, 0] },
          },
          totalRevenue: { $sum: '$totalAmount' },
        },
      },
      { $sort: { '_id.year': 1 as any, '_id.month': 1 as any } },
    ];

    const monthlyStats = await Order.aggregate(pipeline);

    // Calculate overall metrics
    const totalOrders = monthlyStats.reduce((sum, month) => sum + month.totalOrders, 0);
    const totalCompleted = monthlyStats.reduce((sum, month) => sum + month.completedOrders, 0);
    const totalRevenue = monthlyStats.reduce((sum, month) => sum + month.totalRevenue, 0);

    return {
      monthlyStats,
      overallMetrics: {
        totalOrders,
        totalCompleted,
        completionRate: totalOrders > 0 ? (totalCompleted / totalOrders) * 100 : 0,
        totalRevenue,
        avgOrderValue: totalCompleted > 0 ? totalRevenue / totalCompleted : 0,
      },
    };
  }

  // User-specific helper methods
  /**
   * Get user order history
   */
  private async getUserOrderHistory(userId: string, dateFilter: any) {
    return await Order.find({ customer: userId, ...dateFilter })
      .populate('deliveryAgent', 'phone email')
      .sort({ createdAt: -1 })
      .select('totalAmount status createdAt deliveredAt deliveryAddress items');
  }

  /**
   * Get user order statistics
   */
  private async getUserOrderStatistics(userId: string, dateFilter: any) {
    const pipeline = [
      { $match: { customer: new Types.ObjectId(userId), ...dateFilter } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
        },
      },
    ];

    const results = await Order.aggregate(pipeline);
    const stats = {
      total: 0,
      pending: 0,
      confirmed: 0,
      outForDelivery: 0,
      delivered: 0,
      cancelled: 0,
      failed: 0,
    };

    results.forEach(result => {
      stats.total += result.count;
      switch (result._id) {
        case OrderStatus.PENDING:
          stats.pending = result.count;
          break;
        case OrderStatus.CONFIRMED:
          stats.confirmed = result.count;
          break;
        case OrderStatus.OUT_FOR_DELIVERY:
          stats.outForDelivery = result.count;
          break;
        case OrderStatus.DELIVERED:
          stats.delivered = result.count;
          break;
        case OrderStatus.CANCELLED:
          stats.cancelled = result.count;
          break;
        case OrderStatus.FAILED:
          stats.failed = result.count;
          break;
      }
    });

    return stats;
  }

  /**
   * Get user total spent
   */
  private async getUserTotalSpent(userId: string, dateFilter: any) {
    const pipeline: any[] = [
      {
        $match: {
          customer: new Types.ObjectId(userId),
          status: { $in: [OrderStatus.DELIVERED, OrderStatus.CONFIRMED] },
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: null as any,
          totalSpent: { $sum: '$totalAmount' },
          orderCount: { $sum: 1 },
          avgOrderValue: { $avg: '$totalAmount' },
        },
      },
    ];

    const result = await Order.aggregate(pipeline);
    return result[0] || { totalSpent: 0, orderCount: 0, avgOrderValue: 0 };
  }

  /**
   * Get user recent orders
   */
  private async getUserRecentOrders(userId: string, limit: number) {
    return await Order.find({ customer: userId })
      .populate('deliveryAgent', 'phone email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('totalAmount status createdAt deliveryAddress items');
  }

  /**
   * Get user favorite products
   */
  private async getUserFavoriteProducts(userId: string) {
    const pipeline: any[] = [
      { $match: { customer: new Types.ObjectId(userId), status: OrderStatus.DELIVERED } },
      { $unwind: '$items' },
      {
        $group: {
          _id: {
            itemType: '$items.itemType',
            itemId: '$items.itemId',
          },
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: '$items.quantity' },
        },
      },
      { $sort: { orderCount: -1 } },
      { $limit: 5 },
    ];

    const results = await Order.aggregate(pipeline);

    // Populate product details
    const populatedResults = await Promise.all(
      results.map(async result => {
        let productDetails = null;

        switch (result._id.itemType) {
          case 'CYLINDER':
            productDetails = await Cylinder.findById(result._id.itemId).select(
              'type material price'
            );
            break;
          case 'SPARE_PART':
            productDetails = await SparePart.findById(result._id.itemId).select(
              'name category price'
            );
            break;
          case 'PACKAGE':
            productDetails = await Package.findById(result._id.itemId).select(
              'name description price'
            );
            break;
        }

        return {
          ...result,
          productDetails,
        };
      })
    );

    return populatedResults;
  }

  /**
   * Get user delivery statistics
   */
  private async getUserDeliveryStatistics(userId: string, dateFilter: any) {
    const pipeline: any[] = [
      {
        $match: {
          customer: new Types.ObjectId(userId),
          status: OrderStatus.DELIVERED,
          deliveredAt: { $exists: true },
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: null as any,
          totalDeliveries: { $sum: 1 },
          avgDeliveryTime: {
            $avg: { $subtract: ['$deliveredAt', '$createdAt'] },
          },
          fastestDelivery: {
            $min: { $subtract: ['$deliveredAt', '$createdAt'] },
          },
          slowestDelivery: {
            $max: { $subtract: ['$deliveredAt', '$createdAt'] },
          },
        },
      },
    ];

    const result = await Order.aggregate(pipeline);
    const stats = result[0] || {
      totalDeliveries: 0,
      avgDeliveryTime: 0,
      fastestDelivery: 0,
      slowestDelivery: 0,
    };

    // Convert milliseconds to hours for better readability
    return {
      totalDeliveries: stats.totalDeliveries,
      avgDeliveryTimeHours: stats.avgDeliveryTime ? stats.avgDeliveryTime / (1000 * 60 * 60) : 0,
      fastestDeliveryHours: stats.fastestDelivery ? stats.fastestDelivery / (1000 * 60 * 60) : 0,
      slowestDeliveryHours: stats.slowestDelivery ? stats.slowestDelivery / (1000 * 60 * 60) : 0,
    };
  }

  // Supervisor-specific helper methods
  /**
   * Get supervisor order statistics (today only, no cost data)
   */
  private async getSupervisorOrderStatistics(dateFilter: any) {
    const pipeline = [
      { $match: dateFilter },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          // Only include selling price, no cost data
          totalAmount: { $sum: '$totalAmount' },
        },
      },
    ];

    const results = await Order.aggregate(pipeline);
    const stats = {
      total: 0,
      pending: 0,
      confirmed: 0,
      outForDelivery: 0,
      delivered: 0,
      cancelled: 0,
      failed: 0,
      totalRevenue: 0,
    };

    results.forEach(result => {
      stats.total += result.count;
      stats.totalRevenue += result.totalAmount;

      switch (result._id) {
        case OrderStatus.PENDING:
          stats.pending = result.count;
          break;
        case OrderStatus.CONFIRMED:
          stats.confirmed = result.count;
          break;
        case OrderStatus.OUT_FOR_DELIVERY:
          stats.outForDelivery = result.count;
          break;
        case OrderStatus.DELIVERED:
          stats.delivered = result.count;
          break;
        case OrderStatus.CANCELLED:
          stats.cancelled = result.count;
          break;
        case OrderStatus.FAILED:
          stats.failed = result.count;
          break;
      }
    });

    return stats;
  }

  /**
   * Get supervisor sales statistics (today only, selling price only)
   */
  private async getSupervisorSalesStatistics(dateFilter: any) {
    const pipeline: any[] = [
      {
        $match: { ...dateFilter, status: { $in: [OrderStatus.DELIVERED, OrderStatus.CONFIRMED] } },
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: {
            itemType: '$items.itemType',
            itemId: '$items.itemId',
          },
          totalQuantitySold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
          orderCount: { $sum: 1 },
        },
      },
      { $sort: { totalQuantitySold: -1 } },
    ];

    const results = await Order.aggregate(pipeline);

    // Calculate totals
    const totalQuantitySold = results.reduce((sum, item) => sum + item.totalQuantitySold, 0);
    const totalRevenue = results.reduce((sum, item) => sum + item.totalRevenue, 0);

    return {
      totalQuantitySold,
      totalRevenue,
      itemsSold: results.length,
      salesBreakdown: results,
    };
  }

  /**
   * Get available agents for order assignment
   */
  private async getAvailableAgents() {
    return await User.find({
      role: UserRole.AGENT,
      'agentMetadata.isOnDuty': true,
    })
      .select('phone email agentMetadata.rating agentMetadata.vehicle')
      .sort({ 'agentMetadata.rating': -1 });
  }

  /**
   * Get today's orders for supervisor (with assignment capability)
   */
  private async getSupervisorTodayOrders(limit: number) {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      23,
      59,
      59,
      999
    );

    return await Order.find({
      createdAt: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
    })
      .populate('customer', 'phone email')
      .populate('deliveryAgent', 'phone email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('customer deliveryAgent totalAmount status createdAt deliveryAddress');
  }

  /**
   * Get today's top products (selling price only, no cost data)
   */
  private async getSupervisorTopProducts(dateFilter: any) {
    const pipeline: any[] = [
      { $match: { ...dateFilter, status: OrderStatus.DELIVERED } },
      { $unwind: '$items' },
      {
        $group: {
          _id: {
            itemType: '$items.itemType',
            itemId: '$items.itemId',
          },
          totalQuantity: { $sum: '$items.quantity' },
          // Only selling price, no cost/profit/margin data
          totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
          orderCount: { $sum: 1 },
        },
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 10 },
    ];

    const results = await Order.aggregate(pipeline);

    // Populate product details (price only, no cost data)
    const populatedResults = await Promise.all(
      results.map(async result => {
        let productDetails = null;

        switch (result._id.itemType) {
          case 'CYLINDER':
            productDetails = await Cylinder.findById(result._id.itemId).select(
              'type material price' // No costPrice, profit, margin
            );
            break;
          case 'SPARE_PART':
            productDetails = await SparePart.findById(result._id.itemId).select(
              'name category price' // No costPrice, profit, margin
            );
            break;
          case 'PACKAGE':
            productDetails = await Package.findById(result._id.itemId).select(
              'name description price' // No costPrice, profit, margin
            );
            break;
        }

        return {
          ...result,
          productDetails,
        };
      })
    );

    return populatedResults;
  }
}

export const dashboardService = new DashboardService();
