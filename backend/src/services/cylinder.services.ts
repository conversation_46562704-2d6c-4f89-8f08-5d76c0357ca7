import { Cylinder } from '../models';
import { BadRequestError, NotFoundError, DuplicateResourceError } from '../errors/app_errors';
import mongoose, { ClientSession, Types } from 'mongoose';
import { CylinderMaterial, CylinderType, CylinderStatus, UserRole } from '../enums/enums';
import { ICylinder } from '../models/cylinder.model';
import logger from '../config/logger';

class CylinderService {
  /**
   * Reserve cylinders for an order
   * @throws {BadRequestError} If quantity is invalid or insufficient stock
   * @throws {NotFoundError} If cylinder not found
   */
  async reserveCylinder(
    cylinderId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<{ modifiedCount: number; newStatus?: CylinderStatus }> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const cylinder = await Cylinder.findById(cylinderId).session(session || null);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    if (cylinder.status !== CylinderStatus.Active) {
      throw new BadRequestError(`Cannot reserve cylinder with status ${cylinder.status}`, {
        code: 'INVALID_CYLINDER_STATUS',
      });
    }

    if (cylinder.availableQuantity < quantity) {
      throw new BadRequestError('Insufficient cylinder stock', {
        code: 'INSUFFICIENT_CYLINDER_STOCK',
        details: {
          available: cylinder.availableQuantity,
          requested: quantity,
        },
      });
    }

    const update: any = {
      $inc: { reserved: quantity },
    };

    // Update status if this reservation brings us to out of stock
    if (cylinder.quantity - (cylinder.reserved + quantity) <= 0) {
      update.$set = { status: CylinderStatus.OutOfStock };
    }

    const result = await Cylinder.updateOne(
      {
        _id: cylinderId,
        $expr: { $gte: [{ $subtract: ['$quantity', '$reserved'] }, quantity] },
      },
      update,
      { session }
    );

    if (result.modifiedCount === 0) {
      throw new DuplicateResourceError('Cylinder stock changed during reservation', {
        code: 'CONCURRENT_MODIFICATION',
      });
    }

    return {
      modifiedCount: result.modifiedCount,
      newStatus: update.$set?.status,
    };
  }

  /**
   * Release reserved cylinders (when order is cancelled)
   * @throws {BadRequestError} If quantity is invalid or exceeds reserved amount
   * @throws {NotFoundError} If cylinder not found
   */
  async releaseReservation(
    cylinderId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<{ modifiedCount: number; newStatus?: CylinderStatus }> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const cylinder = await Cylinder.findById(cylinderId).session(session || null);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    if (cylinder.reserved < quantity) {
      throw new BadRequestError('Cannot release more than reserved quantity', {
        code: 'INVALID_RESERVATION_RELEASE',
        details: {
          reserved: cylinder.reserved,
          toRelease: quantity,
        },
      });
    }

    const update: any = {
      $inc: { reserved: -quantity },
    };

    // Update status if we're coming back from out of stock
    if (
      cylinder.status === CylinderStatus.OutOfStock &&
      cylinder.quantity - (cylinder.reserved - quantity) > 0
    ) {
      update.$set = { status: CylinderStatus.Active };
    }

    const result = await Cylinder.updateOne(
      { _id: cylinderId, reserved: { $gte: quantity } },
      update,
      { session }
    );

    if (result.modifiedCount === 0) {
      throw new DuplicateResourceError('Cylinder stock changed during release', {
        code: 'CONCURRENT_MODIFICATION',
      });
    }

    return {
      modifiedCount: result.modifiedCount,
      newStatus: update.$set?.status,
    };
  }

  /**
   * Mark reserved cylinders as sold (when order is completed)
   * @throws {BadRequestError} If quantity is invalid
   * @throws {NotFoundError} If cylinder not found
   */
  async markAsSold(
    cylinderId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<ICylinder> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const cylinder = await Cylinder.findByIdAndUpdate(
      cylinderId,
      {
        $inc: {
          reserved: -quantity,
          sold: quantity,
          quantity: -quantity, // Physical count decreases when sold
        },
      },
      { new: true, session }
    );

    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    return cylinder;
  }

  /**
   * Restock cylinders and update status if needed
   * @throws {BadRequestError} If quantity is invalid
   * @throws {NotFoundError} If cylinder not found
   */
  async restock(cylinderId: string, quantity: number, session?: ClientSession): Promise<ICylinder> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const update: any = {
      $inc: { quantity },
      lastRestockedAt: new Date(),
    };

    // Update status if coming from out of stock
    update.$set = {
      status: CylinderStatus.Active,
    };

    const cylinder = await Cylinder.findByIdAndUpdate(cylinderId, update, { new: true, session });

    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    return cylinder;
  }

  /**
   * Create a new cylinder type
   * @throws {DuplicateResourceError} If cylinder type already exists
   */
  async createCylinder(cylinderData: {
    type: CylinderType;
    material: CylinderMaterial;
    price: number;
    cost: number;
    imageUrl?: string;
    description?: string;
    quantity?: number;
    minimumStockLevel?: number;
    status?: CylinderStatus;
  }): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Check if cylinder type already exists
      const existing = await Cylinder.findOne({
        type: cylinderData.type,
        material: cylinderData.material,
      }).session(session);

      if (existing) {
        throw new DuplicateResourceError('Cylinder type already exists', {
          code: 'CYLINDER_EXISTS',
        });
      }

      // Generate dynamic image URL based on cylinder type
      // const imageFileName = `${cylinderData.type.toLowerCase().replace('kg', '')}kg.png`;
      const imageFileName = `${cylinderData.type.replace(/[^\d]/g, '')}kg.png`.toLowerCase();
      const dynamicImageUrl = `/images/cylinders/${imageFileName}`;

      const cylinder = new Cylinder({
        quantity: 0,
        reserved: 0,
        sold: 0,
        minimumStockLevel: 10,
        status: CylinderStatus.Active,
        ...cylinderData,
        // Override with dynamic URL if not provided
        // imageUrl: cylinderData.imageUrl || dynamicImageUrl,
        imageUrl: dynamicImageUrl,
      });

      await cylinder.save({ session });

      await session.commitTransaction();
      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Update cylinder details (excluding inventory-related fields)
   * @throws {NotFoundError} If cylinder not found
   */
  async updateCylinder(cylinderId: string, updateData: Partial<ICylinder>): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Prevent updating inventory-related fields through this method
      const { quantity, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;

      const cylinder = await Cylinder.findByIdAndUpdate(cylinderId, safeUpdateData, {
        new: true,
        runValidators: true,
        session,
      });

      if (!cylinder) {
        throw new NotFoundError('Cylinder not found', {
          code: 'CYLINDER_NOT_FOUND',
        });
      }

      await session.commitTransaction();
      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Delete a cylinder type (only if no active reservations)
   * @throws {NotFoundError} If cylinder not found
   * @throws {DuplicateResourceError} If cylinder has active reservations
   */
  async deleteCylinder(cylinderId: string): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      const cylinder = await Cylinder.findOneAndDelete({
        _id: cylinderId,
        reserved: 0,
      }).session(session);

      if (!cylinder) {
        // Check if it exists but has reservations
        const existing = await Cylinder.findById(cylinderId).session(session);
        if (existing) {
          throw new DuplicateResourceError('Cannot delete cylinder with active reservations', {
            code: 'ACTIVE_RESERVATIONS',
            details: {
              reserved: existing.reserved,
            },
          });
        }
        throw new NotFoundError('Cylinder not found', {
          code: 'CYLINDER_NOT_FOUND',
        });
      }

      await session.commitTransaction();
      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Get cylinder by ID
   * @throws {NotFoundError} If cylinder not found
   */
  async getCylinderById(cylinderId: string): Promise<ICylinder> {
    const cylinder = await Cylinder.findById(cylinderId);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }
    return cylinder;
  }

  /**
   * List all cylinders with optional filtering and pagination
   */
  async listCylinders(
    filter: {
      type?: CylinderType;
      material?: CylinderMaterial;
      status?: CylinderStatus;
      lowStockOnly?: boolean;
    } = {},
    pagination: { page: number; limit: number } = { page: 1, limit: 10 },
    requestedUser: { userId: string | Types.ObjectId; role: UserRole }
  ): Promise<{ data: ICylinder[]; total: number }> {
    const query: any = {};

    if (filter.type) query.type = filter.type;
    if (filter.material) query.material = filter.material;
    // if (filter.status) query.status = filter.status;
    if (filter.status) {
      if (requestedUser.role === UserRole.CUSTOMER) {
        query.status = CylinderStatus.Active;
      } else {
        query.status = filter.status;
      }
    }
    if (filter.lowStockOnly) {
      query.$expr = { $lte: ['$quantity', '$minimumStockLevel'] };
    }

    const [data, total] = await Promise.all([
      Cylinder.find(query)
        .sort({ type: 1, material: 1 })
        .skip((pagination.page - 1) * pagination.limit)
        .limit(pagination.limit),
      Cylinder.countDocuments(query),
    ]);

    return { data, total };
  }

  /**
   * Check cylinder availability
   * @throws {NotFoundError} If cylinder not found
   */
  async checkAvailability(
    cylinderId: string,
    quantity: number
  ): Promise<{ available: boolean; availableQuantity: number; status: CylinderStatus }> {
    const cylinder = await Cylinder.findById(cylinderId);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    return {
      available:
        cylinder.status === CylinderStatus.Active && cylinder.availableQuantity >= quantity,
      availableQuantity: cylinder.availableQuantity,
      status: cylinder.status,
    };
  }

  /**
   * Get low stock alerts (quantity <= minimumStockLevel)
   */
  async getLowStockAlerts(): Promise<ICylinder[]> {
    return Cylinder.find({
      $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
      status: CylinderStatus.Active, // Only active cylinders
    }).sort({ quantity: 1 }); // Sort by most critical first
  }

  /**
   * Get sales statistics aggregated by type and material
   */
  async getSalesStatistics(): Promise<{
    totalSold: number;
    byType: Record<CylinderType, number>;
    byMaterial: Record<CylinderMaterial, number>;
    byStatus: Record<CylinderStatus, number>;
  }> {
    const result = {
      totalSold: 0,
      byType: {} as Record<CylinderType, number>,
      byMaterial: {} as Record<CylinderMaterial, number>,
      byStatus: {} as Record<CylinderStatus, number>,
    };

    // Initialize all possible values with 0
    Object.values(CylinderType).forEach(type => (result.byType[type] = 0));
    Object.values(CylinderMaterial).forEach(material => (result.byMaterial[material] = 0));
    Object.values(CylinderStatus).forEach(status => (result.byStatus[status] = 0));

    const cylinders = await Cylinder.find();

    cylinders.forEach(cylinder => {
      result.totalSold += cylinder.sold;
      result.byType[cylinder.type] += cylinder.sold;
      result.byMaterial[cylinder.material] += cylinder.sold;
      result.byStatus[cylinder.status]++;
    });

    return result;
  }

  /**
   * Bulk update cylinder statuses
   * @throws {BadRequestError} If invalid status transition
   */
  async bulkUpdateStatus(
    cylinderIds: string[],
    newStatus: CylinderStatus,
    session?: ClientSession
  ): Promise<number> {
    // Validate status transitions
    if (newStatus === CylinderStatus.Active) {
      // Can only activate cylinders that are out of stock
      const result = await Cylinder.updateMany(
        {
          _id: { $in: cylinderIds },
          status: { $in: [CylinderStatus.OutOfStock] },
        },
        { $set: { status: newStatus } },
        { session }
      );
      return result.modifiedCount;
    } else {
      // For other statuses (Discontinued)
      const result = await Cylinder.updateMany(
        { _id: { $in: cylinderIds } },
        { $set: { status: newStatus } },
        { session }
      );
      return result.modifiedCount;
    }
  }
}

export const cylinderService = new CylinderService();
