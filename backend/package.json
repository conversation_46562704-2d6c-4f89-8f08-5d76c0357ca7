{"name": "gas-system-project", "version": "1.0.0", "main": "index.js", "scripts": {"format": "prettier --write .", "dev": "nodemon", "start": "node dist/server.js", "build": "tsc", "postinstall": "npm run build", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "description": " Gas System Project  ", "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "querystring": "^0.2.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^24.0.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}