"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toggleAgentOnDutyStatus = exports.toggleUserActiveStatus = exports.getCurrentUser = exports.subscribeToNotifications = exports.deleteUser = exports.updateUser = exports.getAllUsers = exports.getSingleUser = exports.verifyOtp = exports.resendOtp = exports.registerAdminOrAgent = exports.login = void 0;
const user_service_1 = require("../services/user.service");
const response_1 = require("../utils/response");
const app_errors_1 = require("../errors/app_errors");
const login = async (req, res, next) => {
    try {
        const { phone } = req.body;
        if (!phone || phone.trim() === '') {
            throw new app_errors_1.ValidationError('Phone number is required');
        }
        const result = await user_service_1.userService.login(phone);
        (0, response_1.sendResponse)(res, 200, 'success', 'Login successful', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const registerAdminOrAgent = async (req, res, next) => {
    try {
        const userData = req.body;
        const result = await user_service_1.userService.registerAdminOrAgent(userData, req.user);
        (0, response_1.sendResponse)(res, 201, 'success', 'Admin/Agent registered successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.registerAdminOrAgent = registerAdminOrAgent;
const resendOtp = async (req, res, next) => {
    try {
        const { phone } = req.body;
        if (!phone || phone.trim() === '') {
            throw new app_errors_1.ValidationError('Phone number is required');
        }
        const result = await user_service_1.userService.resendOtp(phone);
        (0, response_1.sendResponse)(res, 200, 'success', 'OTP resent successfully'
        //   {
        //   data: result,
        // }
        );
    }
    catch (error) {
        next(error);
    }
};
exports.resendOtp = resendOtp;
const verifyOtp = async (req, res, next) => {
    try {
        const { phone, otp } = req.body;
        if (!phone || phone.trim() === '') {
            throw new app_errors_1.ValidationError('Phone number is required');
        }
        if (!otp || otp.trim() === '') {
            throw new app_errors_1.ValidationError('OTP is required');
        }
        const result = await user_service_1.userService.verifyOtp(phone, otp);
        (0, response_1.sendResponse)(res, 200, 'success', 'OTP verified successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.verifyOtp = verifyOtp;
const getSingleUser = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const result = await user_service_1.userService.getSingleUser(userId, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User retrieved successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getSingleUser = getSingleUser;
const getAllUsers = async (req, res, next) => {
    try {
        const options = {
            role: req.query.role,
            isActive: req.query.isActive === 'true',
            page: req.query.page ? parseInt(req.query.page) : undefined,
            limit: req.query.limit ? parseInt(req.query.limit) : undefined,
            sortBy: req.query.sortBy,
            sortOrder: req.query.sortOrder,
        };
        const result = await user_service_1.userService.getAllUsers(req.user, options);
        (0, response_1.sendResponse)(res, 200, 'success', 'Users retrieved successfully', {
            data: result.users,
            meta: result.pagination,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getAllUsers = getAllUsers;
const updateUser = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const updateData = req.body;
        const result = await user_service_1.userService.updateUser(userId, updateData, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User updated successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateUser = updateUser;
const deleteUser = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const result = await user_service_1.userService.deleteUser(userId, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User deleted successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteUser = deleteUser;
const subscribeToNotifications = async (req, res, next) => {
    try {
        const { userId, topic, deviceToken } = req.body;
        const result = await user_service_1.userService.subscribeToTopic(userId, topic, deviceToken, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'Subscribed to notifications successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.subscribeToNotifications = subscribeToNotifications;
const getCurrentUser = async (req, res, next) => {
    try {
        const result = await user_service_1.userService.getSingleUser(req.user.userId.toString(), req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'Current user retrieved successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUser = getCurrentUser;
const toggleUserActiveStatus = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const isActive = req.body.isActive;
        const result = await user_service_1.userService.toggleUserActiveStatus(userId, isActive, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User active status toggled successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.toggleUserActiveStatus = toggleUserActiveStatus;
const toggleAgentOnDutyStatus = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const isOnDuty = req.body.isOnDuty;
        const result = await user_service_1.userService.toggleAgentOnDutyStatus(userId, isOnDuty, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'Agent on duty status toggled successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.toggleAgentOnDutyStatus = toggleAgentOnDutyStatus;
//# sourceMappingURL=user.controller.js.map