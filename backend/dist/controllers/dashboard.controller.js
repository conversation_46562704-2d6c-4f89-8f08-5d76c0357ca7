"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardController = void 0;
const services_1 = require("../services");
const response_1 = require("../utils/response");
const enums_1 = require("../enums/enums");
const logger_1 = __importDefault(require("../config/logger"));
const app_errors_1 = require("../errors/app_errors");
/**
 * Dashboard controller handling admin, agent, and user dashboard endpoints
 */
class DashboardController {
    /**
     * Get admin dashboard data
     * @route GET /api/v1/dashboard/admin
     * @access Admin only
     */
    async getAdminDashboard(req, res, next) {
        try {
            // Check if user is admin
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.ForbiddenError('Access denied. Admin role required.');
            }
            // Parse date range from query parameters
            const { startDate, endDate } = req.query;
            let dateRange;
            if (startDate && endDate) {
                dateRange = {
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                };
                // Validate date range
                if (dateRange.startDate > dateRange.endDate) {
                    throw new app_errors_1.BadRequestError('Start date cannot be after end date');
                }
            }
            const dashboardData = await services_1.dashboardService.getAdminDashboard(dateRange);
            logger_1.default.info('Admin dashboard data retrieved successfully', {
                adminId: req.user.userId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Admin dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            logger_1.default.error('Failed to retrieve admin dashboard data', {
                error: error.message,
                adminId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            next(error);
        }
    }
    /**
     * Get agent dashboard data
     * @route GET /api/v1/dashboard/agent/:agentId?
     * @access Agent (own data) or Admin (any agent data)
     */
    async getAgentDashboard(req, res, next) {
        try {
            const requestedAgentId = (req.params.agentId || req.user?.userId)?.toString();
            if (!requestedAgentId) {
                throw new app_errors_1.BadRequestError('Agent ID is required');
            }
            // Authorization check
            if (req.user?.role === enums_1.UserRole.AGENT && req.user.userId !== requestedAgentId) {
                throw new app_errors_1.ForbiddenError('Agents can only access their own dashboard data');
            }
            else if (req.user?.role !== enums_1.UserRole.AGENT && req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.ForbiddenError('Access denied. Agent or Admin role required.');
            }
            // Parse date range from query parameters
            const { startDate, endDate } = req.query;
            let dateRange;
            if (startDate && endDate) {
                dateRange = {
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                };
                // Validate date range
                if (dateRange.startDate > dateRange.endDate) {
                    throw new app_errors_1.BadRequestError('Start date cannot be after end date');
                }
            }
            const dashboardData = await services_1.dashboardService.getAgentDashboard(requestedAgentId, dateRange);
            logger_1.default.info('Agent dashboard data retrieved successfully', {
                requesterId: req.user?.userId,
                agentId: requestedAgentId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Agent dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            logger_1.default.error('Failed to retrieve agent dashboard data', {
                error: error.message,
                requesterId: req.user?.userId,
                agentId: req.params.agentId,
                timestamp: new Date().toISOString(),
            });
            next(error);
        }
    }
    /**
     * Get user/customer dashboard data
     * @route GET /api/v1/dashboard/user/:userId?
     * @access Customer (own data) or Admin (any user data)
     */
    async getUserDashboard(req, res, next) {
        try {
            const requestedUserId = (req.params.userId || req.user?.userId)?.toString();
            if (!requestedUserId) {
                throw new app_errors_1.BadRequestError('User ID is required');
            }
            // Authorization check
            if (req.user?.role === enums_1.UserRole.CUSTOMER && req.user.userId !== requestedUserId) {
                throw new app_errors_1.ForbiddenError('Customers can only access their own dashboard data');
            }
            else if (req.user?.role !== enums_1.UserRole.CUSTOMER && req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.ForbiddenError('Access denied. Customer or Admin role required.');
            }
            // Parse date range from query parameters
            const { startDate, endDate } = req.query;
            let dateRange;
            if (startDate && endDate) {
                dateRange = {
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                };
                // Validate date range
                if (dateRange.startDate > dateRange.endDate) {
                    throw new app_errors_1.BadRequestError('Start date cannot be after end date');
                }
            }
            const dashboardData = await services_1.dashboardService.getUserDashboard(requestedUserId, dateRange);
            logger_1.default.info('User dashboard data retrieved successfully', {
                requesterId: req.user?.userId,
                userId: requestedUserId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'User dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            logger_1.default.error('Failed to retrieve user dashboard data', {
                error: error.message,
                requesterId: req.user?.userId,
                userId: req.params.userId,
                timestamp: new Date().toISOString(),
            });
            next(error);
        }
    }
    /**
     * Get dashboard summary for current user based on their role
     * @route GET /api/v1/dashboard/summary
     * @access Authenticated users
     */
    async getDashboardSummary(req, res, next) {
        try {
            if (!req.user?.userId || !req.user?.role) {
                throw new app_errors_1.BadRequestError('User authentication required');
            }
            let dashboardData;
            const userId = req.user.userId.toString();
            const userRole = req.user.role;
            // Get dashboard data based on user role
            switch (userRole) {
                case enums_1.UserRole.ADMIN:
                    dashboardData = await services_1.dashboardService.getAdminDashboard();
                    break;
                case enums_1.UserRole.AGENT:
                    dashboardData = await services_1.dashboardService.getAgentDashboard(userId);
                    break;
                case enums_1.UserRole.CUSTOMER:
                    dashboardData = await services_1.dashboardService.getUserDashboard(userId);
                    break;
                default:
                    throw new app_errors_1.BadRequestError('Invalid user role');
            }
            logger_1.default.info('Dashboard summary retrieved successfully', {
                userId,
                userRole,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Dashboard summary retrieved successfully', {
                data: {
                    userRole,
                    dashboard: dashboardData,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Failed to retrieve dashboard summary', {
                error: error.message,
                userId: req.user?.userId,
                userRole: req.user?.role,
                timestamp: new Date().toISOString(),
            });
            next(error);
        }
    }
    /**
     * Get dashboard reports (admin only)
     * @route GET /api/v1/dashboard/reports
     * @access Admin only
     */
    async getDashboardReports(req, res, next) {
        try {
            // Check if user is admin
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.ForbiddenError('Access denied. Admin role required.');
            }
            const { reportType, startDate, endDate } = req.query;
            if (!reportType) {
                throw new app_errors_1.BadRequestError('Report type is required');
            }
            // Parse date range
            let dateRange;
            if (startDate && endDate) {
                dateRange = {
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                };
                if (dateRange.startDate > dateRange.endDate) {
                    throw new app_errors_1.BadRequestError('Start date cannot be after end date');
                }
            }
            // Get specific report data based on type
            let reportData;
            switch (reportType) {
                case 'orders':
                    reportData = await services_1.dashboardService.getAdminDashboard(dateRange);
                    reportData = {
                        orderStats: reportData.orderStats,
                        recentOrders: reportData.recentOrders,
                    };
                    break;
                case 'revenue':
                    reportData = await services_1.dashboardService.getAdminDashboard(dateRange);
                    reportData = {
                        revenueStats: reportData.revenueStats,
                        paymentStats: reportData.paymentStats,
                    };
                    break;
                case 'inventory':
                    reportData = await services_1.dashboardService.getAdminDashboard(dateRange);
                    reportData = {
                        inventoryStats: reportData.inventoryStats,
                        topProducts: reportData.topProducts,
                    };
                    break;
                case 'agents':
                    reportData = await services_1.dashboardService.getAdminDashboard(dateRange);
                    reportData = {
                        agentPerformance: reportData.agentPerformance,
                        userStats: reportData.userStats,
                    };
                    break;
                default:
                    throw new app_errors_1.BadRequestError('Invalid report type. Available types: orders, revenue, inventory, agents');
            }
            logger_1.default.info('Dashboard report generated successfully', {
                adminId: req.user.userId,
                reportType,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', `${reportType} report generated successfully`, {
                data: {
                    reportType,
                    dateRange,
                    report: reportData,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Failed to generate dashboard report', {
                error: error.message,
                adminId: req.user?.userId,
                reportType: req.query.reportType,
                timestamp: new Date().toISOString(),
            });
            next(error);
        }
    }
}
exports.dashboardController = new DashboardController();
//# sourceMappingURL=dashboard.controller.js.map