"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sparePartController = void 0;
const spare_part_services_1 = require("../services/spare_part.services");
const response_1 = require("../utils/response");
const app_errors_1 = require("../errors/app_errors");
const enums_1 = require("../enums/enums");
const mongoose_1 = require("mongoose");
const logger_1 = __importDefault(require("../config/logger"));
class SparePartController {
    /**
     * @route   POST /api/v1/spare-parts
     * @desc    Create a new spare part
     * @access  Private (Admin only)
     */
    async createSparePart(req, res, next) {
        try {
            const { name, description, price, cost, category, compatibleCylinderTypes, barcode, minimumStockLevel, imageUrl, } = req.body;
            // Validation
            if (!name || !price || !cost || !category) {
                throw new app_errors_1.ValidationError('Name, price, cost, and category are required');
            }
            if (price <= 0 || cost <= 0) {
                throw new app_errors_1.ValidationError('Price and cost must be positive numbers');
            }
            if (!Object.values(enums_1.SparePartCategory).includes(category)) {
                throw new app_errors_1.ValidationError('Invalid category');
            }
            if (compatibleCylinderTypes && !Array.isArray(compatibleCylinderTypes)) {
                throw new app_errors_1.ValidationError('Compatible cylinder types must be an array');
            }
            const sparePart = await spare_part_services_1.sparePartService.createSparePart({
                name: name.trim(),
                description: description?.trim(),
                price: Number(price),
                cost: Number(cost),
                category,
                compatibleCylinderTypes,
                barcode: barcode?.trim(),
                minimumStockLevel: minimumStockLevel ? Number(minimumStockLevel) : undefined,
                imageUrl: imageUrl?.trim(),
            });
            logger_1.default.info('Spare part created successfully', {
                sparePartId: sparePart._id,
                name: sparePart.name,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 201, 'success', 'Spare part created successfully', {
                data: sparePart,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/spare-parts
     * @desc    Get all spare parts with filtering and pagination
     * @access  Private (All authenticated users)
     */
    async getSpareParts(req, res, next) {
        try {
            const { search, category, status, compatibleWith, lowStock, page = 1, limit = 10, } = req.query;
            // Validation
            const pageNum = Math.max(1, parseInt(page) || 1);
            const limitNum = Math.min(50, Math.max(1, parseInt(limit) || 10));
            const filters = {};
            if (search)
                filters.search = search;
            if (category)
                filters.category = category;
            if (status)
                filters.status = status;
            if (compatibleWith)
                filters.compatibleWith = compatibleWith;
            if (lowStock === 'true')
                filters.lowStock = true;
            const result = await spare_part_services_1.sparePartService.listSpareParts(filters, {
                page: pageNum,
                limit: limitNum,
            }, {
                userId: req.user?.userId.toString(),
                role: req.user?.role,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Spare parts retrieved successfully', {
                data: result.data,
                meta: {
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: result.total,
                        pages: Math.ceil(result.total / limitNum),
                    },
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/spare-parts/:id
     * @desc    Get spare part by ID
     * @access  Private (All authenticated users)
     */
    async getSparePartById(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid spare part ID');
            }
            const sparePart = await spare_part_services_1.sparePartService.getSparePartById(id);
            (0, response_1.sendResponse)(res, 200, 'success', 'Spare part retrieved successfully', {
                data: sparePart,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   PUT /api/v1/spare-parts/:id
     * @desc    Update spare part details
     * @access  Private (Admin only)
     */
    async updateSparePart(req, res, next) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid spare part ID');
            }
            // Validate numeric fields if provided
            if (updateData.price !== undefined && updateData.price <= 0) {
                throw new app_errors_1.ValidationError('Price must be positive');
            }
            if (updateData.cost !== undefined && updateData.cost <= 0) {
                throw new app_errors_1.ValidationError('Cost must be positive');
            }
            const sparePart = await spare_part_services_1.sparePartService.updateSparePart(id, updateData);
            logger_1.default.info('Spare part updated successfully', {
                sparePartId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Spare part updated successfully', {
                data: sparePart,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/spare-parts/:id/restock
     * @desc    Restock spare part
     * @access  Private (Admin only)
     */
    async restockSparePart(req, res, next) {
        try {
            const { id } = req.params;
            const { quantity } = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid spare part ID');
            }
            if (!quantity || quantity <= 0) {
                throw new app_errors_1.ValidationError('Quantity must be a positive number');
            }
            const sparePart = await spare_part_services_1.sparePartService.restock(id, Number(quantity));
            logger_1.default.info('Spare part restocked successfully', {
                sparePartId: id,
                quantity: Number(quantity),
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Spare part restocked successfully', {
                data: sparePart,
            });
        }
        catch (error) {
            next(error);
        }
    }
    // Note: Reservation, release, and sales operations are now handled automatically
    // through the order lifecycle. These methods have been removed to prevent
    // data inconsistencies and ensure proper audit trails through order tracking.
    /**
     * @route   PUT /api/v1/spare-parts/:id/discontinue
     * @desc    Discontinue a spare part
     * @access  Private (Admin only)
     */
    async discontinueSparePart(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid spare part ID');
            }
            const sparePart = await spare_part_services_1.sparePartService.discontinue(id);
            (0, response_1.sendResponse)(res, 200, 'success', 'Spare part discontinued successfully', {
                data: sparePart,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/spare-parts/low-stock
     * @desc    Get spare parts with low stock
     * @access  Private (Admin, Agent)
     */
    async getLowStockSpareParts(_req, res, next) {
        try {
            const spareParts = await spare_part_services_1.sparePartService.getLowStockAlerts();
            (0, response_1.sendResponse)(res, 200, 'success', 'Low stock spare parts retrieved successfully', {
                data: spareParts,
                meta: { count: spareParts.length },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/spare-parts/statistics
     * @desc    Get spare parts sales statistics
     * @access  Private (Admin only)
     */
    async getSparePartStatistics(_req, res, next) {
        try {
            const statistics = await spare_part_services_1.sparePartService.getSalesStatistics();
            (0, response_1.sendResponse)(res, 200, 'success', 'Spare part statistics retrieved successfully', {
                data: statistics,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/spare-parts/search
     * @desc    Search spare parts
     * @access  Private (All authenticated users)
     */
    async searchSpareParts(req, res, next) {
        try {
            const { q: query, limit = 10 } = req.query;
            if (!query || typeof query !== 'string') {
                throw new app_errors_1.ValidationError('Search query is required');
            }
            const limitNum = Math.min(50, Math.max(1, parseInt(limit) || 10));
            const spareParts = await spare_part_services_1.sparePartService.searchSpareParts(query, limitNum);
            (0, response_1.sendResponse)(res, 200, 'success', 'Search results retrieved successfully', {
                data: spareParts,
                meta: { count: spareParts.length },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.sparePartController = new SparePartController();
//# sourceMappingURL=spare_part.controller.js.map