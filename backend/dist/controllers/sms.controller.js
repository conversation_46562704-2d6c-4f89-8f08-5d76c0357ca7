"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.smsController = void 0;
const sms_services_1 = require("../services/sms.services");
const models_1 = require("../models");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
const enums_1 = require("../enums/enums");
const response_1 = require("../utils/response");
class SmsController {
    /**
     * Send SMS to a single recipient
     */
    async sendSms(req, res, next) {
        try {
            const { phoneNumber, message, senderId, refId, isOtp } = req.body;
            // Validate required fields
            if (!phoneNumber || !message) {
                throw new app_errors_1.BadRequestError('Phone number and message are required');
            }
            // Check if user has permission to send SMS
            if (req.user?.role !== enums_1.UserRole.ADMIN && req.user?.role !== enums_1.UserRole.AGENT) {
                throw new app_errors_1.BadRequestError('Insufficient permissions to send SMS');
            }
            const result = await sms_services_1.smsService.sendSms(phoneNumber, message, {
                senderId,
                refId,
                isOtp: isOtp || false,
            });
            logger_1.default.info('SMS sent successfully', {
                userId: req.user?.id,
                recipient: phoneNumber,
                messageId: result.messageId,
                isOtp: isOtp || false,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'SMS sent successfully', {
                data: {
                    messageId: result.messageId,
                    recipient: phoneNumber,
                    sentAt: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            logger_1.default.error('SMS sending failed', {
                userId: req.user?.id,
                error: error.message,
                phoneNumber: req.body.phoneNumber,
            });
            next(error);
        }
    }
    /**
     * Send SMS to multiple recipients
     */
    async sendBulkSms(req, res, next) {
        try {
            const { phoneNumbers, message, senderId, refId, isOtp } = req.body;
            // Validate required fields
            if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
                throw new app_errors_1.BadRequestError('Phone numbers array is required and cannot be empty');
            }
            if (!message) {
                throw new app_errors_1.BadRequestError('Message is required');
            }
            // Check if user has permission to send bulk SMS
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.BadRequestError('Only admins can send bulk SMS');
            }
            // Limit bulk SMS to prevent abuse
            // if (phoneNumbers.length > 100) {
            //   throw new BadRequestError('Maximum 100 recipients allowed per bulk SMS');
            // }
            const results = await sms_services_1.smsService.sendSmsToMany(phoneNumbers, message, {
                senderId,
                refId,
                isOtp: isOtp || false,
            });
            logger_1.default.info('Bulk SMS sent successfully', {
                userId: req.user?.id,
                recipientCount: phoneNumbers.length,
                messageIds: results,
                isOtp: isOtp || false,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Bulk SMS sent successfully', {
                data: {
                    messageIds: results,
                    recipientCount: phoneNumbers.length,
                    sentAt: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            logger_1.default.error('Bulk SMS sending failed', {
                userId: req.user?.id,
                error: error.message,
                recipientCount: req.body.phoneNumbers?.length || 0,
            });
            next(error);
        }
    }
    /**
     * Send SMS to users by role
     */
    async sendSmsToRole(req, res, next) {
        try {
            const { role, message, senderId, refId, isOtp, onlyActiveUsers } = req.body;
            // Validate required fields
            if (!role || !message) {
                throw new app_errors_1.BadRequestError('Role and message are required');
            }
            // Check if user has permission
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.BadRequestError('Only admins can send SMS to user roles');
            }
            // Validate role
            if (!Object.values(enums_1.UserRole).includes(role)) {
                throw new app_errors_1.BadRequestError('Invalid user role');
            }
            // Get users by role
            const query = { role };
            if (onlyActiveUsers) {
                query.isActive = true;
            }
            const users = await models_1.User.find(query).select('phone').lean();
            if (!users.length) {
                (0, response_1.sendResponse)(res, 200, 'success', 'No users found for the specified role', {
                    data: {
                        recipientCount: 0,
                        sentAt: new Date().toISOString(),
                    },
                });
                return;
            }
            const phoneNumbers = users.map(user => user.phone).filter(Boolean);
            if (!phoneNumbers.length) {
                (0, response_1.sendResponse)(res, 200, 'success', 'No valid phone numbers found for the specified role', {
                    data: {
                        recipientCount: 0,
                        sentAt: new Date().toISOString(),
                    },
                });
                return;
            }
            const results = await sms_services_1.smsService.sendSmsToMany(phoneNumbers, message, {
                senderId,
                refId,
                isOtp: isOtp || false,
            });
            logger_1.default.info('Role-based SMS sent successfully', {
                userId: req.user?.id,
                targetRole: role,
                recipientCount: phoneNumbers.length,
                messageIds: results,
                isOtp: isOtp || false,
            });
            (0, response_1.sendResponse)(res, 200, 'success', `SMS sent to all ${role} users successfully`, {
                data: {
                    messageIds: results,
                    recipientCount: phoneNumbers.length,
                    targetRole: role,
                    sentAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Role-based SMS sending failed', {
                userId: req.user?.id,
                error: error.message,
                targetRole: req.body.role,
            });
            next(error);
        }
    }
    /**
     * Get SMS service health status
     */
    async getHealthStatus(req, res, next) {
        try {
            // Check if user has permission
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.BadRequestError('Only admins can check SMS service health');
            }
            const isHealthy = await sms_services_1.smsService.checkHealth();
            const metrics = sms_services_1.smsService.getMetrics();
            (0, response_1.sendResponse)(res, 200, 'success', 'SMS service health status retrieved', {
                data: {
                    isHealthy,
                    metrics: {
                        sentCount: metrics.sentCount,
                        failedCount: metrics.failedCount,
                        successRate: Math.round(metrics.successRate * 100),
                        lastError: metrics.lastError?.message || null,
                    },
                    timestamp: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to get SMS health status', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
    /**
     * Get SMS service metrics
     */
    async getMetrics(req, res, next) {
        try {
            // Check if user has permission
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.BadRequestError('Only admins can view SMS metrics');
            }
            const metrics = sms_services_1.smsService.getMetrics();
            (0, response_1.sendResponse)(res, 200, 'success', 'SMS service metrics retrieved', {
                data: {
                    totalSent: metrics.sentCount,
                    totalFailed: metrics.failedCount,
                    successRate: Math.round(metrics.successRate * 100),
                    lastError: metrics.lastError
                        ? {
                            message: metrics.lastError.message,
                            timestamp: new Date().toISOString(),
                        }
                        : null,
                    retrievedAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to get SMS metrics', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
    /**
     * Send OTP to user
     */
    async sendOtp(req, res, next) {
        try {
            const { phoneNumber, otp, customMessage } = req.body;
            // Validate required fields
            if (!phoneNumber || !otp) {
                throw new app_errors_1.BadRequestError('Phone number and OTP are required');
            }
            // Check if user has permission
            if (req.user?.role !== enums_1.UserRole.ADMIN && req.user?.role !== enums_1.UserRole.AGENT) {
                throw new app_errors_1.BadRequestError('Insufficient permissions to send OTP');
            }
            const message = customMessage || `Your verification code is ${otp}. Do not share this code with anyone.`;
            const result = await sms_services_1.smsService.sendSms(phoneNumber, message, {
                isOtp: true,
                refId: `otp_${Date.now()}`,
            });
            logger_1.default.info('OTP sent successfully', {
                userId: req.user?.id,
                recipient: phoneNumber,
                messageId: result.messageId,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'OTP sent successfully', {
                data: {
                    messageId: result.messageId,
                    recipient: phoneNumber,
                    sentAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('OTP sending failed', {
                userId: req.user?.id,
                error: error.message,
                phoneNumber: req.body.phoneNumber,
            });
            next(error);
        }
    }
}
exports.smsController = new SmsController();
//# sourceMappingURL=sms.controller.js.map