"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationManagementController = void 0;
const notification_services_1 = require("../services/notification.services");
const notification_utils_1 = require("../utils/notification_utils");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Notification Management Controller
 * Handles notification management operations
 */
class NotificationManagementController {
    /**
     * Send notification to a specific user
     * @route POST /api/v1/notification-management/send-to-user
     */
    async sendToUser(req, res) {
        try {
            const { userId, title, body, data, imageUrl } = req.body;
            if (!userId || !title || !body) {
                throw new app_errors_1.BadRequestError('User ID, title, and body are required');
            }
            const result = await notification_services_1.notificationService.sendToUser(userId, {
                title,
                body,
                data,
                imageUrl,
            });
            res.status(200).json({
                status: 'success',
                message: 'Notification sent successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.sendToUser error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Send notification to topic
     * @route POST /api/v1/notification-management/send-to-topic
     */
    async sendToTopic(req, res) {
        try {
            const { topic, title, body, data, imageUrl, onlyActiveUsers } = req.body;
            if (!topic || !title || !body) {
                throw new app_errors_1.BadRequestError('Topic, title, and body are required');
            }
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            const result = await notification_services_1.notificationService.sendToTopic(topic, { title, body, data, imageUrl }, { onlyActiveUsers });
            res.status(200).json({
                status: 'success',
                message: 'Topic notification sent successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.sendToTopic error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get notification history
     * @route GET /api/v1/notification-management/history
     */
    async getNotificationHistory(req, res) {
        try {
            const { userId, status, topic, startDate, endDate, page, limit, sortBy, sortOrder, } = req.query;
            const filters = {};
            if (userId)
                filters.userId = userId;
            if (status)
                filters.status = status;
            if (topic)
                filters.topic = topic;
            if (startDate)
                filters.startDate = new Date(startDate);
            if (endDate)
                filters.endDate = new Date(endDate);
            const pagination = {
                page: page ? parseInt(page) : undefined,
                limit: limit ? parseInt(limit) : undefined,
                sortBy: sortBy,
                sortOrder: sortOrder,
            };
            const result = await notification_services_1.notificationService.getNotificationHistory(filters, pagination);
            res.status(200).json({
                status: 'success',
                message: 'Notification history retrieved successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.getNotificationHistory error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get notification statistics
     * @route GET /api/v1/notification-management/stats
     */
    async getNotificationStats(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const filters = {};
            if (startDate)
                filters.startDate = new Date(startDate);
            if (endDate)
                filters.endDate = new Date(endDate);
            const result = await notification_services_1.notificationService.getNotificationStats(filters);
            res.status(200).json({
                status: 'success',
                message: 'Notification statistics retrieved successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.getNotificationStats error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Update user's FCM token
     * @route PUT /api/v1/notification-management/fcm-token
     */
    async updateFcmToken(req, res) {
        try {
            const { token } = req.body;
            const userId = req.user?.id;
            if (!token) {
                throw new app_errors_1.BadRequestError('FCM token is required');
            }
            const result = await notification_services_1.notificationService.updateFcmToken(userId, token);
            res.status(200).json({
                status: 'success',
                message: 'FCM token updated successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.updateFcmToken error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Subscribe user to topic
     * @route POST /api/v1/notification-management/subscribe
     */
    async subscribeToTopic(req, res) {
        try {
            const { topic } = req.body;
            const userId = req.user?.id;
            if (!topic) {
                throw new app_errors_1.BadRequestError('Topic is required');
            }
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            const result = await notification_services_1.notificationService.subscribeToTopic(userId, topic);
            res.status(200).json({
                status: 'success',
                message: 'Subscribed to topic successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.subscribeToTopic error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Toggle notifications for user
     * @route PUT /api/v1/notification-management/toggle
     */
    async toggleNotifications(req, res) {
        try {
            const { enabled } = req.body;
            const userId = req.user?.id;
            if (typeof enabled !== 'boolean') {
                throw new app_errors_1.BadRequestError('Enabled flag is required and must be boolean');
            }
            const result = await notification_services_1.notificationService.toggleNotifications(userId, enabled);
            res.status(200).json({
                status: 'success',
                message: `Notifications ${enabled ? 'enabled' : 'disabled'} successfully`,
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.toggleNotifications error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Mark notification as read
     * @route PUT /api/v1/notification-management/:id/read
     */
    async markAsRead(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user?.id;
            if (!id) {
                throw new app_errors_1.BadRequestError('Notification ID is required');
            }
            const result = await notification_services_1.notificationService.markAsRead(id, userId);
            res.status(200).json({
                status: 'success',
                message: 'Notification marked as read',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.markAsRead error', {
                error: error.message,
                notificationId: req.params.id,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get user's notifications
     * @route GET /api/v1/notification-management/user-notifications
     */
    async getUserNotifications(req, res) {
        try {
            const { unreadOnly, limit, page } = req.query;
            const userId = req.user?.id;
            const options = {};
            if (unreadOnly === 'true')
                options.unreadOnly = true;
            if (limit)
                options.limit = parseInt(limit);
            if (page)
                options.page = parseInt(page);
            const result = await notification_services_1.notificationService.getUserNotifications(userId, options);
            res.status(200).json({
                status: 'success',
                message: 'User notifications retrieved successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.getUserNotifications error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get notification templates (mock data for now)
     * @route GET /api/v1/notification-management/templates
     */
    async getNotificationTemplates(req, res) {
        try {
            // Mock templates - in a real app, these would come from a database
            const templates = [
                {
                    id: '1',
                    name: 'Order Confirmed',
                    title: 'Order Confirmed',
                    body: 'Your order has been confirmed and is being processed.',
                    category: 'Order Updates',
                    variables: ['order_id', 'customer_name'],
                },
                {
                    id: '2',
                    name: 'Out for Delivery',
                    title: 'Order Out for Delivery',
                    body: 'Your order is out for delivery. Expected arrival: {time}',
                    category: 'Delivery Updates',
                    variables: ['time', 'driver_name'],
                },
                {
                    id: '3',
                    name: 'Payment Reminder',
                    title: 'Payment Pending',
                    body: 'Payment pending for order #{order_id}. Please complete payment.',
                    category: 'Payment',
                    variables: ['order_id', 'amount'],
                },
                {
                    id: '4',
                    name: 'Welcome Message',
                    title: 'Welcome!',
                    body: 'Welcome to Gas Delivery Service! Start ordering now.',
                    category: 'Welcome',
                    variables: ['user_name'],
                },
                {
                    id: '5',
                    name: 'System Maintenance',
                    title: 'System Maintenance',
                    body: 'System will be under maintenance from {start_time} to {end_time}.',
                    category: 'System',
                    variables: ['start_time', 'end_time'],
                },
            ];
            res.status(200).json({
                status: 'success',
                message: 'Notification templates retrieved successfully',
                data: { templates },
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.getNotificationTemplates error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get notification dashboard data
     * @route GET /api/v1/notification-management/dashboard
     */
    async getNotificationDashboard(req, res) {
        try {
            const { period } = req.query;
            // Calculate date range based on period
            const endDate = new Date();
            const startDate = new Date();
            switch (period) {
                case 'week':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case 'month':
                    startDate.setMonth(endDate.getMonth() - 1);
                    break;
                case 'year':
                    startDate.setFullYear(endDate.getFullYear() - 1);
                    break;
                default:
                    startDate.setDate(endDate.getDate() - 30); // Default to 30 days
            }
            const [stats, recentNotifications] = await Promise.all([
                notification_services_1.notificationService.getNotificationStats({ startDate, endDate }),
                notification_services_1.notificationService.getNotificationHistory({ startDate, endDate }, { page: 1, limit: 10, sortBy: 'createdAt', sortOrder: 'desc' }),
            ]);
            res.status(200).json({
                status: 'success',
                message: 'Notification dashboard data retrieved successfully',
                data: {
                    stats,
                    recentNotifications: recentNotifications.notifications,
                    period,
                },
            });
        }
        catch (error) {
            logger_1.default.error('NotificationManagementController.getNotificationDashboard error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
}
exports.notificationManagementController = new NotificationManagementController();
//# sourceMappingURL=notification_management.controller.js.map