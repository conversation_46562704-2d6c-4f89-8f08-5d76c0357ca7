"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.smsManagementController = void 0;
const sms_management_services_1 = require("../services/sms_management.services");
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * SMS Management Controller
 * Handles SMS management operations with database tracking
 */
class SmsManagementController {
    /**
     * Send SMS to a single recipient
     * @route POST /api/v1/sms-management/send
     */
    async sendSms(req, res) {
        try {
            const { phoneNumber, message, type, isOtp, senderIdText, refId, scheduledAt } = req.body;
            const senderId = req.user?.id;
            if (!phoneNumber || !message) {
                throw new app_errors_1.BadRequestError('Phone number and message are required');
            }
            const result = await sms_management_services_1.smsManagementService.sendSms(senderId, phoneNumber, message, {
                type: type,
                isOtp,
                senderIdText,
                refId,
                scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
            });
            res.status(200).json({
                status: 'success',
                message: 'SMS sent successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.sendSms error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Send bulk SMS
     * @route POST /api/v1/sms-management/send-bulk
     */
    async sendBulkSms(req, res) {
        try {
            const { phoneNumbers, message, type, isOtp, senderIdText, refId, scheduledAt } = req.body;
            const senderId = req.user?.id;
            if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
                throw new app_errors_1.BadRequestError('Phone numbers array is required');
            }
            if (!message) {
                throw new app_errors_1.BadRequestError('Message is required');
            }
            if (phoneNumbers.length > 1000) {
                throw new app_errors_1.BadRequestError('Maximum 1000 recipients allowed per bulk SMS');
            }
            const result = await sms_management_services_1.smsManagementService.sendBulkSms(senderId, phoneNumbers, message, {
                type: type,
                isOtp,
                senderIdText,
                refId,
                scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
            });
            res.status(200).json({
                status: 'success',
                message: 'Bulk SMS initiated successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.sendBulkSms error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get SMS history
     * @route GET /api/v1/sms-management/history
     */
    async getSmsHistory(req, res) {
        try {
            const { senderId, status, type, startDate, endDate, phoneNumber, page, limit, sortBy, sortOrder, } = req.query;
            const filters = {};
            if (senderId)
                filters.senderId = senderId;
            if (status)
                filters.status = status;
            if (type)
                filters.type = type;
            if (phoneNumber)
                filters.phoneNumber = phoneNumber;
            if (startDate)
                filters.startDate = new Date(startDate);
            if (endDate)
                filters.endDate = new Date(endDate);
            const pagination = {
                page: page ? parseInt(page) : undefined,
                limit: limit ? parseInt(limit) : undefined,
                sortBy: sortBy,
                sortOrder: sortOrder,
            };
            const result = await sms_management_services_1.smsManagementService.getSmsHistory(filters, pagination);
            res.status(200).json({
                status: 'success',
                message: 'SMS history retrieved successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.getSmsHistory error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get SMS statistics
     * @route GET /api/v1/sms-management/stats
     */
    async getSmsStats(req, res) {
        try {
            const { senderId, startDate, endDate } = req.query;
            const filters = {};
            if (senderId)
                filters.senderId = senderId;
            if (startDate)
                filters.startDate = new Date(startDate);
            if (endDate)
                filters.endDate = new Date(endDate);
            const result = await sms_management_services_1.smsManagementService.getSmsStats(filters);
            res.status(200).json({
                status: 'success',
                message: 'SMS statistics retrieved successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.getSmsStats error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get SMS details by ID
     * @route GET /api/v1/sms-management/:id
     */
    async getSmsById(req, res) {
        try {
            const { id } = req.params;
            if (!id) {
                throw new app_errors_1.BadRequestError('SMS ID is required');
            }
            const result = await sms_management_services_1.smsManagementService.getSmsById(id);
            res.status(200).json({
                status: 'success',
                message: 'SMS details retrieved successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.getSmsById error', {
                error: error.message,
                smsId: req.params.id,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Cancel scheduled SMS
     * @route PUT /api/v1/sms-management/:id/cancel
     */
    async cancelScheduledSms(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user?.id;
            if (!id) {
                throw new app_errors_1.BadRequestError('SMS ID is required');
            }
            const result = await sms_management_services_1.smsManagementService.cancelScheduledSms(id, userId);
            res.status(200).json({
                status: 'success',
                message: 'SMS cancelled successfully',
                data: result,
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.cancelScheduledSms error', {
                error: error.message,
                smsId: req.params.id,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get SMS templates (mock data for now)
     * @route GET /api/v1/sms-management/templates
     */
    async getSmsTemplates(req, res) {
        try {
            // Mock templates - in a real app, these would come from a database
            const templates = [
                {
                    id: '1',
                    name: 'Order Confirmation',
                    message: 'Your order #{order_id} has been confirmed. Expected delivery: {delivery_date}',
                    type: enums_1.SmsType.ORDER_CONFIRMATION,
                    variables: ['order_id', 'delivery_date'],
                },
                {
                    id: '2',
                    name: 'Delivery Notification',
                    message: 'Your order is out for delivery. Track: {tracking_link}',
                    type: enums_1.SmsType.DELIVERY_UPDATE,
                    variables: ['tracking_link'],
                },
                {
                    id: '3',
                    name: 'Welcome Message',
                    message: 'Welcome to Gas Delivery Service! Your account is now active.',
                    type: enums_1.SmsType.GENERAL,
                    variables: [],
                },
                {
                    id: '4',
                    name: 'OTP Verification',
                    message: 'Your verification code is: {otp_code}. Valid for 5 minutes.',
                    type: enums_1.SmsType.OTP,
                    variables: ['otp_code'],
                },
                {
                    id: '5',
                    name: 'Promotional Offer',
                    message: 'Special offer! Get {discount}% off your next order. Use code: {promo_code}',
                    type: enums_1.SmsType.PROMOTIONAL,
                    variables: ['discount', 'promo_code'],
                },
            ];
            res.status(200).json({
                status: 'success',
                message: 'SMS templates retrieved successfully',
                data: { templates },
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.getSmsTemplates error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
    /**
     * Get SMS dashboard data
     * @route GET /api/v1/sms-management/dashboard
     */
    async getSmsDashboard(req, res) {
        try {
            const { period } = req.query;
            // Calculate date range based on period
            const endDate = new Date();
            const startDate = new Date();
            switch (period) {
                case 'week':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case 'month':
                    startDate.setMonth(endDate.getMonth() - 1);
                    break;
                case 'year':
                    startDate.setFullYear(endDate.getFullYear() - 1);
                    break;
                default:
                    startDate.setDate(endDate.getDate() - 30); // Default to 30 days
            }
            const [stats, recentSms] = await Promise.all([
                sms_management_services_1.smsManagementService.getSmsStats({ startDate, endDate }),
                sms_management_services_1.smsManagementService.getSmsHistory({ startDate, endDate }, { page: 1, limit: 10, sortBy: 'createdAt', sortOrder: 'desc' }),
            ]);
            res.status(200).json({
                status: 'success',
                message: 'SMS dashboard data retrieved successfully',
                data: {
                    stats,
                    recentSms: recentSms.sms,
                    period,
                },
            });
        }
        catch (error) {
            logger_1.default.error('SmsManagementController.getSmsDashboard error', {
                error: error.message,
                userId: req.user?.id,
            });
            throw error;
        }
    }
}
exports.smsManagementController = new SmsManagementController();
//# sourceMappingURL=sms_management.controller.js.map