"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cylinder = void 0;
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const mongoose_1 = require("mongoose");
const CylinderSchema = new mongoose_1.Schema({
    type: {
        type: String,
        enum: Object.values(enums_1.CylinderType),
        required: true,
    },
    material: {
        type: String,
        enum: Object.values(enums_1.CylinderMaterial),
        required: true,
    },
    status: {
        type: String,
        enum: Object.values(enums_1.CylinderStatus),
        default: enums_1.CylinderStatus.Active,
    },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    imageUrl: { type: String },
    description: { type: String },
    quantity: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    lastRestockedAt: Date,
    minimumStockLevel: { type: Number, default: 10, min: 0 },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});
// Compound index
CylinderSchema.index({ type: 1, material: 1, status: 1 });
CylinderSchema.index({ quantity: 1, minimumStockLevel: 1 });
CylinderSchema.index({ type: 1, status: 1 }); // Fast type/status queries
CylinderSchema.index({ quantity: 1 }); // Low stock alerts
CylinderSchema.index({ quantity: 1, reserved: 1 }); // Helps with availableQuantity calculations
// Virtuals
CylinderSchema.virtual('availableQuantity').get(function () {
    return Math.max(0, this.quantity - this.reserved);
});
// Hook for ensuring data consisitency
CylinderSchema.pre('save', function (next) {
    if (this.reserved > this.quantity) {
        // this.reserved = this.quantity;
        throw new app_errors_1.BadRequestError('Reserved quantity cannot exceed total quantity');
    }
    next();
});
exports.Cylinder = (0, mongoose_1.model)('Cylinder', CylinderSchema);
//# sourceMappingURL=cylinder.model.js.map