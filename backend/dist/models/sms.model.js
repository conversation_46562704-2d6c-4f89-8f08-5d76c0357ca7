"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Sms = void 0;
const mongoose_1 = require("mongoose");
const enums_1 = require("../enums/enums");
const SmsSchema = new mongoose_1.Schema({
    senderId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    recipients: [
        {
            phoneNumber: { type: String, required: true },
            messageId: String,
            status: {
                type: String,
                enum: Object.values(enums_1.SmsStatus),
                default: enums_1.SmsStatus.PENDING,
            },
            error: String,
            deliveredAt: Date,
        },
    ],
    message: {
        type: String,
        required: true,
        maxlength: 459,
    },
    type: {
        type: String,
        enum: Object.values(enums_1.SmsType),
        default: enums_1.SmsType.GENERAL,
    },
    isOtp: {
        type: Boolean,
        default: false,
    },
    senderIdText: String, // SMS provider sender ID
    refId: String,
    totalRecipients: {
        type: Number,
        required: true,
    },
    successCount: {
        type: Number,
        default: 0,
    },
    failureCount: {
        type: Number,
        default: 0,
    },
    status: {
        type: String,
        enum: Object.values(enums_1.SmsStatus),
        default: enums_1.SmsStatus.PENDING,
    },
    scheduledAt: Date,
    sentAt: Date,
    completedAt: Date,
    metadata: {
        type: Map,
        of: mongoose_1.Schema.Types.Mixed,
    },
}, {
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (_doc, ret) => {
            delete ret.__v;
            return ret;
        },
    },
});
// Indexes for faster queries
SmsSchema.index({ senderId: 1 });
SmsSchema.index({ status: 1 });
SmsSchema.index({ type: 1 });
SmsSchema.index({ createdAt: -1 });
SmsSchema.index({ 'recipients.phoneNumber': 1 });
// Virtual for success rate
SmsSchema.virtual('successRate').get(function () {
    if (this.totalRecipients === 0)
        return 0;
    return (this.successCount / this.totalRecipients) * 100;
});
exports.Sms = (0, mongoose_1.model)('Sms', SmsSchema);
//# sourceMappingURL=sms.model.js.map