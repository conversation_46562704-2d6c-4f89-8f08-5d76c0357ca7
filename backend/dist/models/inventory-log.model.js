"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryLog = void 0;
const mongoose_1 = require("mongoose");
const enums_1 = require("../enums/enums");
const InventoryLogSchema = new mongoose_1.Schema({
    inventoryId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Inventory', required: true },
    action: { type: String, enum: Object.values(enums_1.InventoryAction), required: true },
    quantity: { type: Number, required: true },
    actorId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    notes: String
}, { timestamps: true });
exports.InventoryLog = (0, mongoose_1.model)('InventoryLog', InventoryLogSchema);
//# sourceMappingURL=inventory-log.model.js.map