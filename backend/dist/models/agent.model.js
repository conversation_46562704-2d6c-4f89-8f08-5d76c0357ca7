"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = void 0;
const mongoose_1 = require("mongoose");
const AgentSchema = new mongoose_1.Schema({
    userId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
    vehicle: {
        type: { type: String, enum: ['bike', 'truck', 'van'], required: true },
        number: { type: String, required: true },
    },
    isOnDuty: { type: Boolean, default: false },
    lastKnownLocation: {
        type: { type: String, enum: ['Point'], default: 'Point' },
        coordinates: { type: [Number] },
    },
    rating: { type: Number, min: 1, max: 5 },
}, { timestamps: true });
// GeoJSON Index
AgentSchema.index({ lastKnownLocation: '2dsphere' });
exports.Agent = (0, mongoose_1.model)('Agent', AgentSchema);
//# sourceMappingURL=agent.model.js.map