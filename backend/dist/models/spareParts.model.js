"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SparePart = void 0;
const enums_1 = require("../enums/enums");
const mongoose_1 = require("mongoose");
const SparePartSchema = new mongoose_1.Schema({
    name: { type: String, required: true, unique: true, trim: true },
    description: { type: String },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    stock: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    status: {
        type: String,
        enum: Object.values(enums_1.SparePartStatus),
        default: enums_1.SparePartStatus.AVAILABLE,
    },
    category: {
        type: String,
        enum: Object.values(enums_1.SparePartCategory),
        required: true,
    },
    compatibleCylinderTypes: [
        {
            type: String,
            enum: Object.values(enums_1.CylinderType),
        },
    ],
    barcode: { type: String, unique: true, sparse: true },
    minimumStockLevel: { type: Number, default: 5 },
    lastRestockedAt: { type: Date },
    imageUrl: { type: String },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});
// Indexes
SparePartSchema.index({ status: 1 });
SparePartSchema.index({ stock: 1 });
SparePartSchema.index({ category: 1 });
SparePartSchema.index({ compatibleCylinderTypes: 1 });
SparePartSchema.index({ name: 'text', description: 'text', barcode: 'text' }, { weights: { name: 3, barcode: 2, description: 1 } });
// Virtuals
SparePartSchema.virtual('availableQuantity').get(function () {
    return Math.max(0, this.stock - this.reserved);
});
// Hooks
SparePartSchema.pre('save', function (next) {
    // Auto-update status based on stock level
    const available = this.get('availableQuantity');
    if (available <= 0) {
        this.status = enums_1.SparePartStatus.OUT_OF_STOCK;
    }
    else if (available <= this.minimumStockLevel) {
        this.status = enums_1.SparePartStatus.LOW_STOCK;
    }
    else {
        this.status = enums_1.SparePartStatus.AVAILABLE;
    }
    next();
});
SparePartSchema.path('price').validate(function (value) {
    return value >= this.cost;
}, 'Price must be >= cost');
exports.SparePart = (0, mongoose_1.model)('SparePart', SparePartSchema);
//# sourceMappingURL=spareParts.model.js.map