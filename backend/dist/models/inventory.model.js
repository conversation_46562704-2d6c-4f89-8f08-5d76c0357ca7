"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Inventory = void 0;
const mongoose_1 = require("mongoose");
const enums_1 = require("../enums/enums");
const InventorySchema = new mongoose_1.Schema({
    cylinderType: {
        type: String,
        enum: Object.values(enums_1.CylinderType),
        required: true,
    },
    cylinderMaterial: {
        type: String,
        enum: Object.values(enums_1.CylinderMaterial),
        required: true,
    },
    currentStock: { type: Number, default: 0, min: 0 },
    lowStockThreshold: { type: Number, default: 10 },
    warehouse: {
        type: String,
        enum: Object.values(enums_1.Warehouse),
        default: enums_1.Warehouse.MAIN,
        // required: true, 
    },
    lastRestocked: Date,
    imageUrl: {
        type: String,
        default: '/images/cylinders/default.png',
    },
}, { timestamps: true });
InventorySchema.index({ cylinderType: 1, cylinderMaterial: 1 }, { unique: true });
exports.Inventory = (0, mongoose_1.model)('Inventory', InventorySchema);
//# sourceMappingURL=inventory.model.js.map