"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorize = exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const app_errors_1 = require("../errors/app_errors");
const env_config_1 = require("../config/env_config");
const index_1 = require("../models/index");
const authenticate = async (req, _res, next) => {
    try {
        // Get token from header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new app_errors_1.UnauthorizedError('Authentication required');
        }
        const token = authHeader.split(' ')[1];
        if (!token) {
            throw new app_errors_1.UnauthorizedError('Authentication token missing');
        }
        // Verify token
        const decoded = jsonwebtoken_1.default.verify(token, env_config_1.config.server.jwtSecret);
        // Check if user exists
        const user = await index_1.User.findById(decoded.userId).select('_id role');
        if (!user) {
            throw new app_errors_1.UnauthorizedError('User not found');
        }
        // Add user info to request
        req.user = decoded;
        console.log('User authenticated', {
            userId: decoded.userId,
            role: decoded.role,
        });
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            next(new app_errors_1.UnauthorizedError('Invalid authentication token'));
        }
        else if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            next(new app_errors_1.UnauthorizedError('Authentication token expired'));
        }
        else {
            next(error);
        }
    }
};
exports.authenticate = authenticate;
/**
 * Authorization middleware to check user roles
 */
const authorize = (allowedRoles) => {
    return (req, _res, next) => {
        try {
            if (!req.user) {
                throw new app_errors_1.UnauthorizedError('Authentication required');
            }
            if (!allowedRoles.includes(req.user.role)) {
                throw new app_errors_1.ForbiddenError('Insufficient permissions');
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.authorize = authorize;
//# sourceMappingURL=auth.middleware.js.map