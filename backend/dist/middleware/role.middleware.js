"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRole = void 0;
const app_errors_1 = require("../errors/app_errors");
const validateRole = (allowedRoles) => {
    return (req, res, next) => {
        try {
            // log user role and allowed roles and userId
            console.log('User role and allowed roles', req.user?.role, allowedRoles);
            console.log('User ID', req.user?.userId);
            // Check if user exists in request (should be added by authenticate middleware)
            if (!req.user) {
                throw new app_errors_1.UnauthorizedError('Authentication required');
            }
            // Check if user role is in allowed roles
            if (!allowedRoles.includes(req.user.role)) {
                console.log('User role not allowed', req.user.role, allowedRoles);
                throw new app_errors_1.UnauthorizedError('You do not have permission to access this resource');
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.validateRole = validateRole;
//# sourceMappingURL=role.middleware.js.map