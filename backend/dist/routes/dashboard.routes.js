"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const dashboard_controller_1 = require("../controllers/dashboard.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const dashboardRouter = (0, express_1.Router)();
/**
 * Dashboard Routes
 * All routes require authentication
 */
// General dashboard summary for current user (based on their role)
dashboardRouter.get('/summary', auth_middleware_1.authenticate, dashboard_controller_1.dashboardController.getDashboardSummary);
// Admin dashboard routes
dashboardRouter.get('/admin', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), dashboard_controller_1.dashboardController.getAdminDashboard);
// Admin reports endpoint
dashboardRouter.get('/reports', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), dashboard_controller_1.dashboardController.getDashboardReports);
// Agent dashboard routes
// Agents can access their own dashboard, admins can access any agent's dashboard
dashboardRouter.get('/agent/:agentId', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), dashboard_controller_1.dashboardController.getAgentDashboard);
// Agent dashboard for current user (if agent)
dashboardRouter.get('/agent', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.AGENT]), dashboard_controller_1.dashboardController.getAgentDashboard);
// User/Customer dashboard routes
// Customers can access their own dashboard, admins can access any user's dashboard
dashboardRouter.get('/user/:userId', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.CUSTOMER]), dashboard_controller_1.dashboardController.getUserDashboard);
// User dashboard for current user (if customer)
dashboardRouter.get('/customer', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.CUSTOMER]), dashboard_controller_1.dashboardController.getUserDashboard);
exports.default = dashboardRouter;
//# sourceMappingURL=dashboard.routes.js.map