"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationManagementRouter = void 0;
const express_1 = require("express");
const notification_management_controller_1 = require("../controllers/notification_management.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const enums_1 = require("../enums/enums");
const notificationManagementRouter = (0, express_1.Router)();
exports.notificationManagementRouter = notificationManagementRouter;
// Apply authentication middleware to all routes
notificationManagementRouter.use(auth_middleware_1.authenticate);
/**
 * @route   POST /api/v1/notification-management/send-to-user
 * @desc    Send notification to a specific user
 * @access  Private (Admin, Agent)
 * @body    { userId, title, body, data?, imageUrl? }
 */
notificationManagementRouter.post('/send-to-user', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), notification_management_controller_1.notificationManagementController.sendToUser);
/**
 * @route   POST /api/v1/notification-management/send-to-topic
 * @desc    Send notification to topic subscribers
 * @access  Private (Admin only)
 * @body    { topic, title, body, data?, imageUrl?, onlyActiveUsers? }
 */
notificationManagementRouter.post('/send-to-topic', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN]), notification_management_controller_1.notificationManagementController.sendToTopic);
/**
 * @route   GET /api/v1/notification-management/history
 * @desc    Get notification history with pagination and filtering
 * @access  Private (Admin, Agent)
 * @query   { userId?, status?, topic?, startDate?, endDate?, page?, limit?, sortBy?, sortOrder? }
 */
notificationManagementRouter.get('/history', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), notification_management_controller_1.notificationManagementController.getNotificationHistory);
/**
 * @route   GET /api/v1/notification-management/stats
 * @desc    Get notification statistics
 * @access  Private (Admin, Agent)
 * @query   { startDate?, endDate? }
 */
notificationManagementRouter.get('/stats', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), notification_management_controller_1.notificationManagementController.getNotificationStats);
/**
 * @route   GET /api/v1/notification-management/dashboard
 * @desc    Get notification dashboard data
 * @access  Private (Admin, Agent)
 * @query   { period? }
 */
notificationManagementRouter.get('/dashboard', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), notification_management_controller_1.notificationManagementController.getNotificationDashboard);
/**
 * @route   GET /api/v1/notification-management/templates
 * @desc    Get notification templates
 * @access  Private (Admin, Agent)
 */
notificationManagementRouter.get('/templates', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), notification_management_controller_1.notificationManagementController.getNotificationTemplates);
/**
 * @route   PUT /api/v1/notification-management/fcm-token
 * @desc    Update user's FCM token
 * @access  Private (All authenticated users)
 * @body    { token }
 */
notificationManagementRouter.put('/fcm-token', notification_management_controller_1.notificationManagementController.updateFcmToken);
/**
 * @route   POST /api/v1/notification-management/subscribe
 * @desc    Subscribe user to notification topic
 * @access  Private (All authenticated users)
 * @body    { topic }
 */
notificationManagementRouter.post('/subscribe', notification_management_controller_1.notificationManagementController.subscribeToTopic);
/**
 * @route   PUT /api/v1/notification-management/toggle
 * @desc    Toggle notifications for user
 * @access  Private (All authenticated users)
 * @body    { enabled }
 */
notificationManagementRouter.put('/toggle', notification_management_controller_1.notificationManagementController.toggleNotifications);
/**
 * @route   PUT /api/v1/notification-management/:id/read
 * @desc    Mark notification as read
 * @access  Private (All authenticated users)
 */
notificationManagementRouter.put('/:id/read', notification_management_controller_1.notificationManagementController.markAsRead);
/**
 * @route   GET /api/v1/notification-management/user-notifications
 * @desc    Get user's notifications
 * @access  Private (All authenticated users)
 * @query   { unreadOnly?, limit?, page? }
 */
notificationManagementRouter.get('/user-notifications', notification_management_controller_1.notificationManagementController.getUserNotifications);
//# sourceMappingURL=notification_management.routes.js.map