"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.smsManagementRouter = void 0;
const express_1 = require("express");
const sms_management_controller_1 = require("../controllers/sms_management.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const enums_1 = require("../enums/enums");
const smsManagementRouter = (0, express_1.Router)();
exports.smsManagementRouter = smsManagementRouter;
// Apply authentication middleware to all routes
smsManagementRouter.use(auth_middleware_1.authenticate);
/**
 * @route   POST /api/v1/sms-management/send
 * @desc    Send SMS to a single recipient with database tracking
 * @access  Private (Admin, Agent)
 * @body    { phoneNumber, message, type?, isOtp?, senderIdText?, refId?, scheduledAt? }
 */
smsManagementRouter.post('/send', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.sendSms);
/**
 * @route   POST /api/v1/sms-management/send-bulk
 * @desc    Send bulk SMS with database tracking
 * @access  Private (Admin only)
 * @body    { phoneNumbers[], message, type?, isOtp?, senderIdText?, refId?, scheduledAt? }
 */
smsManagementRouter.post('/send-bulk', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN]), sms_management_controller_1.smsManagementController.sendBulkSms);
/**
 * @route   GET /api/v1/sms-management/history
 * @desc    Get SMS history with pagination and filtering
 * @access  Private (Admin, Agent)
 * @query   { senderId?, status?, type?, startDate?, endDate?, phoneNumber?, page?, limit?, sortBy?, sortOrder? }
 */
smsManagementRouter.get('/history', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.getSmsHistory);
/**
 * @route   GET /api/v1/sms-management/stats
 * @desc    Get SMS statistics
 * @access  Private (Admin, Agent)
 * @query   { senderId?, startDate?, endDate? }
 */
smsManagementRouter.get('/stats', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.getSmsStats);
/**
 * @route   GET /api/v1/sms-management/dashboard
 * @desc    Get SMS dashboard data
 * @access  Private (Admin, Agent)
 * @query   { period? }
 */
smsManagementRouter.get('/dashboard', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.getSmsDashboard);
/**
 * @route   GET /api/v1/sms-management/templates
 * @desc    Get SMS templates
 * @access  Private (Admin, Agent)
 */
smsManagementRouter.get('/templates', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.getSmsTemplates);
/**
 * @route   GET /api/v1/sms-management/:id
 * @desc    Get SMS details by ID
 * @access  Private (Admin, Agent)
 */
smsManagementRouter.get('/:id', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.getSmsById);
/**
 * @route   PUT /api/v1/sms-management/:id/cancel
 * @desc    Cancel scheduled SMS
 * @access  Private (Admin, Agent - own SMS only unless Admin)
 */
smsManagementRouter.put('/:id/cancel', (0, auth_middleware_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), sms_management_controller_1.smsManagementController.cancelScheduledSms);
//# sourceMappingURL=sms_management.routes.js.map