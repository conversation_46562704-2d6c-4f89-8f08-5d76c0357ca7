"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_routes_1 = __importDefault(require("./user.routes"));
const sms_routes_1 = __importDefault(require("./sms.routes"));
const notification_routes_1 = __importDefault(require("./notification.routes"));
const order_routes_1 = __importDefault(require("./order.routes"));
const spare_part_routes_1 = __importDefault(require("./spare_part.routes"));
const package_routes_1 = __importDefault(require("./package.routes"));
const cylinder_routes_1 = __importDefault(require("./cylinder.routes"));
const payment_routes_1 = __importDefault(require("./payment.routes"));
const dashboard_routes_1 = __importDefault(require("./dashboard.routes"));
const sms_management_routes_1 = require("./sms_management.routes");
const notification_management_routes_1 = require("./notification_management.routes");
const appRouter = (0, express_1.Router)();
const baseRoute = '/api/v1';
// Existing routes
appRouter.use(`${baseRoute}/users`, user_routes_1.default);
appRouter.use(`${baseRoute}/sms`, sms_routes_1.default);
appRouter.use(`${baseRoute}/notifications`, notification_routes_1.default);
appRouter.use(`${baseRoute}/orders`, order_routes_1.default);
// Payment management routes
appRouter.use(`${baseRoute}/payments`, payment_routes_1.default);
// Inventory management routes
appRouter.use(`${baseRoute}/spare-parts`, spare_part_routes_1.default);
appRouter.use(`${baseRoute}/packages`, package_routes_1.default);
appRouter.use(`${baseRoute}/cylinders`, cylinder_routes_1.default);
// Dashboard routes
appRouter.use(`${baseRoute}/dashboard`, dashboard_routes_1.default);
// SMS and Notification Management routes
appRouter.use(`${baseRoute}/sms-management`, sms_management_routes_1.smsManagementRouter);
appRouter.use(`${baseRoute}/notification-management`, notification_management_routes_1.notificationManagementRouter);
exports.default = appRouter;
//# sourceMappingURL=index.js.map