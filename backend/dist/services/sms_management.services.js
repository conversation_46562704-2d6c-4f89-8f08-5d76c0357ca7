"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.smsManagementService = void 0;
const app_errors_1 = require("../errors/app_errors");
const models_1 = require("../models");
const enums_1 = require("../enums/enums");
const mongoose_1 = require("mongoose");
const sms_services_1 = require("./sms.services");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * SMS Management Service
 * Handles SMS operations with database tracking and management features
 */
class SmsManagementService {
    /**
     * Send SMS to a single recipient with database tracking
     */
    async sendSms(senderId, phoneNumber, message, options) {
        try {
            // Validate sender
            const sender = await models_1.User.findById(senderId);
            if (!sender)
                throw new app_errors_1.NotFoundError('Sender not found');
            // Create SMS record
            const smsRecord = await models_1.Sms.create({
                senderId: new mongoose_1.Types.ObjectId(senderId),
                recipients: [
                    {
                        phoneNumber,
                        status: enums_1.SmsStatus.PENDING,
                    },
                ],
                message,
                type: options?.type || enums_1.SmsType.GENERAL,
                isOtp: options?.isOtp || false,
                senderIdText: options?.senderIdText,
                refId: options?.refId,
                totalRecipients: 1,
                status: enums_1.SmsStatus.PENDING,
                scheduledAt: options?.scheduledAt,
            });
            // If scheduled for later, return early
            if (options?.scheduledAt && options.scheduledAt > new Date()) {
                logger_1.default.info(`SMS scheduled for ${options.scheduledAt}`, {
                    smsId: smsRecord._id,
                    phoneNumber,
                });
                return { smsId: smsRecord._id.toString() };
            }
            try {
                // Send SMS using existing service
                const result = await sms_services_1.smsService.sendSms(phoneNumber, message, {
                    isOtp: options?.isOtp,
                    senderId: options?.senderIdText,
                    refId: options?.refId,
                });
                // Update SMS record with success
                await models_1.Sms.findByIdAndUpdate(smsRecord._id, {
                    $set: {
                        'recipients.0.status': enums_1.SmsStatus.SENT,
                        'recipients.0.messageId': result.messageId,
                        status: enums_1.SmsStatus.SENT,
                        sentAt: new Date(),
                        successCount: 1,
                    },
                });
                logger_1.default.info(`SMS sent successfully`, {
                    smsId: smsRecord._id,
                    messageId: result.messageId,
                    phoneNumber,
                });
                return {
                    smsId: smsRecord._id.toString(),
                    messageId: result.messageId,
                };
            }
            catch (error) {
                // Update SMS record with failure
                await models_1.Sms.findByIdAndUpdate(smsRecord._id, {
                    $set: {
                        'recipients.0.status': enums_1.SmsStatus.FAILED,
                        'recipients.0.error': error.message,
                        status: enums_1.SmsStatus.FAILED,
                        failureCount: 1,
                    },
                });
                logger_1.default.error(`SMS sending failed`, {
                    smsId: smsRecord._id,
                    error: error.message,
                    phoneNumber,
                });
                throw error;
            }
        }
        catch (error) {
            logger_1.default.error('SmsManagementService.sendSms error', {
                senderId,
                phoneNumber,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Send bulk SMS with database tracking
     */
    async sendBulkSms(senderId, phoneNumbers, message, options) {
        try {
            // Validate sender
            const sender = await models_1.User.findById(senderId);
            if (!sender)
                throw new app_errors_1.NotFoundError('Sender not found');
            // Remove duplicates
            const uniqueNumbers = [...new Set(phoneNumbers)];
            // Create SMS record
            const recipients = uniqueNumbers.map(phoneNumber => ({
                phoneNumber,
                status: enums_1.SmsStatus.PENDING,
            }));
            const smsRecord = await models_1.Sms.create({
                senderId: new mongoose_1.Types.ObjectId(senderId),
                recipients,
                message,
                type: options?.type || enums_1.SmsType.GENERAL,
                isOtp: options?.isOtp || false,
                senderIdText: options?.senderIdText,
                refId: options?.refId,
                totalRecipients: uniqueNumbers.length,
                status: enums_1.SmsStatus.PENDING,
                scheduledAt: options?.scheduledAt,
            });
            // If scheduled for later, return early
            if (options?.scheduledAt && options.scheduledAt > new Date()) {
                logger_1.default.info(`Bulk SMS scheduled for ${options.scheduledAt}`, {
                    smsId: smsRecord._id,
                    recipientCount: uniqueNumbers.length,
                });
                return { smsId: smsRecord._id.toString() };
            }
            try {
                // Send bulk SMS using existing service
                const results = await sms_services_1.smsService.sendSmsToMany(uniqueNumbers, message, {
                    isOtp: options?.isOtp,
                    senderId: options?.senderIdText,
                    refId: options?.refId,
                });
                // Update SMS record with results
                // const updateOperations = [];
                const updateOperations = [];
                let successCount = 0;
                let failureCount = 0;
                // Update successful sends
                results.data.messageIds.successes.forEach((success, index) => {
                    const recipientIndex = recipients.findIndex(r => r.phoneNumber === success.phoneNumber);
                    if (recipientIndex !== -1) {
                        updateOperations.push({
                            updateOne: {
                                filter: { _id: smsRecord._id },
                                update: {
                                    $set: {
                                        [`recipients.${recipientIndex}.status`]: enums_1.SmsStatus.SENT,
                                        [`recipients.${recipientIndex}.messageId`]: success.messageId,
                                    },
                                },
                            },
                        });
                        successCount++;
                    }
                });
                // Update failed sends
                results.data.messageIds.failures.forEach((failure, index) => {
                    const recipientIndex = recipients.findIndex(r => r.phoneNumber === failure.phoneNumber);
                    if (recipientIndex !== -1) {
                        updateOperations.push({
                            updateOne: {
                                filter: { _id: smsRecord._id },
                                update: {
                                    $set: {
                                        [`recipients.${recipientIndex}.status`]: enums_1.SmsStatus.FAILED,
                                        [`recipients.${recipientIndex}.error`]: failure.error.message,
                                    },
                                },
                            },
                        });
                        failureCount++;
                    }
                });
                // Execute all updates
                if (updateOperations.length > 0) {
                    await models_1.Sms.bulkWrite(updateOperations);
                }
                // Update overall SMS status
                const overallStatus = failureCount === 0
                    ? enums_1.SmsStatus.SENT
                    : successCount === 0
                        ? enums_1.SmsStatus.FAILED
                        : enums_1.SmsStatus.SENT;
                await models_1.Sms.findByIdAndUpdate(smsRecord._id, {
                    status: overallStatus,
                    sentAt: new Date(),
                    successCount,
                    failureCount,
                });
                logger_1.default.info(`Bulk SMS completed`, {
                    smsId: smsRecord._id,
                    successCount,
                    failureCount,
                    totalRecipients: uniqueNumbers.length,
                });
                return {
                    smsId: smsRecord._id.toString(),
                    results,
                };
            }
            catch (error) {
                // Update SMS record with failure
                await models_1.Sms.findByIdAndUpdate(smsRecord._id, {
                    status: enums_1.SmsStatus.FAILED,
                    failureCount: uniqueNumbers.length,
                });
                logger_1.default.error(`Bulk SMS sending failed`, {
                    smsId: smsRecord._id,
                    error: error.message,
                    recipientCount: uniqueNumbers.length,
                });
                throw error;
            }
        }
        catch (error) {
            logger_1.default.error('SmsManagementService.sendBulkSms error', {
                senderId,
                recipientCount: phoneNumbers.length,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get SMS history with pagination and filtering
     */
    async getSmsHistory(filters, pagination) {
        try {
            const page = pagination?.page || 1;
            const limit = Math.min(pagination?.limit || 20, 100); // Max 100 items per page
            const skip = (page - 1) * limit;
            const sortBy = pagination?.sortBy || 'createdAt';
            const sortOrder = pagination?.sortOrder === 'asc' ? 1 : -1;
            // Build query
            const query = {};
            if (filters?.senderId) {
                query.senderId = new mongoose_1.Types.ObjectId(filters.senderId);
            }
            if (filters?.status) {
                query.status = filters.status;
            }
            if (filters?.type) {
                query.type = filters.type;
            }
            if (filters?.startDate || filters?.endDate) {
                query.createdAt = {};
                if (filters.startDate)
                    query.createdAt.$gte = filters.startDate;
                if (filters.endDate)
                    query.createdAt.$lte = filters.endDate;
            }
            if (filters?.phoneNumber) {
                query['recipients.phoneNumber'] = { $regex: filters.phoneNumber, $options: 'i' };
            }
            // Execute query with pagination
            const [sms, totalItems] = await Promise.all([
                models_1.Sms.find(query)
                    .populate('senderId', 'phone email role')
                    .sort({ [sortBy]: sortOrder })
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                models_1.Sms.countDocuments(query),
            ]);
            const totalPages = Math.ceil(totalItems / limit);
            return {
                sms: sms,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalItems,
                    hasNext: page < totalPages,
                    hasPrev: page > 1,
                },
            };
        }
        catch (error) {
            logger_1.default.error('SmsManagementService.getSmsHistory error', {
                filters,
                pagination,
                error: error.message,
            });
            // throw new InternalServerError('Failed to fetch SMS history');
            throw error;
        }
    }
    /**
     * Get SMS statistics
     */
    async getSmsStats(filters) {
        try {
            const query = {};
            if (filters?.senderId) {
                query.senderId = new mongoose_1.Types.ObjectId(filters.senderId);
            }
            if (filters?.startDate || filters?.endDate) {
                query.createdAt = {};
                if (filters.startDate)
                    query.createdAt.$gte = filters.startDate;
                if (filters.endDate)
                    query.createdAt.$lte = filters.endDate;
            }
            // Get basic stats
            const [totalStats, typeStats, dailyStats] = await Promise.all([
                models_1.Sms.aggregate([
                    { $match: query },
                    {
                        $group: {
                            _id: null,
                            totalSms: { $sum: 1 },
                            totalRecipients: { $sum: '$totalRecipients' },
                            successfulSms: {
                                $sum: { $cond: [{ $eq: ['$status', enums_1.SmsStatus.SENT] }, 1, 0] },
                            },
                            failedSms: {
                                $sum: { $cond: [{ $eq: ['$status', enums_1.SmsStatus.FAILED] }, 1, 0] },
                            },
                        },
                    },
                ]),
                models_1.Sms.aggregate([{ $match: query }, { $group: { _id: '$type', count: { $sum: 1 } } }]),
                models_1.Sms.aggregate([
                    { $match: query },
                    {
                        $group: {
                            _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
                            sent: {
                                $sum: { $cond: [{ $eq: ['$status', enums_1.SmsStatus.SENT] }, 1, 0] },
                            },
                            failed: {
                                $sum: { $cond: [{ $eq: ['$status', enums_1.SmsStatus.FAILED] }, 1, 0] },
                            },
                        },
                    },
                    { $sort: { _id: 1 } },
                ]),
            ]);
            const stats = totalStats[0] || {
                totalSms: 0,
                totalRecipients: 0,
                successfulSms: 0,
                failedSms: 0,
            };
            const typeBreakdown = {};
            typeStats.forEach(stat => {
                typeBreakdown[stat._id] = stat.count;
            });
            const successRate = stats.totalSms > 0 ? (stats.successfulSms / stats.totalSms) * 100 : 0;
            return {
                ...stats,
                successRate: Math.round(successRate * 100) / 100,
                typeBreakdown,
                dailyStats: dailyStats.map(stat => ({
                    date: stat._id,
                    sent: stat.sent,
                    failed: stat.failed,
                })),
            };
        }
        catch (error) {
            logger_1.default.error('SmsManagementService.getSmsStats error', {
                filters,
                error: error.message,
            });
            // throw new InternalServerError('Failed to fetch SMS statistics');
            throw error;
        }
    }
    /**
     * Get SMS details by ID
     */
    async getSmsById(smsId) {
        try {
            const sms = await models_1.Sms.findById(smsId).populate('senderId', 'phone email role').lean();
            if (!sms)
                throw new app_errors_1.NotFoundError('SMS not found');
            return sms;
        }
        catch (error) {
            if (error instanceof app_errors_1.NotFoundError)
                throw error;
            logger_1.default.error('SmsManagementService.getSmsById error', {
                smsId,
                error: error.message,
            });
            // throw new InternalServerError('Failed to fetch SMS details');
            throw error;
        }
    }
    /**
     * Cancel scheduled SMS
     */
    async cancelScheduledSms(smsId, userId) {
        try {
            const sms = await models_1.Sms.findById(smsId);
            if (!sms)
                throw new app_errors_1.NotFoundError('SMS not found');
            // Check if user has permission to cancel
            const user = await models_1.User.findById(userId);
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            if (user.role !== enums_1.UserRole.ADMIN && sms.senderId.toString() !== userId) {
                throw new app_errors_1.BadRequestError('Not authorized to cancel this SMS');
            }
            if (sms.status !== enums_1.SmsStatus.PENDING) {
                throw new app_errors_1.BadRequestError('Can only cancel pending SMS');
            }
            if (!sms.scheduledAt || sms.scheduledAt <= new Date()) {
                throw new app_errors_1.BadRequestError('SMS is not scheduled or already being sent');
            }
            await models_1.Sms.findByIdAndUpdate(smsId, {
                status: enums_1.SmsStatus.CANCELLED,
            });
            logger_1.default.info(`SMS cancelled`, { smsId, userId });
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('SmsManagementService.cancelScheduledSms error', {
                smsId,
                userId,
                error: error.message,
            });
            throw error;
        }
    }
}
exports.smsManagementService = new SmsManagementService();
//# sourceMappingURL=sms_management.services.js.map