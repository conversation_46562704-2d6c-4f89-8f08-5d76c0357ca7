"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationService = void 0;
const app_errors_1 = require("../errors/app_errors");
const models_1 = require("../models");
const notificationUtil = __importStar(require("../utils/notification_utils"));
const notification_utils_1 = require("../utils/notification_utils");
const logger_1 = __importDefault(require("../config/logger"));
class NotificationService {
    /**
     * Send notification to a specific user
     */
    async sendToUser(userId, payload) {
        try {
            // Validate input
            if (!userId)
                throw new app_errors_1.BadRequestError('User ID is required');
            if (!payload?.title || !payload?.body) {
                throw new app_errors_1.BadRequestError('Title and body are required');
            }
            const user = await models_1.User.findById(userId).select('notification');
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            if (!user.notification) {
                throw new app_errors_1.NotFoundError('Notification settings not configured');
            }
            if (!user.notification.isEnabled) {
                throw new app_errors_1.BadRequestError('Notifications are disabled for this user');
            }
            if (!user.notification.fcmToken) {
                throw new app_errors_1.BadRequestError('FCM token not registered for this user');
            }
            // Create notification record
            const notificationRecord = await models_1.Notification.create({
                userId,
                title: payload.title,
                body: payload.body,
                data: payload.data,
                imageUrl: payload.imageUrl,
                status: 'pending',
            });
            try {
                // Send the actual push notification
                const result = await notificationUtil.sendPushNotification(user.notification.fcmToken, payload);
                // Update notification status
                await models_1.Notification.findByIdAndUpdate(notificationRecord._id, {
                    status: 'delivered',
                    deliveredAt: new Date(),
                    result,
                });
                logger_1.default.info(`Notification sent to user ${userId}`, {
                    notificationId: notificationRecord._id,
                    title: payload.title,
                });
                return {
                    success: true,
                    //   notificationId: notificationRecord._id,
                    details: result,
                };
            }
            catch (error) {
                await models_1.Notification.findByIdAndUpdate(notificationRecord._id, {
                    status: 'failed',
                    error: error.message,
                });
                logger_1.default.error(`Failed to send notification to user ${userId}`, {
                    error: error.message,
                    notificationId: notificationRecord._id,
                });
                throw new app_errors_1.InternalServerError('Failed to send notification');
            }
        }
        catch (error) {
            logger_1.default.error('NotificationService.sendToUser error', {
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Send notification to all users subscribed to a topic
     */
    async sendToTopic(topic, payload, options) {
        try {
            // Validate input
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            if (!payload?.title || !payload?.body) {
                throw new app_errors_1.BadRequestError('Title and body are required');
            }
            // Get all users subscribed to this topic
            const query = {
                'notification.topics': topic,
                'notification.isEnabled': true,
            };
            if (options?.onlyActiveUsers) {
                query.isActive = true;
            }
            const users = await models_1.User.find(query).select('notification isActive').lean();
            if (!users.length) {
                logger_1.default.warn(`No users found subscribed to topic ${topic}`);
                return {
                    success: true,
                    message: 'No subscribed users found',
                    count: 0,
                };
            }
            // Filter users with valid FCM tokens
            const validUsers = users.filter(u => u.notification?.fcmToken);
            const tokens = validUsers.map(u => u.notification.fcmToken);
            if (!tokens.length) {
                logger_1.default.warn(`No valid FCM tokens found for topic ${topic}`);
                return {
                    success: true,
                    message: 'No valid FCM tokens found',
                    count: 0,
                };
            }
            // Create bulk notification records
            const notificationRecords = await models_1.Notification.insertMany(validUsers.map(user => ({
                userId: user._id,
                title: payload.title,
                body: payload.body,
                data: payload.data,
                imageUrl: payload.imageUrl,
                topic,
                status: 'pending',
            })));
            try {
                // Send to topic
                const result = await notificationUtil.sendToTopic(topic, payload);
                // Update all notification records
                await models_1.Notification.updateMany({ _id: { $in: notificationRecords.map(n => n._id) } }, {
                    status: 'delivered',
                    deliveredAt: new Date(),
                    result,
                });
                logger_1.default.info(`Notification sent to topic ${topic}`, {
                    count: validUsers.length,
                    title: payload.title,
                });
                return {
                    success: true,
                    count: validUsers.length,
                    details: result,
                };
            }
            catch (error) {
                await models_1.Notification.updateMany({ _id: { $in: notificationRecords.map(n => n._id) } }, {
                    status: 'failed',
                    error: error.message,
                });
                logger_1.default.error(`Failed to send notification to topic ${topic}`, {
                    error: error.message,
                    count: validUsers.length,
                });
                throw new app_errors_1.InternalServerError('Failed to send topic notification');
            }
        }
        catch (error) {
            logger_1.default.error('NotificationService.sendToTopic error', {
                topic,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Update user's FCM token
     */
    async updateFcmToken(userId, token) {
        try {
            if (!userId || !token) {
                throw new app_errors_1.BadRequestError('User ID and token are required');
            }
            const user = await models_1.User.findByIdAndUpdate(userId, {
                $set: {
                    'notification.fcmToken': token,
                    'notification.isEnabled': true,
                },
            }, { new: true });
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            logger_1.default.info(`Updated FCM token for user ${userId}`);
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('Failed to update FCM token', {
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Subscribe user to notification topic
     */
    async subscribeToTopic(userId, topic) {
        try {
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            const user = await models_1.User.findById(userId).select('notification');
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            if (!user.notification) {
                throw new app_errors_1.BadRequestError('Notification settings not initialized');
            }
            // Check if already subscribed
            if (user.notification.topics?.includes(topic)) {
                return { success: true };
            }
            // Update in database
            await models_1.User.findByIdAndUpdate(userId, { $addToSet: { 'notification.topics': topic } });
            // Subscribe with FCM if token exists
            if (user.notification.fcmToken) {
                try {
                    await notificationUtil.subscribeToTopic([user.notification.fcmToken], topic);
                }
                catch (error) {
                    logger_1.default.error('FCM topic subscription failed', {
                        userId,
                        topic,
                        error: error.message,
                    });
                    // Continue even if FCM fails - we'll sync later
                }
            }
            logger_1.default.info(`User ${userId} subscribed to topic ${topic}`);
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('Failed to subscribe to topic', {
                userId,
                topic,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Toggle notification enable/disable
     */
    async toggleNotifications(userId, enabled) {
        try {
            const user = await models_1.User.findByIdAndUpdate(userId, { $set: { 'notification.isEnabled': enabled } }, { new: true });
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            logger_1.default.info(`Notifications ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('Failed to toggle notifications', {
                userId,
                enabled,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get notification history with pagination and filtering
     */
    async getNotificationHistory(filters, pagination) {
        try {
            const page = pagination?.page || 1;
            const limit = Math.min(pagination?.limit || 20, 100);
            const skip = (page - 1) * limit;
            const sortBy = pagination?.sortBy || 'createdAt';
            const sortOrder = pagination?.sortOrder === 'asc' ? 1 : -1;
            // Build query
            const query = {};
            if (filters?.userId) {
                query.userId = filters.userId;
            }
            if (filters?.status) {
                query.status = filters.status;
            }
            if (filters?.topic) {
                query.topic = filters.topic;
            }
            if (filters?.startDate || filters?.endDate) {
                query.createdAt = {};
                if (filters.startDate)
                    query.createdAt.$gte = filters.startDate;
                if (filters.endDate)
                    query.createdAt.$lte = filters.endDate;
            }
            // Execute query with pagination
            const [notifications, totalItems] = await Promise.all([
                models_1.Notification.find(query)
                    .populate('userId', 'phone email role')
                    .sort({ [sortBy]: sortOrder })
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                models_1.Notification.countDocuments(query),
            ]);
            const totalPages = Math.ceil(totalItems / limit);
            return {
                notifications,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalItems,
                    hasNext: page < totalPages,
                    hasPrev: page > 1,
                },
            };
        }
        catch (error) {
            logger_1.default.error('NotificationService.getNotificationHistory error', {
                filters,
                pagination,
                error: error.message,
            });
            throw new app_errors_1.InternalServerError('Failed to fetch notification history');
        }
    }
    /**
     * Get notification statistics
     */
    async getNotificationStats(filters) {
        try {
            const query = {};
            if (filters?.startDate || filters?.endDate) {
                query.createdAt = {};
                if (filters.startDate)
                    query.createdAt.$gte = filters.startDate;
                if (filters.endDate)
                    query.createdAt.$lte = filters.endDate;
            }
            // Get basic stats
            const [totalStats, topicStats, dailyStats] = await Promise.all([
                models_1.Notification.aggregate([
                    { $match: query },
                    {
                        $group: {
                            _id: null,
                            totalNotifications: { $sum: 1 },
                            deliveredNotifications: {
                                $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] },
                            },
                            failedNotifications: {
                                $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] },
                            },
                        },
                    },
                ]),
                models_1.Notification.aggregate([
                    { $match: { ...query, topic: { $exists: true } } },
                    { $group: { _id: '$topic', count: { $sum: 1 } } },
                    { $sort: { count: -1 } },
                ]),
                models_1.Notification.aggregate([
                    { $match: query },
                    {
                        $group: {
                            _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
                            sent: { $sum: 1 },
                            delivered: {
                                $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] },
                            },
                            failed: {
                                $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] },
                            },
                        },
                    },
                    { $sort: { _id: 1 } },
                ]),
            ]);
            const stats = totalStats[0] || {
                totalNotifications: 0,
                deliveredNotifications: 0,
                failedNotifications: 0,
            };
            const deliveryRate = stats.totalNotifications > 0
                ? (stats.deliveredNotifications / stats.totalNotifications) * 100
                : 0;
            return {
                ...stats,
                deliveryRate: Math.round(deliveryRate * 100) / 100,
                topicBreakdown: topicStats.map(stat => ({
                    topic: stat._id,
                    count: stat.count,
                })),
                dailyStats: dailyStats.map(stat => ({
                    date: stat._id,
                    sent: stat.sent,
                    delivered: stat.delivered,
                    failed: stat.failed,
                })),
            };
        }
        catch (error) {
            logger_1.default.error('NotificationService.getNotificationStats error', {
                filters,
                error: error.message,
            });
            throw new app_errors_1.InternalServerError('Failed to fetch notification statistics');
        }
    }
    /**
     * Mark notification as read
     */
    async markAsRead(notificationId, userId) {
        try {
            const notification = await models_1.Notification.findOneAndUpdate({ _id: notificationId, userId }, { readAt: new Date() }, { new: true });
            if (!notification)
                throw new app_errors_1.NotFoundError('Notification not found');
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('NotificationService.markAsRead error', {
                notificationId,
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get user's notifications
     */
    async getUserNotifications(userId, options) {
        try {
            const query = { userId };
            if (options?.unreadOnly) {
                query.readAt = { $exists: false };
            }
            const limit = options?.limit || 20;
            const page = options?.page || 1;
            const skip = (page - 1) * limit;
            const [notifications, unreadCount, totalItems] = await Promise.all([
                models_1.Notification.find(query).sort({ createdAt: -1 }).skip(skip).limit(limit).lean(),
                models_1.Notification.countDocuments({ userId, readAt: { $exists: false } }),
                options?.page ? models_1.Notification.countDocuments(query) : Promise.resolve(0),
            ]);
            const result = {
                notifications,
                unreadCount,
            };
            if (options?.page) {
                result.pagination = {
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit),
                    totalItems,
                };
            }
            return result;
        }
        catch (error) {
            logger_1.default.error('NotificationService.getUserNotifications error', {
                userId,
                options,
                error: error.message,
            });
            throw new app_errors_1.InternalServerError('Failed to fetch user notifications');
        }
    }
}
exports.notificationService = new NotificationService();
//# sourceMappingURL=notification.services.js.map