"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cylinderService = void 0;
const models_1 = require("../models");
const app_errors_1 = require("../errors/app_errors");
const mongoose_1 = __importDefault(require("mongoose"));
const enums_1 = require("../enums/enums");
class CylinderService {
    /**
     * Reserve cylinders for an order
     * @throws {BadRequestError} If quantity is invalid or insufficient stock
     * @throws {NotFoundError} If cylinder not found
     */
    async reserveCylinder(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const cylinder = await models_1.Cylinder.findById(cylinderId).session(session || null);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        if (cylinder.status !== enums_1.CylinderStatus.Active) {
            throw new app_errors_1.BadRequestError(`Cannot reserve cylinder with status ${cylinder.status}`, {
                code: 'INVALID_CYLINDER_STATUS',
            });
        }
        if (cylinder.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient cylinder stock', {
                code: 'INSUFFICIENT_CYLINDER_STOCK',
                details: {
                    available: cylinder.availableQuantity,
                    requested: quantity,
                },
            });
        }
        const update = {
            $inc: { reserved: quantity },
        };
        // Update status if this reservation brings us to out of stock
        if (cylinder.quantity - (cylinder.reserved + quantity) <= 0) {
            update.$set = { status: enums_1.CylinderStatus.OutOfStock };
        }
        const result = await models_1.Cylinder.updateOne({
            _id: cylinderId,
            $expr: { $gte: [{ $subtract: ['$quantity', '$reserved'] }, quantity] },
        }, update, { session });
        if (result.modifiedCount === 0) {
            throw new app_errors_1.DuplicateResourceError('Cylinder stock changed during reservation', {
                code: 'CONCURRENT_MODIFICATION',
            });
        }
        return {
            modifiedCount: result.modifiedCount,
            newStatus: update.$set?.status,
        };
    }
    /**
     * Release reserved cylinders (when order is cancelled)
     * @throws {BadRequestError} If quantity is invalid or exceeds reserved amount
     * @throws {NotFoundError} If cylinder not found
     */
    async releaseReservation(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const cylinder = await models_1.Cylinder.findById(cylinderId).session(session || null);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        if (cylinder.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot release more than reserved quantity', {
                code: 'INVALID_RESERVATION_RELEASE',
                details: {
                    reserved: cylinder.reserved,
                    toRelease: quantity,
                },
            });
        }
        const update = {
            $inc: { reserved: -quantity },
        };
        // Update status if we're coming back from out of stock
        if (cylinder.status === enums_1.CylinderStatus.OutOfStock &&
            cylinder.quantity - (cylinder.reserved - quantity) > 0) {
            update.$set = { status: enums_1.CylinderStatus.Active };
        }
        const result = await models_1.Cylinder.updateOne({ _id: cylinderId, reserved: { $gte: quantity } }, update, { session });
        if (result.modifiedCount === 0) {
            throw new app_errors_1.DuplicateResourceError('Cylinder stock changed during release', {
                code: 'CONCURRENT_MODIFICATION',
            });
        }
        return {
            modifiedCount: result.modifiedCount,
            newStatus: update.$set?.status,
        };
    }
    /**
     * Mark reserved cylinders as sold (when order is completed)
     * @throws {BadRequestError} If quantity is invalid
     * @throws {NotFoundError} If cylinder not found
     */
    async markAsSold(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const cylinder = await models_1.Cylinder.findByIdAndUpdate(cylinderId, {
            $inc: {
                reserved: -quantity,
                sold: quantity,
                quantity: -quantity, // Physical count decreases when sold
            },
        }, { new: true, session });
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        return cylinder;
    }
    /**
     * Restock cylinders and update status if needed
     * @throws {BadRequestError} If quantity is invalid
     * @throws {NotFoundError} If cylinder not found
     */
    async restock(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const update = {
            $inc: { quantity },
            lastRestockedAt: new Date(),
        };
        // Update status if coming from out of stock
        update.$set = {
            status: enums_1.CylinderStatus.Active,
        };
        const cylinder = await models_1.Cylinder.findByIdAndUpdate(cylinderId, update, { new: true, session });
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        return cylinder;
    }
    /**
     * Create a new cylinder type
     * @throws {DuplicateResourceError} If cylinder type already exists
     */
    async createCylinder(cylinderData) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Check if cylinder type already exists
            const existing = await models_1.Cylinder.findOne({
                type: cylinderData.type,
                material: cylinderData.material,
            }).session(session);
            if (existing) {
                throw new app_errors_1.DuplicateResourceError('Cylinder type already exists', {
                    code: 'CYLINDER_EXISTS',
                });
            }
            const cylinder = new models_1.Cylinder({
                quantity: 0,
                reserved: 0,
                sold: 0,
                minimumStockLevel: 10,
                status: enums_1.CylinderStatus.Active,
                ...cylinderData,
            });
            await cylinder.save({ session });
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Update cylinder details (excluding inventory-related fields)
     * @throws {NotFoundError} If cylinder not found
     */
    async updateCylinder(cylinderId, updateData) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Prevent updating inventory-related fields through this method
            const { quantity, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;
            const cylinder = await models_1.Cylinder.findByIdAndUpdate(cylinderId, safeUpdateData, {
                new: true,
                runValidators: true,
                session,
            });
            if (!cylinder) {
                throw new app_errors_1.NotFoundError('Cylinder not found', {
                    code: 'CYLINDER_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Delete a cylinder type (only if no active reservations)
     * @throws {NotFoundError} If cylinder not found
     * @throws {DuplicateResourceError} If cylinder has active reservations
     */
    async deleteCylinder(cylinderId) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const cylinder = await models_1.Cylinder.findOneAndDelete({
                _id: cylinderId,
                reserved: 0,
            }).session(session);
            if (!cylinder) {
                // Check if it exists but has reservations
                const existing = await models_1.Cylinder.findById(cylinderId).session(session);
                if (existing) {
                    throw new app_errors_1.DuplicateResourceError('Cannot delete cylinder with active reservations', {
                        code: 'ACTIVE_RESERVATIONS',
                        details: {
                            reserved: existing.reserved,
                        },
                    });
                }
                throw new app_errors_1.NotFoundError('Cylinder not found', {
                    code: 'CYLINDER_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Get cylinder by ID
     * @throws {NotFoundError} If cylinder not found
     */
    async getCylinderById(cylinderId) {
        const cylinder = await models_1.Cylinder.findById(cylinderId);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        return cylinder;
    }
    /**
     * List all cylinders with optional filtering and pagination
     */
    async listCylinders(filter = {}, pagination = { page: 1, limit: 10 }, requestedUser) {
        const query = {};
        if (filter.type)
            query.type = filter.type;
        if (filter.material)
            query.material = filter.material;
        // if (filter.status) query.status = filter.status;
        if (filter.status) {
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.CylinderStatus.Active;
            }
            else {
                query.status = filter.status;
            }
        }
        if (filter.lowStockOnly) {
            query.$expr = { $lte: ['$quantity', '$minimumStockLevel'] };
        }
        const [data, total] = await Promise.all([
            models_1.Cylinder.find(query)
                .sort({ type: 1, material: 1 })
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            models_1.Cylinder.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Check cylinder availability
     * @throws {NotFoundError} If cylinder not found
     */
    async checkAvailability(cylinderId, quantity) {
        const cylinder = await models_1.Cylinder.findById(cylinderId);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        return {
            available: cylinder.status === enums_1.CylinderStatus.Active && cylinder.availableQuantity >= quantity,
            availableQuantity: cylinder.availableQuantity,
            status: cylinder.status,
        };
    }
    /**
     * Get low stock alerts (quantity <= minimumStockLevel)
     */
    async getLowStockAlerts() {
        return models_1.Cylinder.find({
            $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
            status: enums_1.CylinderStatus.Active, // Only active cylinders
        }).sort({ quantity: 1 }); // Sort by most critical first
    }
    /**
     * Get sales statistics aggregated by type and material
     */
    async getSalesStatistics() {
        const result = {
            totalSold: 0,
            byType: {},
            byMaterial: {},
            byStatus: {},
        };
        // Initialize all possible values with 0
        Object.values(enums_1.CylinderType).forEach(type => (result.byType[type] = 0));
        Object.values(enums_1.CylinderMaterial).forEach(material => (result.byMaterial[material] = 0));
        Object.values(enums_1.CylinderStatus).forEach(status => (result.byStatus[status] = 0));
        const cylinders = await models_1.Cylinder.find();
        cylinders.forEach(cylinder => {
            result.totalSold += cylinder.sold;
            result.byType[cylinder.type] += cylinder.sold;
            result.byMaterial[cylinder.material] += cylinder.sold;
            result.byStatus[cylinder.status]++;
        });
        return result;
    }
    /**
     * Bulk update cylinder statuses
     * @throws {BadRequestError} If invalid status transition
     */
    async bulkUpdateStatus(cylinderIds, newStatus, session) {
        // Validate status transitions
        if (newStatus === enums_1.CylinderStatus.Active) {
            // Can only activate cylinders that are out of stock
            const result = await models_1.Cylinder.updateMany({
                _id: { $in: cylinderIds },
                status: { $in: [enums_1.CylinderStatus.OutOfStock] },
            }, { $set: { status: newStatus } }, { session });
            return result.modifiedCount;
        }
        else {
            // For other statuses (Discontinued)
            const result = await models_1.Cylinder.updateMany({ _id: { $in: cylinderIds } }, { $set: { status: newStatus } }, { session });
            return result.modifiedCount;
        }
    }
}
exports.cylinderService = new CylinderService();
//# sourceMappingURL=cylinder.services.js.map