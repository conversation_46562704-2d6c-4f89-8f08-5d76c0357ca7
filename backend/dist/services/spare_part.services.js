"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sparePartService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const spareParts_model_1 = require("../models/spareParts.model");
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
class SparePartService {
    /**
     * Create a new spare part
     * @throws {DuplicateResourceError} If spare part with same name/barcode exists
     */
    async createSparePart(data, session) {
        const sessionToUse = session || (await mongoose_1.default.startSession());
        const shouldCommit = !session; // Only commit if we started the session
        try {
            if (!session)
                sessionToUse.startTransaction();
            // Check for duplicates
            const existingConditions = [];
            if (data.name)
                existingConditions.push({ name: data.name });
            if (data.barcode)
                existingConditions.push({ barcode: data.barcode });
            if (existingConditions.length > 0) {
                const existing = await spareParts_model_1.SparePart.findOne({
                    $or: existingConditions,
                }).session(sessionToUse);
                if (existing) {
                    throw new app_errors_1.DuplicateResourceError(existing.name === data.name
                        ? 'Spare part with this name already exists'
                        : 'Spare part with this barcode already exists', { code: 'DUPLICATE_SPARE_PART' });
                }
            }
            const sparePart = new spareParts_model_1.SparePart({
                stock: 0,
                reserved: 0,
                sold: 0,
                status: enums_1.SparePartStatus.AVAILABLE,
                minimumStockLevel: 5,
                ...data,
            });
            await sparePart.save({ session: sessionToUse });
            if (shouldCommit) {
                await sessionToUse.commitTransaction();
            }
            return sparePart;
        }
        catch (error) {
            if (shouldCommit) {
                await sessionToUse.abortTransaction();
            }
            throw error;
        }
        finally {
            if (shouldCommit) {
                sessionToUse.endSession();
            }
        }
    }
    /**
     * Update spare part details
     * @throws {NotFoundError} If spare part not found
     */
    async updateSparePart(id, updateData, session) {
        // Prevent updating inventory fields through this method
        const { stock, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;
        const sparePart = await spareParts_model_1.SparePart.findByIdAndUpdate(id, safeUpdateData, {
            new: true,
            runValidators: true,
            session,
        });
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * Get spare part by ID
     * @throws {NotFoundError} If spare part not found
     */
    async getSparePartById(id) {
        const sparePart = await spareParts_model_1.SparePart.findById(id);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * List spare parts with filtering and pagination
     */
    async listSpareParts(filters = {}, pagination = { page: 1, limit: 10 }, requestedUser) {
        const query = {};
        // Text search
        if (filters.search) {
            query.$text = { $search: filters.search };
        }
        // Category filter
        if (filters.category) {
            query.category = filters.category;
        }
        // Status filter
        if (filters.status) {
            console.log('Status filter: ', {
                requestedUser,
                filters,
            });
            // query.status = filters.status;
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.SparePartStatus.AVAILABLE;
            }
            else {
                query.status = filters.status;
            }
        }
        else {
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.SparePartStatus.AVAILABLE;
            }
        }
        // Compatibility filter
        if (filters.compatibleWith) {
            query.compatibleCylinderTypes = filters.compatibleWith;
        }
        // Low stock filter
        if (filters.lowStock) {
            query.$expr = { $lte: ['$stock', '$minimumStockLevel'] };
        }
        const [data, total] = await Promise.all([
            spareParts_model_1.SparePart.find(query)
                .sort({ name: 1 })
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            spareParts_model_1.SparePart.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Restock spare parts
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If quantity is invalid
     */
    async restock(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findByIdAndUpdate(id, {
            $inc: { stock: quantity },
            $set: { lastRestockedAt: new Date() },
        }, { new: true, session });
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * Reserve spare parts for an order
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If insufficient stock
     */
    async reserve(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient stock', {
                code: 'INSUFFICIENT_STOCK',
                details: {
                    available: sparePart.availableQuantity,
                    requested: quantity,
                },
            });
        }
        const updated = await spareParts_model_1.SparePart.findByIdAndUpdate(id, { $inc: { reserved: quantity } }, { new: true, session });
        return updated;
    }
    /**
     * Release reserved spare parts
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If trying to release more than reserved
     */
    async release(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot release more than reserved quantity', {
                code: 'INVALID_RELEASE',
                details: {
                    reserved: sparePart.reserved,
                    toRelease: quantity,
                },
            });
        }
        const updated = await spareParts_model_1.SparePart.findByIdAndUpdate(id, { $inc: { reserved: -quantity } }, { new: true, session });
        return updated;
    }
    /**
     * Mark spare parts as sold
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If insufficient stock
     */
    async markAsSold(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient stock', {
                code: 'INSUFFICIENT_STOCK',
                details: {
                    available: sparePart.availableQuantity,
                    requested: quantity,
                },
            });
        }
        if (sparePart.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot sell more than reserved quantity', {
                code: 'INVALID_SALE_QUANTITY',
            });
        }
        const updated = await spareParts_model_1.SparePart.findByIdAndUpdate(id, {
            $inc: {
                stock: -quantity,
                reserved: -quantity,
                sold: quantity,
            },
        }, { new: true, session });
        return updated;
    }
    /**
     * Get low stock alerts
     */
    async getLowStockAlerts() {
        return spareParts_model_1.SparePart.find({
            $expr: { $lte: ['$stock', '$minimumStockLevel'] },
            status: { $nin: [enums_1.SparePartStatus.DISCONTINUED, enums_1.SparePartStatus.OUT_OF_STOCK] },
        }).sort({ stock: 1 });
    }
    /**
     * Discontinue a spare part
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If spare part has active reservations
     */
    async discontinue(id, session) {
        const sparePart = await spareParts_model_1.SparePart.findOneAndUpdate({
            _id: id,
            reserved: 0, // Can only discontinue if no reservations
        }, { status: enums_1.SparePartStatus.DISCONTINUED }, { new: true, session });
        if (!sparePart) {
            // Check if it exists but has reservations
            const existing = await spareParts_model_1.SparePart.findById(id).session(session || null);
            if (existing) {
                throw new app_errors_1.BadRequestError('Cannot discontinue spare part with active reservations', {
                    code: 'ACTIVE_RESERVATIONS',
                    details: {
                        reserved: existing.reserved,
                    },
                });
            }
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * Get sales statistics
     */
    async getSalesStatistics() {
        const result = {
            totalSold: 0,
            byCategory: {},
            byStatus: {},
        };
        // Initialize all possible values with 0
        Object.values(enums_1.SparePartCategory).forEach(cat => {
            result.byCategory[cat] = 0;
        });
        Object.values(enums_1.SparePartStatus).forEach(status => {
            result.byStatus[status] = 0;
        });
        const spareParts = await spareParts_model_1.SparePart.find();
        spareParts.forEach(sp => {
            result.totalSold += sp.sold;
            result.byCategory[sp.category] += sp.sold;
            result.byStatus[sp.status]++;
        });
        return result;
    }
    async searchSpareParts(query, limit = 10) {
        try {
            return spareParts_model_1.SparePart.find({ $text: { $search: query } }, { score: { $meta: 'textScore' } })
                .sort({ score: { $meta: 'textScore' } })
                .limit(limit);
        }
        catch (error) {
            logger_1.default.error('Failed to search spare parts', {
                query,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            throw error;
        }
    }
}
exports.sparePartService = new SparePartService();
//# sourceMappingURL=spare_part.services.js.map