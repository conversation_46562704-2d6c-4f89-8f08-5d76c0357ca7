"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.orderService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const models_1 = require("../models");
const enums_1 = require("../enums/enums");
const logger_1 = __importDefault(require("../config/logger"));
const app_errors_1 = require("../errors/app_errors");
const order_model_1 = require("../models/order.model");
const jwt_utils_1 = require("../utils/jwt_utils");
const index_1 = require("./index");
class OrderService {
    constructor() { }
    async createOrder(customerId, items, deliveryAddress, paymentMethod = enums_1.PaymentMethod.WAAFI_PREAUTH) {
        return this.executeWithRetry(async () => {
            return this.createOrderTransaction(customerId, items, deliveryAddress, paymentMethod);
        });
    }
    /**
     * Execute MongoDB transaction with retry logic for transient errors
     * @param operation - The transaction operation to execute
     * @param maxRetries - Maximum number of retry attempts
     * @returns Promise<T> - Result of the operation
     */
    async executeWithRetry(operation, maxRetries = 3) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                // Check if error is retryable
                const isRetryable = this.isRetryableError(error);
                logger_1.default.warn('Transaction attempt failed', {
                    attempt,
                    maxRetries,
                    isRetryable,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                });
                if (!isRetryable || attempt === maxRetries) {
                    throw error;
                }
                // Exponential backoff with jitter
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000) + Math.random() * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        throw lastError;
    }
    /**
     * Check if an error is retryable (transient MongoDB errors)
     * @param error - The error to check
     * @returns boolean - Whether the error is retryable
     */
    isRetryableError(error) {
        if (!error)
            return false;
        // Payment errors should NEVER be retried
        if (error instanceof app_errors_1.PaymentError ||
            error.code === 'PAYMENT_FAILED' ||
            error.code === 'WAAFI_PAYMENT_FAILED' ||
            error.message?.includes('Payment Failed') ||
            error.message?.includes('payment') ||
            error.message?.includes('balance')) {
            return false;
        }
        // Business logic errors should not be retried
        const nonRetryableErrors = [
            'CUSTOMER_NOT_FOUND',
            'ORDER_EXISTS',
            'INVALID_ITEM_FORMAT',
            'INVALID_ITEM_TYPE',
            'ITEM_NOT_FOUND',
            'INSUFFICIENT_STOCK',
        ];
        if (error.code && nonRetryableErrors.includes(error.code)) {
            return false;
        }
        // MongoDB transient transaction errors
        const retryableErrorCodes = [
            112, // WriteConflict
            11000, // DuplicateKey (in some cases)
            16500, // TransientTransactionError
            251, // NoSuchTransaction
        ];
        // Check error code
        if (error.code && retryableErrorCodes.includes(error.code)) {
            return true;
        }
        // Check error labels
        if (error.errorLabels && Array.isArray(error.errorLabels)) {
            return error.errorLabels.includes('TransientTransactionError');
        }
        // Check for application-level concurrent modification errors
        if (error.code === 'CONCURRENT_MODIFICATION') {
            return true;
        }
        // Check error message for known transient patterns
        const retryableMessages = [
            'WriteConflict',
            'TransientTransactionError',
            'NoSuchTransaction',
            'Transaction has been aborted',
            'Cylinder stock changed during reservation',
            'Package stock changed during reservation',
            'stock changed during',
        ];
        const errorMessage = error.message || '';
        return retryableMessages.some(msg => errorMessage.includes(msg));
    }
    async createOrderTransaction(customerId, items, deliveryAddress, paymentMethod = enums_1.PaymentMethod.WAAFI_PREAUTH) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Validate input
            if (!customerId) {
                throw new app_errors_1.BadRequestError('Customer ID is required');
            }
            if (!items || !items.length) {
                throw new app_errors_1.BadRequestError('Items are required');
            }
            if (!deliveryAddress) {
                throw new app_errors_1.BadRequestError('Delivery address is required');
            }
            if (!paymentMethod) {
                throw new app_errors_1.BadRequestError('Payment method is required');
            }
            // CRITICAL FIX: Validate customer INSIDE transaction with session
            const customer = await models_1.User.findById(customerId).session(session);
            if (!customer) {
                throw new app_errors_1.NotFoundError('Customer not found', {
                    code: 'CUSTOMER_NOT_FOUND',
                    details: {
                        customerId,
                    },
                });
            }
            const existingOrder = await models_1.Order.findOne({
                customer: customerId,
                items: { $eq: items },
                status: { $in: [enums_1.OrderStatus.PENDING, enums_1.OrderStatus.CONFIRMED] },
            }).session(session);
            if (existingOrder) {
                throw new app_errors_1.BadRequestError('Order already exists', {
                    code: 'ORDER_EXISTS',
                    details: {
                        orderId: existingOrder._id.toString(),
                    },
                });
            }
            // Validate items and check availability
            const validatedItems = await this.validateItems(items, { session });
            // Check inventory availability for all items
            await this.checkInventoryAvailability(validatedItems, session);
            // Calculate total amount
            const totalAmount = validatedItems.reduce((acc, item) => {
                return acc + item.price * item.quantity;
            }, 0);
            logger_1.default.info(`Total amount calculated: ${totalAmount}`);
            // Create payment record first WITH SESSION
            const payment = new models_1.Payment({
                orderId: null, // Will be set after order creation
                userId: customerId,
                method: paymentMethod,
                amount: totalAmount,
                status: enums_1.PaymentStatus.PENDING,
                metadata: {
                    deliveryDetails: {
                        estimatedDeliveryTime: '30 minutes',
                        deliveryAddress,
                    },
                },
            });
            const savedPayment = await payment.save({ session });
            // Create order WITH SESSION and link to payment
            const order = new models_1.Order({
                customer: customerId,
                payment: savedPayment._id, // Link to payment
                items: validatedItems,
                totalAmount,
                status: enums_1.OrderStatus.PENDING,
                deliveryAddress,
                paymentMethod,
            });
            const savedOrder = await order.save({ session });
            // Update payment with order ID
            await models_1.Payment.findByIdAndUpdate(savedPayment._id, { orderId: savedOrder._id }, { session });
            // Reserve inventory for all items
            await this.reserveInventoryForOrder(savedOrder._id.toString(), validatedItems, session);
            // Generate QR code
            const qrCode = (0, jwt_utils_1.generateQRPayload)({ orderId: savedOrder._id.toString() });
            const qrCodeExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
            // Update order WITH SESSION
            const updatedOrder = await models_1.Order.findByIdAndUpdate(savedOrder._id, { qrCode, qrCodeExpiresAt }, { new: true, session })
                .populate('customer')
                .populate('deliveryAgent');
            await session.commitTransaction();
            // Initiate payment
            let finalOrder = updatedOrder;
            if (paymentMethod === enums_1.PaymentMethod.WAAFI_PREAUTH) {
                try {
                    await index_1.paymentService.initiatePreauthorization(savedPayment._id.toString(), customer.phone, 
                    // totalAmount, for production
                    0.01, // for testing
                    {
                        estimatedDeliveryTime: '30 minutes',
                        deliveryAddress,
                    });
                    // Payment successful - send success SMS
                    try {
                        await index_1.smsService.sendSms(customer.phone, 'Your order has been created successfully');
                    }
                    catch (smsError) {
                        logger_1.default.error('Failed to send success SMS', {
                            error: smsError.message,
                            orderId: savedOrder._id.toString(),
                            timestamp: new Date().toISOString(),
                        });
                    }
                }
                catch (error) {
                    logger_1.default.error('Payment preauthorization failed', {
                        orderId: savedOrder._id.toString(),
                        error: error.message,
                        errorType: typeof error,
                        errorConstructor: error.constructor?.name,
                        isAppError: error instanceof app_errors_1.AppError,
                        isPaymentError: error instanceof app_errors_1.PaymentError,
                        timestamp: new Date().toISOString(),
                    });
                    // Handle payment failure AFTER successful order creation
                    const failedOrder = await this.handlePostCommitPaymentFailure(savedOrder._id.toString(), validatedItems, error.message);
                    // Use failed order if available, otherwise keep original order
                    finalOrder = failedOrder || updatedOrder;
                    // Send failure SMS
                    try {
                        await index_1.smsService.sendSms(customer.phone, 'Your order could not be processed due to payment failure. Please try again.');
                    }
                    catch (smsError) {
                        logger_1.default.error('Failed to send failure SMS', {
                            error: smsError.message,
                            orderId: savedOrder._id.toString(),
                            timestamp: new Date().toISOString(),
                        });
                    }
                    // Throw the original payment error to preserve the specific error message
                    if (error instanceof app_errors_1.AppError) {
                        // If it's already an AppError (like WaaFiPaymentFailed), throw it directly
                        throw error;
                    }
                    else {
                        // If it's a generic error, wrap it with the original message
                        throw new app_errors_1.PaymentError(error.message || 'Payment preauthorization failed', 400, 'PAYMENT_FAILED', {
                            orderId: savedOrder._id.toString(),
                            details: { originalError: error.message },
                        });
                    }
                }
            }
            else {
                // For non-payment methods, send success SMS
                try {
                    await index_1.smsService.sendSms(customer.phone, 'Your order has been created successfully');
                }
                catch (smsError) {
                    logger_1.default.error('Failed to send SMS', {
                        error: smsError.message,
                        orderId: savedOrder._id.toString(),
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            return finalOrder;
        }
        catch (error) {
            logger_1.default.error('Order creation failed', {
                customerId,
                items,
                deliveryAddress,
                paymentMethod,
                error: error.message,
                errorType: typeof error,
                errorConstructor: error.constructor?.name,
                isAppError: error instanceof app_errors_1.AppError,
                isPaymentError: error instanceof app_errors_1.PaymentError,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            // ENHANCED TRANSACTION FAILURE HANDLING
            try {
                // Check if transaction is still active before aborting
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Transaction aborted successfully', {
                        customerId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    customerId,
                    timestamp: new Date().toISOString(),
                });
            }
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Order creation failed', {
                code: 'ORDER_CREATION_FAILED',
                details: { originalError: error.message },
            });
        }
        finally {
            // SAFE SESSION CLEANUP
            try {
                if (!session.hasEnded) {
                    await session.endSession();
                    logger_1.default.debug('Session ended successfully', {
                        customerId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (sessionError) {
                logger_1.default.error('Failed to end session', {
                    error: sessionError.message,
                    customerId,
                    timestamp: new Date().toISOString(),
                });
            }
        }
    }
    async validateItems(items, options) {
        const validatedItems = [];
        for (const item of items) {
            if (!item.itemType || !item.itemId || !item.quantity) {
                throw new app_errors_1.BadRequestError('Invalid item format', {
                    code: 'INVALID_ITEM_FORMAT',
                    details: { item },
                });
            }
            let itemDoc;
            switch (item.itemType) {
                case order_model_1.OrderItemType.Cylinder:
                    itemDoc = await models_1.Cylinder.findById(item.itemId).session(options?.session || null);
                    break;
                case order_model_1.OrderItemType.SparePart:
                    itemDoc = await models_1.SparePart.findById(item.itemId).session(options?.session || null);
                    break;
                case order_model_1.OrderItemType.Package:
                    itemDoc = await models_1.Package.findById(item.itemId).session(options?.session || null);
                    break;
                // Add cases for other item types
                default:
                    throw new app_errors_1.BadRequestError('Invalid item type', {
                        code: 'INVALID_ITEM_TYPE',
                        details: { itemType: item.itemType },
                    });
            }
            if (!itemDoc) {
                throw new app_errors_1.NotFoundError('Item not found', {
                    code: 'ITEM_NOT_FOUND',
                    details: { itemId: item.itemId, itemType: item.itemType },
                });
            }
            //   if (itemDoc.stock < item.quantity) {
            //     throw new BadRequestError('Insufficient stock', {
            //       code: 'INSUFFICIENT_STOCK',
            //       details: { itemId: item.itemId, itemType: item.itemType, available: itemDoc.stock, requested: item.quantity },
            //     });
            //   }
            validatedItems.push({
                itemType: item.itemType,
                itemId: item.itemId,
                quantity: item.quantity,
                price: itemDoc.price,
            });
        }
        // CRITICAL FIX: Do NOT end the session here - it's still needed for the transaction
        // The session will be ended in the finally block of the calling method
        return validatedItems;
    }
    async assignAgentToOrder(orderId, agentId) {
        try {
            const order = await models_1.Order.findById(orderId);
            if (!order) {
                throw new app_errors_1.NotFoundError('Order not found', {
                    code: 'ORDER_NOT_FOUND',
                    details: {
                        orderId,
                    },
                });
            }
            if (order.status !== enums_1.OrderStatus.PENDING && order.status !== enums_1.OrderStatus.CONFIRMED) {
                // throw new BadRequestError('Order is not pending', {
                throw new app_errors_1.BadRequestError('Order is not pending or confirmed', {
                    code: 'ORDER_NOT_PENDING',
                    details: {
                        orderId,
                        currentStatus: order.status,
                    },
                });
            }
            const agent = await models_1.User.findById(agentId);
            if (!agent) {
                throw new app_errors_1.NotFoundError('Agent not found', {
                    code: 'AGENT_NOT_FOUND',
                    details: {
                        agentId,
                    },
                });
            }
            if (agent.role !== enums_1.UserRole.AGENT) {
                throw new app_errors_1.BadRequestError('User is not an agent', {
                    code: 'USER_NOT_AGENT',
                    details: {
                        agentId,
                        role: agent.role,
                    },
                });
            }
            const updatedOrder = await models_1.Order.findByIdAndUpdate(orderId, { deliveryAgent: agentId, status: enums_1.OrderStatus.OUT_FOR_DELIVERY }, { new: true });
            return updatedOrder;
        }
        catch (error) {
            logger_1.default.error('Failed to assign agent to order', {
                orderId,
                agentId,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to assign agent to order', {
                code: 'ASSIGN_AGENT_FAILED',
                details: { originalError: error.message },
            });
        }
    }
    async cancelOrder(orderId) {
        return this.executeWithRetry(async () => {
            return this.cancelOrderTransaction(orderId);
        });
    }
    async cancelOrderTransaction(orderId) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const order = await models_1.Order.findById(orderId).session(session);
            if (!order) {
                throw new app_errors_1.NotFoundError('Order not found', {
                    code: 'ORDER_NOT_FOUND',
                    details: {
                        orderId,
                    },
                });
            }
            if (order.status !== enums_1.OrderStatus.PENDING && order.status !== enums_1.OrderStatus.OUT_FOR_DELIVERY) {
                throw new app_errors_1.BadRequestError('Order cannot be cancelled', {
                    code: 'ORDER_CANNOT_BE_CANCELLED',
                    details: {
                        orderId,
                        currentStatus: order.status,
                    },
                });
            }
            // Release reserved inventory
            await this.releaseInventoryForOrder(orderId.toString(), order.items, session);
            // Cancel payment
            if (order.paymentMethod === enums_1.PaymentMethod.WAAFI_PREAUTH && order.payment) {
                try {
                    await index_1.paymentService.cancelPreauthorization(order.payment.toString());
                }
                catch (error) {
                    logger_1.default.error('Failed to cancel payment', {
                        orderId,
                        paymentId: order.payment.toString(),
                        error: error.message,
                        timestamp: new Date().toISOString(),
                    });
                    // Don't throw error here - we still want to cancel the order even if payment cancellation fails
                }
            }
            const updatedOrder = await models_1.Order.findByIdAndUpdate(orderId, { status: enums_1.OrderStatus.CANCELLED, cancelledAt: new Date() }, { new: true, session });
            // check updatedOrder payment status
            if (updatedOrder.paymentMethod === enums_1.PaymentMethod.WAAFI_PREAUTH && updatedOrder.payment) {
                const payment = await models_1.Payment.findById(updatedOrder.payment);
                if (!payment) {
                    logger_1.default.warn('Payment not found for cancelled order', {
                        orderId: updatedOrder._id.toString(),
                        paymentId: updatedOrder.payment.toString(),
                        timestamp: new Date().toISOString(),
                    });
                }
                else if (payment.status !== enums_1.PaymentStatus.CANCELLED) {
                    logger_1.default.warn('Payment was not cancelled successfully', {
                        orderId: updatedOrder._id.toString(),
                        paymentId: payment._id.toString(),
                        paymentStatus: payment.status,
                        timestamp: new Date().toISOString(),
                    });
                    // Don't throw error - order cancellation should succeed even if payment cancellation fails
                }
            }
            await session.commitTransaction();
            return updatedOrder;
        }
        catch (error) {
            logger_1.default.error('Failed to cancel order', {
                orderId,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            // ENHANCED TRANSACTION FAILURE HANDLING
            try {
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Cancel order transaction aborted successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort cancel order transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to cancel order', {
                code: 'CANCEL_ORDER_FAILED',
                details: { originalError: error.message },
            });
        }
        finally {
            // SAFE SESSION CLEANUP
            try {
                if (!session.hasEnded) {
                    await session.endSession();
                    logger_1.default.debug('Cancel order session ended successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (sessionError) {
                logger_1.default.error('Failed to end cancel order session', {
                    error: sessionError.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
        }
    }
    // complete or deliver order
    async completeOrder(orderId) {
        return this.executeWithRetry(async () => {
            return this.completeOrderTransaction(orderId);
        });
    }
    async completeOrderTransaction(orderId) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const order = await models_1.Order.findById(orderId).session(session);
            if (!order) {
                throw new app_errors_1.NotFoundError('Order not found', {
                    code: 'ORDER_NOT_FOUND',
                    details: {
                        orderId,
                    },
                });
            }
            if (order.status !== enums_1.OrderStatus.OUT_FOR_DELIVERY) {
                throw new app_errors_1.BadRequestError('Order is not assigned', {
                    code: 'ORDER_NOT_ASSIGNED',
                    details: {
                        orderId,
                        currentStatus: order.status,
                    },
                });
            }
            // Mark inventory items as sold
            await this.markInventoryAsSold(order.items, session);
            // Capture payment
            if (order.paymentMethod === enums_1.PaymentMethod.WAAFI_PREAUTH && order.payment) {
                await index_1.paymentService.capturePreauthorizedPayment(order.payment.toString());
            }
            const updatedOrder = await models_1.Order.findByIdAndUpdate(orderId, { status: enums_1.OrderStatus.DELIVERED, deliveredAt: new Date() }, { new: true, session });
            // check updatedOrder payment status
            if (updatedOrder.paymentMethod === enums_1.PaymentMethod.WAAFI_PREAUTH && updatedOrder.payment) {
                const payment = await models_1.Payment.findById(updatedOrder.payment);
                if (!payment) {
                    throw new app_errors_1.NotFoundError('Payment not found', {
                        code: 'PAYMENT_NOT_FOUND',
                        details: {
                            paymentId: updatedOrder.payment.toString(),
                        },
                    });
                }
                if (payment.status !== enums_1.PaymentStatus.CAPTURED) {
                    throw new app_errors_1.PaymentError(`Payment is not captured, status is ${payment.status}`, 400, 'PAYMENT_NOT_CAPTURED', {
                        paymentId: payment._id.toString(),
                        orderId: payment.orderId.toString(),
                        details: {
                            currentStatus: payment.status,
                        },
                    });
                }
            }
            await session.commitTransaction();
            return updatedOrder;
        }
        catch (error) {
            logger_1.default.error('Failed to complete order', {
                orderId,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            // ENHANCED TRANSACTION FAILURE HANDLING
            try {
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Complete order transaction aborted successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort complete order transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to complete order', {
                code: 'COMPLETE_ORDER_FAILED',
                details: { originalError: error.message },
            });
        }
        finally {
            // SAFE SESSION CLEANUP
            try {
                if (!session.hasEnded) {
                    await session.endSession();
                    logger_1.default.debug('Complete order session ended successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (sessionError) {
                logger_1.default.error('Failed to end complete order session', {
                    error: sessionError.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
        }
    }
    // Validate order in qr code data
    async validateOrderInQRCode(qrCode) {
        try {
            const { success, orderId, expiresAt, message } = (0, jwt_utils_1.validateQRPayload)(qrCode);
            if (!success) {
                throw new app_errors_1.BadRequestError(message, {
                    code: 'INVALID_QR_CODE',
                    details: {
                        message,
                    },
                });
            }
            if (!orderId) {
                throw new app_errors_1.BadRequestError('Missing orderId in token', {
                    code: 'MISSING_ORDER_ID',
                    details: {
                        message,
                    },
                });
            }
            if (expiresAt && expiresAt < new Date()) {
                throw new app_errors_1.BadRequestError('QR code has expired', {
                    code: 'QR_CODE_EXPIRED',
                    details: {
                        expiresAt,
                    },
                });
            }
            const order = await models_1.Order.findById(orderId)
                .populate('customer', 'phone email')
                .populate('deliveryAgent', 'phone email');
            if (!order) {
                throw new app_errors_1.NotFoundError('Order not found', {
                    code: 'ORDER_NOT_FOUND',
                    details: {
                        orderId,
                    },
                });
            }
            return order;
        }
        catch (error) {
            logger_1.default.error('Failed to validate order in QR code', {
                qrCode,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to validate order in QR code', {
                code: 'VALIDATE_QR_CODE_FAILED',
                details: { originalError: error.message },
            });
        }
    }
    // ==================== INVENTORY MANAGEMENT METHODS ====================
    /**
     * Check inventory availability for all items in the order
     * @param items - Validated order items
     * @param session - MongoDB session for transaction
     * @throws {BadRequestError} If any item has insufficient stock
     */
    async checkInventoryAvailability(items, session) {
        for (const item of items) {
            try {
                switch (item.itemType) {
                    case order_model_1.OrderItemType.Cylinder:
                        const cylinderAvailability = await index_1.cylinderService.checkAvailability(item.itemId.toString(), item.quantity);
                        if (!cylinderAvailability.available) {
                            throw new app_errors_1.BadRequestError('Insufficient cylinder stock', {
                                code: 'INSUFFICIENT_CYLINDER_STOCK',
                                details: {
                                    itemId: item.itemId,
                                    requested: item.quantity,
                                    available: cylinderAvailability.availableQuantity,
                                },
                            });
                        }
                        break;
                    case order_model_1.OrderItemType.Package:
                        const packageAvailability = await index_1.packageService.checkPackageAvailability(item.itemId.toString(), item.quantity);
                        if (!packageAvailability.available) {
                            throw new app_errors_1.BadRequestError('Insufficient package stock', {
                                code: 'INSUFFICIENT_PACKAGE_STOCK',
                                details: {
                                    itemId: item.itemId,
                                    requested: item.quantity,
                                    available: packageAvailability.availableQuantity,
                                },
                            });
                        }
                        break;
                    case order_model_1.OrderItemType.SparePart:
                        const sparePartDoc = await models_1.SparePart.findById(item.itemId).session(session);
                        if (!sparePartDoc) {
                            throw new app_errors_1.NotFoundError('Spare part not found', {
                                code: 'SPARE_PART_NOT_FOUND',
                                details: { itemId: item.itemId },
                            });
                        }
                        const availableQuantity = Math.max(0, sparePartDoc.quantity - sparePartDoc.reserved);
                        if (availableQuantity < item.quantity) {
                            throw new app_errors_1.BadRequestError('Insufficient spare part stock', {
                                code: 'INSUFFICIENT_SPARE_PART_STOCK',
                                details: {
                                    itemId: item.itemId,
                                    requested: item.quantity,
                                    available: availableQuantity,
                                },
                            });
                        }
                        break;
                    default:
                        throw new app_errors_1.BadRequestError('Invalid item type for availability check', {
                            code: 'INVALID_ITEM_TYPE',
                            details: { itemType: item.itemType },
                        });
                }
            }
            catch (error) {
                logger_1.default.error('Inventory availability check failed', {
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                });
                throw error;
            }
        }
    }
    /**
     * Reserve inventory for all items in an order
     * @param orderId - The order ID for tracking
     * @param items - Order items to reserve
     * @param session - MongoDB session for transaction
     */
    async reserveInventoryForOrder(orderId, items, session) {
        for (const item of items) {
            try {
                switch (item.itemType) {
                    case order_model_1.OrderItemType.Cylinder:
                        await index_1.cylinderService.reserveCylinder(item.itemId.toString(), item.quantity, session);
                        break;
                    case order_model_1.OrderItemType.Package:
                        await index_1.packageService.reservePackage(item.itemId.toString(), item.quantity, orderId, session);
                        break;
                    case order_model_1.OrderItemType.SparePart:
                        await index_1.sparePartService.reserve(item.itemId.toString(), item.quantity, session);
                        break;
                    default:
                        throw new app_errors_1.BadRequestError('Invalid item type for reservation', {
                            code: 'INVALID_ITEM_TYPE',
                            details: { itemType: item.itemType },
                        });
                }
                logger_1.default.info('Inventory reserved for order item', {
                    orderId,
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                logger_1.default.error('Inventory reservation failed', {
                    orderId,
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                });
                throw error;
            }
        }
    }
    /**
     * Release reserved inventory for all items in an order (when order is cancelled)
     * @param orderId - The order ID for tracking
     * @param items - Order items to release
     * @param session - MongoDB session for transaction
     */
    async releaseInventoryForOrder(orderId, items, session) {
        for (const item of items) {
            try {
                switch (item.itemType) {
                    case order_model_1.OrderItemType.Cylinder:
                        await index_1.cylinderService.releaseReservation(item.itemId.toString(), item.quantity, session);
                        break;
                    case order_model_1.OrderItemType.Package:
                        await index_1.packageService.releaseReservation(item.itemId.toString(), item.quantity, orderId, session);
                        break;
                    case order_model_1.OrderItemType.SparePart:
                        await index_1.sparePartService.release(item.itemId.toString(), item.quantity, session);
                        break;
                    default:
                        throw new app_errors_1.BadRequestError('Invalid item type for release', {
                            code: 'INVALID_ITEM_TYPE',
                            details: { itemType: item.itemType },
                        });
                }
                logger_1.default.info('Inventory reservation released for order item', {
                    orderId,
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                logger_1.default.error('Inventory reservation release failed', {
                    orderId,
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                });
                throw error;
            }
        }
    }
    /**
     * Mark inventory items as sold (when order is completed/delivered)
     * @param items - Order items to mark as sold
     * @param session - MongoDB session for transaction
     */
    async markInventoryAsSold(items, session) {
        for (const item of items) {
            try {
                switch (item.itemType) {
                    case order_model_1.OrderItemType.Cylinder:
                        await index_1.cylinderService.markAsSold(item.itemId.toString(), item.quantity, session);
                        break;
                    case order_model_1.OrderItemType.Package:
                        await index_1.packageService.markPackageAsSold(item.itemId.toString(), item.quantity, session);
                        break;
                    case order_model_1.OrderItemType.SparePart:
                        await index_1.sparePartService.markAsSold(item.itemId.toString(), item.quantity, session);
                        break;
                    default:
                        throw new app_errors_1.BadRequestError('Invalid item type for sale', {
                            code: 'INVALID_ITEM_TYPE',
                            details: { itemType: item.itemType },
                        });
                }
                logger_1.default.info('Inventory marked as sold for order item', {
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                logger_1.default.error('Inventory sale marking failed', {
                    itemType: item.itemType,
                    itemId: item.itemId,
                    quantity: item.quantity,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                });
                throw error;
            }
        }
    }
    /**
     * Handle Payment errors by marking order as failed and releasing inventory
     * @param orderId - The order ID for tracking
     * @param items - Order items to release
     * @param session - MongoDB session for transaction
     */
    async handlePostCommitPaymentFailure(orderId, items, errorMessage) {
        const newSession = await mongoose_1.default.startSession();
        try {
            newSession.startTransaction();
            const failedOrder = await models_1.Order.findByIdAndUpdate(orderId, {
                status: enums_1.OrderStatus.FAILED,
                failedAt: new Date(),
                failureReason: errorMessage,
            }, { new: true, session: newSession });
            await this.releaseInventoryForOrder(orderId, items, newSession);
            await newSession.commitTransaction();
            logger_1.default.info('Payment failure handled successfully', {
                orderId,
                errorMessage,
                timestamp: new Date().toISOString(),
            });
            return failedOrder;
        }
        catch (error) {
            logger_1.default.error('Failed to handle payment failure', {
                orderId,
                error: error.message,
                stack: error.stack,
                originalPaymentError: errorMessage,
                timestamp: new Date().toISOString(),
            });
            // ENHANCED TRANSACTION FAILURE HANDLING
            try {
                if (newSession.inTransaction()) {
                    await newSession.abortTransaction();
                    logger_1.default.info('Payment failure handling transaction aborted successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort payment failure handling transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
            // Return null to indicate failure handling failed
            return null;
        }
        finally {
            // SAFE SESSION CLEANUP
            try {
                if (!newSession.hasEnded) {
                    await newSession.endSession();
                    logger_1.default.debug('Payment failure handling session ended successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (sessionError) {
                logger_1.default.error('Failed to end payment failure handling session', {
                    error: sessionError.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
        }
    }
    async updateOrder(orderId, customerId, items, deliveryAddress, paymentMethod) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const order = await models_1.Order.findById(orderId).session(session);
            if (!order) {
                throw new app_errors_1.NotFoundError('Order not found', {
                    code: 'ORDER_NOT_FOUND',
                    details: {
                        orderId,
                    },
                });
            }
            // Update customer
            if (customerId) {
                const customer = await models_1.User.findById(customerId).session(session);
                if (!customer) {
                    throw new app_errors_1.NotFoundError('Customer not found', {
                        code: 'CUSTOMER_NOT_FOUND',
                        details: {
                            customerId,
                        },
                    });
                }
                order.customer = customer._id.toString();
            }
            // Update items
            if (items) {
                // Validate items and check availability
                const validatedItems = await this.validateItems(items, { session });
                // Check inventory availability for all items
                await this.checkInventoryAvailability(validatedItems, session);
                // Update order items
                // order.items = validatedItems;
                order.items = [
                    ...validatedItems.map(item => ({
                        itemType: item.itemType,
                        itemId: item.itemId.toString(),
                        quantity: item.quantity,
                    })),
                ];
            }
            // Update delivery address
            if (deliveryAddress) {
                order.deliveryAddress = deliveryAddress;
            }
            // Update payment method
            if (paymentMethod) {
                order.paymentMethod = paymentMethod;
            }
            // Save order
            await order.save({ session });
            await session.commitTransaction();
        }
        catch (error) {
            logger_1.default.error('Failed to update order', {
                orderId,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            // ENHANCED TRANSACTION FAILURE HANDLING
            try {
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Update order transaction aborted successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort update order transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to update order', {
                code: 'UPDATE_ORDER_FAILED',
                details: { originalError: error.message },
            });
        }
        finally {
            // SAFE SESSION CLEANUP
            try {
                if (!session.hasEnded) {
                    await session.endSession();
                    logger_1.default.debug('Update order session ended successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (sessionError) {
                logger_1.default.error('Failed to end update order session', {
                    error: sessionError.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
        }
    }
    async deleteOrder(orderId) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const order = await models_1.Order.findById(orderId).session(session);
            if (!order) {
                throw new app_errors_1.NotFoundError('Order not found', {
                    code: 'ORDER_NOT_FOUND',
                    details: {
                        orderId,
                    },
                });
            }
            await models_1.Order.findByIdAndDelete(orderId).session(session);
            await session.commitTransaction();
        }
        catch (error) {
            logger_1.default.error('Failed to delete order', {
                orderId,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            // ENHANCED TRANSACTION FAILURE HANDLING
            try {
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Delete order transaction aborted successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort delete order transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to delete order', {
                code: 'DELETE_ORDER_FAILED',
                details: { originalError: error.message },
            });
        }
        finally {
            // SAFE SESSION CLEANUP
            try {
                if (!session.hasEnded) {
                    await session.endSession();
                    logger_1.default.debug('Delete order session ended successfully', {
                        orderId,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (sessionError) {
                logger_1.default.error('Failed to end delete order session', {
                    error: sessionError.message,
                    orderId,
                    timestamp: new Date().toISOString(),
                });
            }
        }
    }
    /// -----------   Get Orders  ----------- ///
    async getOrders(
    // filters: {
    //   customer?: string | Types.ObjectId;
    //   status?: OrderStatus;
    //   paymentMethod?: PaymentMethod;
    //   deliveryAgent?: string | Types.ObjectId;
    // } = {}
    filters = {}, requestingUser) {
        try {
            // const query: any = {};
            // if (filters.customer) query.customer = filters.customer;
            // if (filters.status) query.status = filters.status;
            // if (filters.paymentMethod) query.paymentMethod = filters.paymentMethod;
            // if (filters.deliveryAgent) query.deliveryAgent = filters.deliveryAgent;
            // const orders = await Order.find(query).populate('customer deliveryAgent');
            const DEFAULT_PAGE = 1;
            const DEFAULT_LIMIT = 20;
            const MAX_LIMIT = 100;
            const page = Math.max(filters.page || DEFAULT_PAGE, 1);
            const limit = Math.min(filters.limit || DEFAULT_LIMIT, MAX_LIMIT);
            const skip = (page - 1) * limit;
            const query = this.buildQuery(filters, requestingUser);
            const orders = await models_1.Order.find(query)
                .populate('customer deliveryAgent')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .lean();
            return orders;
        }
        catch (error) {
            logger_1.default.error('Failed to get orders', {
                filters,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            // Re-throw known errors, wrap unknown errors
            if (error instanceof app_errors_1.AppError) {
                throw error;
            }
            throw new app_errors_1.InternalServerError('Failed to get orders', {
                code: 'GET_ORDERS_FAILED',
                details: { originalError: error.message },
            });
        }
    }
    buildQuery(filters, user) {
        const query = {};
        // Customer restrictions
        if (user.role === enums_1.UserRole.CUSTOMER) {
            const customerId = filters.customer ?? user.userId;
            if (customerId.toString() !== user.userId.toString()) {
                throw new app_errors_1.UnauthorizedError('Customers can only view their own orders');
            }
            query.customer = customerId;
            // Status restriction
            if (filters.status && filters.status === enums_1.OrderStatus.FAILED) {
                throw new app_errors_1.ValidationError('Customers cannot view failed orders', {
                    code: 'INVALID_STATUS_FILTER',
                });
            }
            query.status = filters.status ?? { $nin: [enums_1.OrderStatus.FAILED, enums_1.OrderStatus.CANCELLED] };
        }
        // Agent restrictions
        else if (user.role === enums_1.UserRole.AGENT) {
            const agentId = filters.deliveryAgent ?? user.userId;
            if (agentId.toString() !== user.userId.toString()) {
                throw new app_errors_1.UnauthorizedError('Agents can only view their own assigned orders');
            }
            query.deliveryAgent = agentId;
            const allowedStatuses = [
                enums_1.OrderStatus.CONFIRMED,
                enums_1.OrderStatus.OUT_FOR_DELIVERY,
                enums_1.OrderStatus.DELIVERED,
            ];
            if (filters.status) {
                if (!allowedStatuses.includes(filters.status)) {
                    throw new app_errors_1.ValidationError('Agents can only view CONFIRMED, OUT_FOR_DELIVERY, or DELIVERED orders', {
                        code: 'INVALID_STATUS_FILTER',
                    });
                }
                query.status = filters.status;
            }
            else {
                query.status = { $in: allowedStatuses };
            }
        }
        // Admin
        else if (user.role === enums_1.UserRole.ADMIN) {
            if (filters.customer)
                query.customer = filters.customer;
            if (filters.deliveryAgent)
                query.deliveryAgent = filters.deliveryAgent;
            if (filters.status)
                query.status = filters.status;
        }
        // Common to all roles
        if (filters.paymentMethod) {
            query.paymentMethod = filters.paymentMethod;
        }
        return query;
    }
}
exports.orderService = new OrderService();
//# sourceMappingURL=order.services.js.map