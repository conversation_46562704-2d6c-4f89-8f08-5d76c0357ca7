"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardService = void 0;
const mongoose_1 = require("mongoose");
const models_1 = require("../models");
const enums_1 = require("../enums/enums");
const logger_1 = __importDefault(require("../config/logger"));
const app_errors_1 = require("../errors/app_errors");
/**
 * Dashboard service providing comprehensive analytics and reporting
 * for admin, agent, and user roles
 */
class DashboardService {
    constructor() { }
    /**
     * Get comprehensive admin dashboard data
     * @param dateRange - Optional date range filter
     * @returns Admin dashboard analytics
     */
    async getAdminDashboard(dateRange) {
        try {
            logger_1.default.info('Fetching admin dashboard data', { dateRange });
            const dateFilter = dateRange
                ? {
                    createdAt: {
                        $gte: dateRange.startDate,
                        $lte: dateRange.endDate,
                    },
                }
                : {};
            // Execute all queries in parallel for better performance
            const [orderStats, revenueStats, inventoryStats, userStats, recentOrders, topProducts, agentPerformance, paymentStats,] = await Promise.all([
                this.getOrderStatistics(dateFilter),
                this.getRevenueStatistics(dateFilter),
                this.getInventoryStatistics(),
                this.getUserStatistics(dateFilter),
                this.getRecentOrders(10),
                this.getTopProducts(dateFilter),
                this.getAgentPerformance(dateFilter),
                this.getPaymentStatistics(dateFilter),
            ]);
            return {
                orderStats,
                revenueStats,
                inventoryStats,
                userStats,
                recentOrders,
                topProducts,
                agentPerformance,
                paymentStats,
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch admin dashboard data', {
                error: error.message,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            throw new app_errors_1.InternalServerError('Failed to fetch admin dashboard data');
        }
    }
    /**
     * Get agent dashboard data
     * @param agentId - Agent user ID
     * @param dateRange - Optional date range filter
     * @returns Agent dashboard analytics
     */
    async getAgentDashboard(agentId, dateRange) {
        try {
            logger_1.default.info('Fetching agent dashboard data', { agentId, dateRange });
            // Validate agent exists and has correct role
            const agent = await models_1.User.findById(agentId);
            if (!agent || agent.role !== enums_1.UserRole.AGENT) {
                throw new app_errors_1.NotFoundError('Agent not found or invalid role');
            }
            const dateFilter = dateRange
                ? {
                    createdAt: {
                        $gte: dateRange.startDate,
                        $lte: dateRange.endDate,
                    },
                }
                : {};
            // Execute queries in parallel
            const [assignedOrders, completedOrders, pendingOrders, earnings, recentDeliveries, performanceMetrics,] = await Promise.all([
                this.getAgentOrderCount(agentId, dateFilter),
                this.getAgentCompletedOrders(agentId, dateFilter),
                this.getAgentPendingOrders(agentId),
                this.getAgentEarnings(agentId, dateFilter),
                this.getAgentRecentDeliveries(agentId, 10),
                this.getAgentPerformanceMetrics(agentId, dateFilter),
            ]);
            return {
                agentInfo: {
                    id: agent._id,
                    phone: agent.phone,
                    email: agent.email,
                    isOnDuty: agent.agentMetadata?.isOnDuty || false,
                    rating: agent.agentMetadata?.rating || 0,
                    vehicle: agent.agentMetadata?.vehicle,
                },
                orderStats: {
                    total: assignedOrders,
                    completed: completedOrders,
                    pending: pendingOrders,
                    completionRate: assignedOrders > 0 ? (completedOrders / assignedOrders) * 100 : 0,
                },
                earnings,
                recentDeliveries,
                performanceMetrics,
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch agent dashboard data', {
                error: error.message,
                agentId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            if (error instanceof app_errors_1.NotFoundError)
                throw error;
            throw new app_errors_1.InternalServerError('Failed to fetch agent dashboard data');
        }
    }
    /**
     * Get user/customer dashboard data
     * @param userId - Customer user ID
     * @param dateRange - Optional date range filter
     * @returns Customer dashboard analytics
     */
    async getUserDashboard(userId, dateRange) {
        try {
            logger_1.default.info('Fetching user dashboard data', { userId, dateRange });
            // Validate user exists
            const user = await models_1.User.findById(userId);
            if (!user) {
                throw new app_errors_1.NotFoundError('User not found');
            }
            const dateFilter = dateRange
                ? {
                    createdAt: {
                        $gte: dateRange.startDate,
                        $lte: dateRange.endDate,
                    },
                }
                : {};
            // Execute queries in parallel
            const [orderStats, totalSpent, recentOrders, favoriteProducts, deliveryStats] = await Promise.all([
                this.getUserOrderStatistics(userId, dateFilter),
                this.getUserTotalSpent(userId, dateFilter),
                this.getUserRecentOrders(userId, 5),
                this.getUserFavoriteProducts(userId),
                this.getUserDeliveryStatistics(userId, dateFilter),
            ]);
            return {
                userInfo: {
                    id: user._id,
                    phone: user.phone,
                    email: user.email,
                    addresses: user.addresses,
                    memberSince: user.createdAt,
                },
                orderStats,
                totalSpent,
                recentOrders,
                favoriteProducts,
                deliveryStats,
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch user dashboard data', {
                error: error.message,
                userId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            if (error instanceof app_errors_1.NotFoundError)
                throw error;
            throw new app_errors_1.InternalServerError('Failed to fetch user dashboard data');
        }
    }
    /**
     * Get order statistics for admin dashboard
     */
    async getOrderStatistics(dateFilter) {
        const pipeline = [
            { $match: dateFilter },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$totalAmount' },
                },
            },
        ];
        const results = await models_1.Order.aggregate(pipeline);
        const stats = {
            total: 0,
            pending: 0,
            confirmed: 0,
            outForDelivery: 0,
            delivered: 0,
            cancelled: 0,
            failed: 0,
            totalRevenue: 0,
        };
        results.forEach(result => {
            stats.total += result.count;
            stats.totalRevenue += result.totalAmount;
            switch (result._id) {
                case enums_1.OrderStatus.PENDING:
                    stats.pending = result.count;
                    break;
                case enums_1.OrderStatus.CONFIRMED:
                    stats.confirmed = result.count;
                    break;
                case enums_1.OrderStatus.OUT_FOR_DELIVERY:
                    stats.outForDelivery = result.count;
                    break;
                case enums_1.OrderStatus.DELIVERED:
                    stats.delivered = result.count;
                    break;
                case enums_1.OrderStatus.CANCELLED:
                    stats.cancelled = result.count;
                    break;
                case enums_1.OrderStatus.FAILED:
                    stats.failed = result.count;
                    break;
            }
        });
        return stats;
    }
    /**
     * Get revenue statistics with trends
     */
    async getRevenueStatistics(dateFilter) {
        const pipeline = [
            {
                $match: { ...dateFilter, status: { $in: [enums_1.OrderStatus.DELIVERED, enums_1.OrderStatus.CONFIRMED] } },
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                        day: { $dayOfMonth: '$createdAt' },
                    },
                    dailyRevenue: { $sum: '$totalAmount' },
                    orderCount: { $sum: 1 },
                },
            },
            { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } },
        ];
        const dailyStats = await models_1.Order.aggregate(pipeline);
        const totalRevenue = dailyStats.reduce((sum, day) => sum + day.dailyRevenue, 0);
        const averageDailyRevenue = dailyStats.length > 0 ? totalRevenue / dailyStats.length : 0;
        return {
            totalRevenue,
            averageDailyRevenue,
            dailyStats,
            totalOrders: dailyStats.reduce((sum, day) => sum + day.orderCount, 0),
        };
    }
    /**
     * Get inventory statistics
     */
    async getInventoryStatistics() {
        const [cylinderStats, sparePartStats, packageStats] = await Promise.all([
            this.getCylinderInventoryStats(),
            this.getSparePartInventoryStats(),
            this.getPackageInventoryStats(),
        ]);
        return {
            cylinders: cylinderStats,
            spareParts: sparePartStats,
            packages: packageStats,
        };
    }
    /**
     * Get cylinder inventory statistics
     */
    async getCylinderInventoryStats() {
        const pipeline = [
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalQuantity: { $sum: '$quantity' },
                    totalReserved: { $sum: '$reserved' },
                    totalSold: { $sum: '$sold' },
                    totalValue: { $sum: { $multiply: ['$quantity', '$price'] } },
                },
            },
        ];
        const results = await models_1.Cylinder.aggregate(pipeline);
        const lowStockItems = await models_1.Cylinder.find({
            $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
            status: enums_1.CylinderStatus.Active,
        }).select('type material quantity minimumStockLevel');
        return {
            byStatus: results,
            lowStockItems,
            lowStockCount: lowStockItems.length,
        };
    }
    /**
     * Get spare part inventory statistics
     */
    async getSparePartInventoryStats() {
        const pipeline = [
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalQuantity: { $sum: '$quantity' },
                    totalValue: { $sum: { $multiply: ['$quantity', '$price'] } },
                },
            },
        ];
        const results = await models_1.SparePart.aggregate(pipeline);
        const lowStockItems = await models_1.SparePart.find({
            $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
            status: enums_1.SparePartStatus.AVAILABLE,
        }).select('name category quantity minimumStockLevel');
        return {
            byStatus: results,
            lowStockItems,
            lowStockCount: lowStockItems.length,
        };
    }
    /**
     * Get package inventory statistics
     */
    async getPackageInventoryStats() {
        const pipeline = [
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalQuantity: { $sum: '$quantity' },
                    totalValue: { $sum: { $multiply: ['$quantity', '$price'] } },
                },
            },
        ];
        const results = await models_1.Package.aggregate(pipeline);
        return { byStatus: results };
    }
    /**
     * Get user statistics
     */
    async getUserStatistics(dateFilter) {
        const pipeline = [
            { $match: dateFilter },
            {
                $group: {
                    _id: '$role',
                    count: { $sum: 1 },
                },
            },
        ];
        const results = await models_1.User.aggregate(pipeline);
        const stats = {
            total: 0,
            customers: 0,
            agents: 0,
            admins: 0,
            activeAgents: 0,
        };
        results.forEach(result => {
            stats.total += result.count;
            switch (result._id) {
                case enums_1.UserRole.CUSTOMER:
                    stats.customers = result.count;
                    break;
                case enums_1.UserRole.AGENT:
                    stats.agents = result.count;
                    break;
                case enums_1.UserRole.ADMIN:
                    stats.admins = result.count;
                    break;
            }
        });
        // Get active agents count
        stats.activeAgents = await models_1.User.countDocuments({
            role: enums_1.UserRole.AGENT,
            'agentMetadata.isOnDuty': true,
        });
        return stats;
    }
    /**
     * Get recent orders for admin dashboard
     */
    async getRecentOrders(limit) {
        return await models_1.Order.find()
            .populate('customer', 'phone email')
            .populate('deliveryAgent', 'phone email')
            .sort({ createdAt: -1 })
            .limit(limit)
            .select('customer deliveryAgent totalAmount status createdAt deliveryAddress');
    }
    /**
     * Get top products by sales
     */
    async getTopProducts(dateFilter) {
        const pipeline = [
            { $match: { ...dateFilter, status: enums_1.OrderStatus.DELIVERED } },
            { $unwind: '$items' },
            {
                $group: {
                    _id: {
                        itemType: '$items.itemType',
                        itemId: '$items.itemId',
                    },
                    totalQuantity: { $sum: '$items.quantity' },
                    totalRevenue: { $sum: { $multiply: ['$items.quantity', '$totalAmount'] } },
                    orderCount: { $sum: 1 },
                },
            },
            { $sort: { totalQuantity: -1 } },
            { $limit: 10 },
        ];
        const results = await models_1.Order.aggregate(pipeline);
        // Populate product details
        const populatedResults = await Promise.all(results.map(async (result) => {
            let productDetails = null;
            switch (result._id.itemType) {
                case 'CYLINDER':
                    productDetails = await models_1.Cylinder.findById(result._id.itemId).select('type material price');
                    break;
                case 'SPARE_PART':
                    productDetails = await models_1.SparePart.findById(result._id.itemId).select('name category price');
                    break;
                case 'PACKAGE':
                    productDetails = await models_1.Package.findById(result._id.itemId).select('name description price');
                    break;
            }
            return {
                ...result,
                productDetails,
            };
        }));
        return populatedResults;
    }
    /**
     * Get agent performance metrics
     */
    async getAgentPerformance(dateFilter) {
        const pipeline = [
            { $match: { ...dateFilter, deliveryAgent: { $exists: true } } },
            {
                $group: {
                    _id: '$deliveryAgent',
                    totalOrders: { $sum: 1 },
                    completedOrders: {
                        $sum: { $cond: [{ $eq: ['$status', enums_1.OrderStatus.DELIVERED] }, 1, 0] },
                    },
                    totalRevenue: { $sum: '$totalAmount' },
                    avgDeliveryTime: { $avg: { $subtract: ['$deliveredAt', '$createdAt'] } },
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'agent',
                },
            },
            { $unwind: '$agent' },
            {
                $project: {
                    agentId: '$_id',
                    agentPhone: '$agent.phone',
                    agentRating: '$agent.agentMetadata.rating',
                    totalOrders: 1,
                    completedOrders: 1,
                    completionRate: {
                        $multiply: [{ $divide: ['$completedOrders', '$totalOrders'] }, 100],
                    },
                    totalRevenue: 1,
                    avgDeliveryTime: 1,
                },
            },
            { $sort: { completionRate: -1 } },
        ];
        return await models_1.Order.aggregate(pipeline);
    }
    /**
     * Get payment statistics
     */
    async getPaymentStatistics(dateFilter) {
        const pipeline = [
            { $match: dateFilter },
            {
                $group: {
                    _id: {
                        method: '$method',
                        status: '$status',
                    },
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$amount' },
                },
            },
        ];
        const results = await models_1.Payment.aggregate(pipeline);
        const stats = {
            byMethod: {},
            byStatus: {},
            totalProcessed: 0,
            totalAmount: 0,
        };
        results.forEach(result => {
            const method = result._id.method;
            const status = result._id.status;
            if (!stats.byMethod[method]) {
                stats.byMethod[method] = { count: 0, amount: 0 };
            }
            if (!stats.byStatus[status]) {
                stats.byStatus[status] = { count: 0, amount: 0 };
            }
            stats.byMethod[method].count += result.count;
            stats.byMethod[method].amount += result.totalAmount;
            stats.byStatus[status].count += result.count;
            stats.byStatus[status].amount += result.totalAmount;
            stats.totalProcessed += result.count;
            stats.totalAmount += result.totalAmount;
        });
        return stats;
    }
    // Agent-specific helper methods
    /**
     * Get agent order count
     */
    async getAgentOrderCount(agentId, dateFilter) {
        return await models_1.Order.countDocuments({ deliveryAgent: agentId, ...dateFilter });
    }
    /**
     * Get agent completed orders count
     */
    async getAgentCompletedOrders(agentId, dateFilter) {
        return await models_1.Order.countDocuments({
            deliveryAgent: agentId,
            status: enums_1.OrderStatus.DELIVERED,
            ...dateFilter,
        });
    }
    /**
     * Get agent pending orders count
     */
    async getAgentPendingOrders(agentId) {
        return await models_1.Order.countDocuments({
            deliveryAgent: agentId,
            status: { $in: [enums_1.OrderStatus.CONFIRMED, enums_1.OrderStatus.OUT_FOR_DELIVERY] },
        });
    }
    /**
     * Get agent earnings
     */
    async getAgentEarnings(agentId, dateFilter) {
        const pipeline = [
            {
                $match: {
                    deliveryAgent: new mongoose_1.Types.ObjectId(agentId),
                    status: enums_1.OrderStatus.DELIVERED,
                    ...dateFilter,
                },
            },
            {
                $group: {
                    _id: null,
                    totalEarnings: { $sum: '$totalAmount' },
                    totalOrders: { $sum: 1 },
                    avgOrderValue: { $avg: '$totalAmount' },
                },
            },
        ];
        const result = await models_1.Order.aggregate(pipeline);
        return result[0] || { totalEarnings: 0, totalOrders: 0, avgOrderValue: 0 };
    }
    /**
     * Get agent recent deliveries
     */
    async getAgentRecentDeliveries(agentId, limit) {
        return await models_1.Order.find({
            deliveryAgent: agentId,
            status: enums_1.OrderStatus.DELIVERED,
        })
            .populate('customer', 'phone email')
            .sort({ deliveredAt: -1 })
            .limit(limit)
            .select('customer totalAmount deliveredAt deliveryAddress');
    }
    /**
     * Get agent performance metrics (renamed from getAgentPerformanceMetrics)
     */
    async getAgentPerformanceMetrics(agentId, dateFilter) {
        const pipeline = [
            {
                $match: {
                    deliveryAgent: new mongoose_1.Types.ObjectId(agentId),
                    ...dateFilter,
                },
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    totalOrders: { $sum: 1 },
                    completedOrders: {
                        $sum: { $cond: [{ $eq: ['$status', enums_1.OrderStatus.DELIVERED] }, 1, 0] },
                    },
                    totalRevenue: { $sum: '$totalAmount' },
                },
            },
            { $sort: { '_id.year': 1, '_id.month': 1 } },
        ];
        const monthlyStats = await models_1.Order.aggregate(pipeline);
        // Calculate overall metrics
        const totalOrders = monthlyStats.reduce((sum, month) => sum + month.totalOrders, 0);
        const totalCompleted = monthlyStats.reduce((sum, month) => sum + month.completedOrders, 0);
        const totalRevenue = monthlyStats.reduce((sum, month) => sum + month.totalRevenue, 0);
        return {
            monthlyStats,
            overallMetrics: {
                totalOrders,
                totalCompleted,
                completionRate: totalOrders > 0 ? (totalCompleted / totalOrders) * 100 : 0,
                totalRevenue,
                avgOrderValue: totalCompleted > 0 ? totalRevenue / totalCompleted : 0,
            },
        };
    }
    // User-specific helper methods
    /**
     * Get user order history
     */
    async getUserOrderHistory(userId, dateFilter) {
        return await models_1.Order.find({ customer: userId, ...dateFilter })
            .populate('deliveryAgent', 'phone email')
            .sort({ createdAt: -1 })
            .select('totalAmount status createdAt deliveredAt deliveryAddress items');
    }
    /**
     * Get user order statistics
     */
    async getUserOrderStatistics(userId, dateFilter) {
        const pipeline = [
            { $match: { customer: new mongoose_1.Types.ObjectId(userId), ...dateFilter } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$totalAmount' },
                },
            },
        ];
        const results = await models_1.Order.aggregate(pipeline);
        const stats = {
            total: 0,
            pending: 0,
            confirmed: 0,
            outForDelivery: 0,
            delivered: 0,
            cancelled: 0,
            failed: 0,
        };
        results.forEach(result => {
            stats.total += result.count;
            switch (result._id) {
                case enums_1.OrderStatus.PENDING:
                    stats.pending = result.count;
                    break;
                case enums_1.OrderStatus.CONFIRMED:
                    stats.confirmed = result.count;
                    break;
                case enums_1.OrderStatus.OUT_FOR_DELIVERY:
                    stats.outForDelivery = result.count;
                    break;
                case enums_1.OrderStatus.DELIVERED:
                    stats.delivered = result.count;
                    break;
                case enums_1.OrderStatus.CANCELLED:
                    stats.cancelled = result.count;
                    break;
                case enums_1.OrderStatus.FAILED:
                    stats.failed = result.count;
                    break;
            }
        });
        return stats;
    }
    /**
     * Get user total spent
     */
    async getUserTotalSpent(userId, dateFilter) {
        const pipeline = [
            {
                $match: {
                    customer: new mongoose_1.Types.ObjectId(userId),
                    status: { $in: [enums_1.OrderStatus.DELIVERED, enums_1.OrderStatus.CONFIRMED] },
                    ...dateFilter,
                },
            },
            {
                $group: {
                    _id: null,
                    totalSpent: { $sum: '$totalAmount' },
                    orderCount: { $sum: 1 },
                    avgOrderValue: { $avg: '$totalAmount' },
                },
            },
        ];
        const result = await models_1.Order.aggregate(pipeline);
        return result[0] || { totalSpent: 0, orderCount: 0, avgOrderValue: 0 };
    }
    /**
     * Get user recent orders
     */
    async getUserRecentOrders(userId, limit) {
        return await models_1.Order.find({ customer: userId })
            .populate('deliveryAgent', 'phone email')
            .sort({ createdAt: -1 })
            .limit(limit)
            .select('totalAmount status createdAt deliveryAddress items');
    }
    /**
     * Get user favorite products
     */
    async getUserFavoriteProducts(userId) {
        const pipeline = [
            { $match: { customer: new mongoose_1.Types.ObjectId(userId), status: enums_1.OrderStatus.DELIVERED } },
            { $unwind: '$items' },
            {
                $group: {
                    _id: {
                        itemType: '$items.itemType',
                        itemId: '$items.itemId',
                    },
                    orderCount: { $sum: 1 },
                    totalQuantity: { $sum: '$items.quantity' },
                },
            },
            { $sort: { orderCount: -1 } },
            { $limit: 5 },
        ];
        const results = await models_1.Order.aggregate(pipeline);
        // Populate product details
        const populatedResults = await Promise.all(results.map(async (result) => {
            let productDetails = null;
            switch (result._id.itemType) {
                case 'CYLINDER':
                    productDetails = await models_1.Cylinder.findById(result._id.itemId).select('type material price');
                    break;
                case 'SPARE_PART':
                    productDetails = await models_1.SparePart.findById(result._id.itemId).select('name category price');
                    break;
                case 'PACKAGE':
                    productDetails = await models_1.Package.findById(result._id.itemId).select('name description price');
                    break;
            }
            return {
                ...result,
                productDetails,
            };
        }));
        return populatedResults;
    }
    /**
     * Get user delivery statistics
     */
    async getUserDeliveryStatistics(userId, dateFilter) {
        const pipeline = [
            {
                $match: {
                    customer: new mongoose_1.Types.ObjectId(userId),
                    status: enums_1.OrderStatus.DELIVERED,
                    deliveredAt: { $exists: true },
                    ...dateFilter,
                },
            },
            {
                $group: {
                    _id: null,
                    totalDeliveries: { $sum: 1 },
                    avgDeliveryTime: {
                        $avg: { $subtract: ['$deliveredAt', '$createdAt'] },
                    },
                    fastestDelivery: {
                        $min: { $subtract: ['$deliveredAt', '$createdAt'] },
                    },
                    slowestDelivery: {
                        $max: { $subtract: ['$deliveredAt', '$createdAt'] },
                    },
                },
            },
        ];
        const result = await models_1.Order.aggregate(pipeline);
        const stats = result[0] || {
            totalDeliveries: 0,
            avgDeliveryTime: 0,
            fastestDelivery: 0,
            slowestDelivery: 0,
        };
        // Convert milliseconds to hours for better readability
        return {
            totalDeliveries: stats.totalDeliveries,
            avgDeliveryTimeHours: stats.avgDeliveryTime ? stats.avgDeliveryTime / (1000 * 60 * 60) : 0,
            fastestDeliveryHours: stats.fastestDelivery ? stats.fastestDelivery / (1000 * 60 * 60) : 0,
            slowestDeliveryHours: stats.slowestDelivery ? stats.slowestDelivery / (1000 * 60 * 60) : 0,
        };
    }
}
exports.dashboardService = new DashboardService();
//# sourceMappingURL=dashboard.services.js.map