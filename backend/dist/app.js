"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_2 = require("express");
const helmet_1 = __importDefault(require("helmet"));
const error_handler_middle_ware_1 = require("./middleware/error_handler_middle_ware");
// import cors from "cors";
const response_1 = require("./utils/response");
const routes_1 = __importDefault(require("./routes"));
const app_errors_1 = require("./errors/app_errors");
const logger_interceptor_middleware_1 = require("./middleware/logger_interceptor_middleware");
const mongoose_1 = require("mongoose");
const payment_services_1 = require("./services/payment.services");
const notification_utils_1 = require("./utils/notification_utils");
const app = (0, express_1.default)();
// middleware
app.use((0, express_2.json)());
app.use((0, express_2.urlencoded)({ extended: true }));
// security
app.use((0, helmet_1.default)());
// app.use(cors());
// app.options("*", cors());
/// Logger Interceptor for request and response
app.use(logger_interceptor_middleware_1.loggerInterceptor);
// routes
app.use(routes_1.default);
// test api
app.get('/', (req, res) => {
    (0, response_1.sendResponse)(res, 200, 'success', 'Welcome to Gas System Project Backend');
    return;
});
// /health
app.get('/health', (req, res) => {
    (0, response_1.sendResponse)(res, 200, 'success', 'Service is up and running');
    return;
});
///------------------- Test Endpoints -----------------
app.get('/test-push-notification', async (req, res) => {
    const andriodToken = 'd-sV-LZAR9eeCnlNjxHZiq:APA91bGGDAlAQsFUi8HDi-LSzCcDVoYWzPbYGh3VXnJAJk_jGm3v-r89cfmVhBKysOFXejhpEQufPDBJUappXGOGC-4_3QaecQVA6N0EbHenYshH7MblZ88';
    const testUserId = '684f671dabea9ca7a8c7f3e9';
    const xcodeToken = '806a813a4d09c86c450b575e7b369de7791131703a04fe37cd790706e3fb9090e9b5dde9615a9a7d4e3a071b13493ffe23eb95ade93aed02229c03cdc70ce8b08d47d3c50fe0c23d522be830d7e16ed6';
    const flutterToken = 'cNgLdGtVI0cwgvc2adFxFl:APA91bEpLHmsO_E1e0hLG84usaaeli9AMTBxCPeE5du4M3_8obm4GqEcz4Ix_6W8rS_IG0-2cGEZeskfYy93tbp3EnQ8bS8A-5gO2FQ0kvuNO6PKXn6QA84';
    try {
        let response = await (0, notification_utils_1.sendPushNotification)(flutterToken, 
        // xcodeToken,
        {
            title: 'Test',
            body: 'Test',
            data: {
                test: 'test',
            },
            imageUrl: 'https://cdn-icons-png.flaticon.com/128/3486/3486926.png',
        });
        console.log('response', response);
        (0, response_1.sendResponse)(res, 200, 'success', 'Test notification sent successfully', {
            data: {
                response,
            },
        });
        return;
    }
    catch (error) {
        console.error('Error sending notification', error);
        (0, response_1.sendResponse)(res, 500, 'error', 'Failed to send notification', {
            data: {
                error,
            },
        });
        return;
    }
});
// Test endpoints for payment service
app.post('/test/payment/preauthorize', async (req, res, next) => {
    try {
        // Test data - in a real app, these would come from the request body
        const testData = {
            mobile: req.body.mobile || '613656021', // Somali mobile number without 252
            amount: req.body.amount || 1000, // amount in USD cents or equivalent
            paymentId: req.body.paymentId || new mongoose_1.Types.ObjectId(), // test payment ID
        };
        const result = await payment_services_1.paymentService.initiatePreauthorization(testData.paymentId, testData.mobile, testData.amount, {
            estimatedDeliveryTime: '30 minutes',
            deliveryAddress: '123 Test Street, Mogadishu',
        });
        (0, response_1.sendResponse)(res, 200, 'success', 'Preauthorization initiated successfully', {
            data: {
                payment: result.payment,
                cashierUrl: result.cashierUrl,
                preauthCode: result.preauthCode,
            },
        });
    }
    catch (error) {
        next(error);
    }
});
app.post('/test/payment/cancel/:paymentId', async (req, res, next) => {
    try {
        const paymentId = req.params.paymentId;
        if (!paymentId) {
            (0, response_1.sendResponse)(res, 400, 'error', 'Payment ID is required');
            return;
        }
        const cancelledPayment = await payment_services_1.paymentService.cancelPreauthorization(paymentId);
        (0, response_1.sendResponse)(res, 200, 'success', 'Preauthorization cancelled successfully', {
            data: {
                cancelledPayment,
            },
        });
        return;
    }
    catch (error) {
        next(error);
    }
});
app.post('/test/payment/capture/:paymentId', async (req, res, next) => {
    try {
        const paymentId = req.params.paymentId;
        if (!paymentId) {
            (0, response_1.sendResponse)(res, 400, 'error', 'Payment ID is required');
            return;
        }
        const capturedPayment = await payment_services_1.paymentService.capturePreauthorizedPayment(paymentId);
        (0, response_1.sendResponse)(res, 200, 'success', 'Payment captured successfully', {
            data: {
                capturedPayment,
            },
        });
        return;
    }
    catch (error) {
        next(error);
    }
});
// 404 handler
app.use((req, res, next) => {
    next(new app_errors_1.NotFoundError(`Route not found: ${req.method} ${req.originalUrl}`));
});
// error handler
app.use(error_handler_middle_ware_1.globalErrorHandler);
exports.default = app;
//# sourceMappingURL=app.js.map