"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsType = exports.SmsStatus = exports.NotificationStatus = exports.DeliveryVerificationMethod = exports.PaymentStatus = exports.PaymentMethod = exports.VehicleType = exports.CylinderMaterial = exports.CylinderStatus = exports.CylinderType = exports.SparePartCategory = exports.SparePartStatus = exports.OrderStatus = exports.UserRole = void 0;
// User Roles
var UserRole;
(function (UserRole) {
    UserRole["CUSTOMER"] = "customer";
    UserRole["AGENT"] = "agent";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
// Order Lifecycle
var OrderStatus;
(function (OrderStatus) {
    // Core Flow
    OrderStatus["PENDING"] = "PENDING";
    OrderStatus["CONFIRMED"] = "CONFIRMED";
    OrderStatus["OUT_FOR_DELIVERY"] = "OUT_FOR_DELIVERY";
    OrderStatus["DELIVERED"] = "DELIVERED";
    // Termination States
    OrderStatus["CANCELLED"] = "CANCELLED";
    OrderStatus["FAILED"] = "FAILED";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var SparePartStatus;
(function (SparePartStatus) {
    SparePartStatus["AVAILABLE"] = "AVAILABLE";
    SparePartStatus["LOW_STOCK"] = "LOW_STOCK";
    SparePartStatus["OUT_OF_STOCK"] = "OUT_OF_STOCK";
    SparePartStatus["DISCONTINUED"] = "DISCONTINUED";
})(SparePartStatus || (exports.SparePartStatus = SparePartStatus = {}));
var SparePartCategory;
(function (SparePartCategory) {
    SparePartCategory["VALVE"] = "VALVE";
    SparePartCategory["REGULATOR"] = "REGULATOR";
    SparePartCategory["HOSE"] = "HOSE";
    SparePartCategory["CONNECTOR"] = "CONNECTOR";
    SparePartCategory["GAUGE"] = "GAUGE";
    SparePartCategory["OTHER"] = "OTHER";
})(SparePartCategory || (exports.SparePartCategory = SparePartCategory = {}));
// Cylinder Types
var CylinderType;
(function (CylinderType) {
    CylinderType["SixKg"] = "6KG";
    CylinderType["ThirteenKg"] = "13KG";
    CylinderType["SeventeenKg"] = "17KG";
})(CylinderType || (exports.CylinderType = CylinderType = {}));
var CylinderStatus;
(function (CylinderStatus) {
    CylinderStatus["Active"] = "ACTIVE";
    CylinderStatus["Discontinued"] = "DISCONTINUED";
    CylinderStatus["OutOfStock"] = "OUT_OF_STOCK";
})(CylinderStatus || (exports.CylinderStatus = CylinderStatus = {}));
var CylinderMaterial;
(function (CylinderMaterial) {
    CylinderMaterial["Iron"] = "IRON";
    CylinderMaterial["Plastic"] = "PLASTIC";
})(CylinderMaterial || (exports.CylinderMaterial = CylinderMaterial = {}));
var VehicleType;
(function (VehicleType) {
    VehicleType["BIKE"] = "bike";
    VehicleType["CAR"] = "car";
    VehicleType["MOTORCYCLE"] = "motorcycle";
})(VehicleType || (exports.VehicleType = VehicleType = {}));
// export enum Warehouse { // instead of `warehouse`
//   MAIN = 'ceelasha_warehouse',
//   SECONDARY = 'seybiyaano_warehouse',
//   THIRD = 'towfiiq_warehouse',
//   FOURTH = 'bakaaro_warehouse',
// }
// Payment Methods
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["CASH"] = "cash";
    PaymentMethod["WAAFI_PREAUTH"] = "waafi_preauth";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "PENDING";
    PaymentStatus["PREAUTHORIZED"] = "PREAUTHORIZED";
    PaymentStatus["CAPTURED"] = "CAPTURED";
    PaymentStatus["CANCELLED"] = "CANCELLED";
    PaymentStatus["FAILED"] = "FAILED";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
// Delivery Verification
var DeliveryVerificationMethod;
(function (DeliveryVerificationMethod) {
    DeliveryVerificationMethod["QR_CODE"] = "qr_code";
    DeliveryVerificationMethod["OTP"] = "otp";
    DeliveryVerificationMethod["BIOMETRIC"] = "biometric";
})(DeliveryVerificationMethod || (exports.DeliveryVerificationMethod = DeliveryVerificationMethod = {}));
var NotificationStatus;
(function (NotificationStatus) {
    NotificationStatus["PENDING"] = "pending";
    NotificationStatus["DELIVERED"] = "delivered";
    NotificationStatus["FAILED"] = "failed";
})(NotificationStatus || (exports.NotificationStatus = NotificationStatus = {}));
var SmsStatus;
(function (SmsStatus) {
    SmsStatus["PENDING"] = "pending";
    SmsStatus["SENT"] = "sent";
    SmsStatus["DELIVERED"] = "delivered";
    SmsStatus["FAILED"] = "failed";
    SmsStatus["CANCELLED"] = "cancelled";
})(SmsStatus || (exports.SmsStatus = SmsStatus = {}));
var SmsType;
(function (SmsType) {
    SmsType["GENERAL"] = "general";
    SmsType["OTP"] = "otp";
    SmsType["ORDER_CONFIRMATION"] = "order_confirmation";
    SmsType["DELIVERY_UPDATE"] = "delivery_update";
    SmsType["PROMOTIONAL"] = "promotional";
    SmsType["SYSTEM_ALERT"] = "system_alert";
})(SmsType || (exports.SmsType = SmsType = {}));
//# sourceMappingURL=enums.js.map