"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const mongodb_memory_server_1 = require("mongodb-memory-server");
const order_services_1 = require("../services/order.services");
const models_1 = require("../models");
const enums_1 = require("../enums/enums");
const order_model_1 = require("../models/order.model");
// Mock external services
jest.mock('../services/sms.services', () => ({
    smsService: {
        sendSms: jest.fn().mockResolvedValue(true),
    },
}));
jest.mock('../services/payment.services', () => ({
    paymentService: {
        initiatePreauthorization: jest.fn().mockResolvedValue({
            success: true,
            transactionId: 'test-transaction-id',
        }),
        capturePreauthorizedPayment: jest.fn().mockResolvedValue({
            success: true,
            transactionId: 'test-transaction-id',
        }),
        cancelPreauthorization: jest.fn().mockResolvedValue({
            success: true,
            transactionId: 'test-transaction-id',
        }),
    },
}));
describe('OrderService Transaction Tests', () => {
    let mongoServer;
    beforeAll(async () => {
        mongoServer = await mongodb_memory_server_1.MongoMemoryServer.create();
        const mongoUri = mongoServer.getUri();
        await mongoose_1.default.connect(mongoUri);
    });
    afterAll(async () => {
        await mongoose_1.default.disconnect();
        await mongoServer.stop();
    });
    beforeEach(async () => {
        // Clear all collections
        await models_1.User.deleteMany({});
        await models_1.Order.deleteMany({});
        await models_1.Cylinder.deleteMany({});
        await models_1.Package.deleteMany({});
        await models_1.SparePart.deleteMany({});
        await models_1.Payment.deleteMany({});
    });
    describe('createOrder', () => {
        let customerId;
        let cylinderId;
        let packageId;
        let sparePartId;
        beforeEach(async () => {
            // Create test customer
            const customer = new models_1.User({
                name: 'Test Customer',
                phone: '+252612345678',
                email: '<EMAIL>',
                role: enums_1.UserRole.CUSTOMER,
                isActive: true,
            });
            await customer.save();
            customerId = customer._id.toString();
            // Create test cylinder
            const cylinder = new models_1.Cylinder({
                type: enums_1.CylinderType.SixKg,
                material: enums_1.CylinderMaterial.Iron,
                description: 'Test cylinder for orders',
                price: 100,
                cost: 80,
                quantity: 10,
                reserved: 0,
                sold: 0,
                status: enums_1.CylinderStatus.Active,
                minimumStockLevel: 2,
            });
            await cylinder.save();
            cylinderId = cylinder._id.toString();
            // Create test package
            const packageDoc = new models_1.Package({
                name: 'Test Package',
                description: 'Test package for orders',
                price: 200,
                quantity: 5,
                reserved: 0,
                sold: 0,
                isActive: true,
                minimumStockLevel: 1,
                items: [
                    {
                        itemType: order_model_1.OrderItemType.Cylinder,
                        itemId: cylinderId,
                        quantity: 2,
                    },
                ],
            });
            await packageDoc.save();
            packageId = packageDoc._id.toString();
            // Create test spare part
            const sparePart = new models_1.SparePart({
                name: 'Test Spare Part',
                description: 'Test spare part for orders',
                price: 50,
                stock: 20,
                reserved: 0,
                sold: 0,
                status: enums_1.SparePartStatus.AVAILABLE,
                minimumStockLevel: 5,
            });
            await sparePart.save();
            sparePartId = sparePart._id.toString();
        });
        it('should create order successfully with proper transaction handling', async () => {
            const items = [
                { itemType: order_model_1.OrderItemType.Cylinder, itemId: cylinderId, quantity: 2 },
                { itemType: order_model_1.OrderItemType.Package, itemId: packageId, quantity: 1 },
                { itemType: order_model_1.OrderItemType.SparePart, itemId: sparePartId, quantity: 3 },
            ];
            const order = await order_services_1.orderService.createOrder(customerId, items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH);
            // Verify order creation
            expect(order).toBeDefined();
            expect(order.customer.toString()).toBe(customerId);
            expect(order.status).toBe(enums_1.OrderStatus.PENDING);
            expect(order.items).toHaveLength(3);
            expect(order.totalAmount).toBe(450); // 100*2 + 200*1 + 50*3
            // Verify inventory reservations
            const updatedCylinder = await models_1.Cylinder.findById(cylinderId);
            expect(updatedCylinder?.reserved).toBe(2);
            const updatedPackage = await models_1.Package.findById(packageId);
            expect(updatedPackage?.reserved).toBe(1);
            const updatedSparePart = await models_1.SparePart.findById(sparePartId);
            expect(updatedSparePart?.reserved).toBe(3);
            // Verify payment record
            const payment = await models_1.Payment.findOne({ orderId: order._id });
            expect(payment).toBeDefined();
            expect(payment?.status).toBe(enums_1.PaymentStatus.PENDING);
            expect(payment?.amount).toBe(450);
        });
        it('should handle insufficient stock gracefully', async () => {
            const items = [
                { itemType: order_model_1.OrderItemType.Cylinder, itemId: cylinderId, quantity: 15 }, // More than available (10)
            ];
            await expect(order_services_1.orderService.createOrder(customerId, items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH)).rejects.toThrow('Insufficient cylinder stock');
            // Verify no inventory was reserved
            const cylinder = await models_1.Cylinder.findById(cylinderId);
            expect(cylinder?.reserved).toBe(0);
            // Verify no order was created
            const orders = await models_1.Order.find({ customer: customerId });
            expect(orders).toHaveLength(0);
        });
        it('should handle invalid customer gracefully', async () => {
            const invalidCustomerId = new mongoose_1.default.Types.ObjectId().toString();
            const items = [{ itemType: order_model_1.OrderItemType.Cylinder, itemId: cylinderId, quantity: 1 }];
            await expect(order_services_1.orderService.createOrder(invalidCustomerId, items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH)).rejects.toThrow('Customer not found');
            // Verify no inventory was reserved
            const cylinder = await models_1.Cylinder.findById(cylinderId);
            expect(cylinder?.reserved).toBe(0);
            // Verify no order was created
            const orders = await models_1.Order.find({});
            expect(orders).toHaveLength(0);
        });
        it('should handle duplicate order detection', async () => {
            const items = [{ itemType: order_model_1.OrderItemType.Cylinder, itemId: cylinderId, quantity: 1 }];
            // Create first order
            const firstOrder = await order_services_1.orderService.createOrder(customerId, items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH);
            expect(firstOrder).toBeDefined();
            // Try to create duplicate order
            await expect(order_services_1.orderService.createOrder(customerId, items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH)).rejects.toThrow('Order already exists');
            // Verify only one order exists
            const orders = await models_1.Order.find({ customer: customerId });
            expect(orders).toHaveLength(1);
        });
    });
    describe('cancelOrder', () => {
        let orderId;
        let cylinderId;
        beforeEach(async () => {
            // Create test data
            const customer = new models_1.User({
                name: 'Test Customer',
                phone: '+252612345678',
                email: '<EMAIL>',
                role: enums_1.UserRole.CUSTOMER,
                isActive: true,
            });
            await customer.save();
            const cylinder = new models_1.Cylinder({
                type: enums_1.CylinderType.SixKg,
                material: enums_1.CylinderMaterial.Iron,
                description: 'Test cylinder for orders',
                price: 100,
                cost: 80,
                quantity: 10,
                reserved: 2,
                sold: 0,
                status: enums_1.CylinderStatus.Active,
                minimumStockLevel: 2,
            });
            await cylinder.save();
            cylinderId = cylinder._id.toString();
            // Create order
            const items = [{ itemType: order_model_1.OrderItemType.Cylinder, itemId: cylinderId, quantity: 2 }];
            const order = await order_services_1.orderService.createOrder(customer._id.toString(), items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH);
            orderId = order._id.toString();
        });
        it('should cancel order and release inventory', async () => {
            const cancelledOrder = await order_services_1.orderService.cancelOrder(orderId);
            // Verify order cancellation
            expect(cancelledOrder.status).toBe(enums_1.OrderStatus.CANCELLED);
            expect(cancelledOrder.cancelledAt).toBeDefined();
            // Verify inventory release
            const cylinder = await models_1.Cylinder.findById(cylinderId);
            expect(cylinder?.reserved).toBe(0);
        });
    });
    describe('completeOrder', () => {
        let orderId;
        let cylinderId;
        beforeEach(async () => {
            // Create test data and order
            const customer = new models_1.User({
                name: 'Test Customer',
                phone: '+252612345678',
                email: '<EMAIL>',
                role: enums_1.UserRole.CUSTOMER,
                isActive: true,
            });
            await customer.save();
            const cylinder = new models_1.Cylinder({
                type: enums_1.CylinderType.SixKg,
                material: enums_1.CylinderMaterial.Iron,
                description: 'Test cylinder for orders',
                price: 100,
                cost: 80,
                quantity: 10,
                reserved: 0,
                sold: 0,
                status: enums_1.CylinderStatus.Active,
                minimumStockLevel: 2,
            });
            await cylinder.save();
            cylinderId = cylinder._id.toString();
            const items = [{ itemType: order_model_1.OrderItemType.Cylinder, itemId: cylinderId, quantity: 2 }];
            const order = await order_services_1.orderService.createOrder(customer._id.toString(), items, 'Test Address', enums_1.PaymentMethod.WAAFI_PREAUTH);
            orderId = order._id.toString();
            // Set order to OUT_FOR_DELIVERY status
            await models_1.Order.findByIdAndUpdate(orderId, { status: enums_1.OrderStatus.OUT_FOR_DELIVERY });
        });
        it('should complete order and mark inventory as sold', async () => {
            const completedOrder = await order_services_1.orderService.completeOrder(orderId);
            // Verify order completion
            expect(completedOrder.status).toBe(enums_1.OrderStatus.DELIVERED);
            expect(completedOrder.deliveredAt).toBeDefined();
            // Verify inventory sold
            const cylinder = await models_1.Cylinder.findById(cylinderId);
            expect(cylinder?.reserved).toBe(0);
            expect(cylinder?.sold).toBe(2);
            expect(cylinder?.quantity).toBe(8); // 10 - 2 sold
        });
    });
});
//# sourceMappingURL=order.service.test.js.map